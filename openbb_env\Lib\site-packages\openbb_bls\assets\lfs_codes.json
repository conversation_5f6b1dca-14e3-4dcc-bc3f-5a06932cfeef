{"ln": {"lfst_code": {"00": "Civilian noninstitutional population", "10": "Civilian labor force", "11": "Full time labor force (includes persons working part time for economic reasons, both usually work fu", "12": "Part time labor force (excludes persons working part time for economic reasons)", "13": "Civilian labor force participation rate", "14": "Percent of labor force time lost", "15": "Experienced labor force", "16": "Civilian Labor Force plus discouraged workers", "17": "Civilian Labor Force plus marginally attached workers", "20": "Employed", "21": "Employed full time (includes persons working part time for economic reasons)", "22": "Employed part time (by economic/noneconomic reason)", "23": "Employment-population ratio", "24": "Employed part time (involuntary)", "25": "Employed full time (persons who usually work 35 hours or more)", "26": "Employed part time (persons who usually work less than 35 hours)", "27": "Aggregated totals employed", "28": "Employment-population ratio (Full-time Workers)", "29": "Employment-population ratio (Part-time Workers)", "30": "Unemployed", "31": "Unemployed looking for full-time work", "32": "Unemployed looking for part-time work", "33": "Aggregated totals unemployed", "34": "Unemployed plus discouraged workers", "35": "Unemployed plus marginally attached workers", "36": "Unemployed plus marginally attached workers plus employed part time for economic reasons", "40": "Unemployment rate", "41": "Unemployment rate of the full-time labor force", "42": "Unemployment rate of the part-time labor force", "50": "Not in labor force", "60": "Adjusted employment (CPS employment adjusted to CES concepts)", "70": "Labor Force Flows Employed to Employed", "71": "Labor Force Flows Unemployed to Employed", "72": "Labor Force Flows Not in Labor Force to Employed", "73": "Marginal Inflows to Employed", "74": "Labor Force Flows Employed to Unemployed", "75": "Labor Force Flows Unemployed to Unemployed", "76": "Labor Force Flows Not in Labor Force to Unemployed", "77": "Marginal Inflows to Unemployed", "78": "Labor Force Flows Employed to Not in Labor Force", "79": "Labor Force Flows Unemployed to Not in Labor Force", "80": "Labor Force Flows Not in Labor Force to Not in Labor Force", "81": "Marginal Inflows to Not in Labor Force", "82": "Employed to other Marginal Outflows", "83": "Unemployed to other Marginal Outflows", "84": "Not in Labor Force to other Marginal Outflows", "85": "Total other Marginal Outflows", "86": "Total Employed in previous month", "87": "Total Unemployed in previous month", "88": "Total Not in Labor Force in previous month", "89": "Total Marginal Inflows", "93": "CPS (Household Survey) response rate"}, "absn_code": {"0": null, "1": "Paid absence", "2": "Unpaid absence", "3": "Absence Universe", "4": "Lost-time Universe"}, "activity_code": {"0": null, "3": "Enrolled in School", "4": "Enrolled in High School", "5": "Enrolled in College", "6": "Enrolled in College Full-time", "7": "Enrolled in College Part-time", "8": "Not Enrolled"}, "ages_code": {"00": "16 years and over", "01": "14 years and over", "07": "16 to 17 years", "08": "16 to 19 years", "10": "16 to 24 years", "11": "16 to 64 years", "13": "18 to 19 years", "15": "18 years and over", "17": "20 years and over", "20": "20 to 24 years", "22": "20 to 64 years", "28": "25 years and over", "30": "25 to 29 years", "31": "25 to 34 years", "33": "25 to 54 years", "36": "30 to 34 years", "37": "35 to 39 years", "38": "35 to 44 years", "39": "40 to 44 years", "40": "45 years and over", "41": "45 to 49 years", "42": "45 to 54 years", "44": "50 to 54 years", "45": "55 years and over", "48": "55 to 59 years", "49": "55 to 64 years", "56": "60 to 61 years", "57": "60 to 64 years", "61": "62 to 64 years", "65": "65 years and over", "66": "65 to 69 years", "72": "70 years and over", "73": "70 to 74 years", "78": "75 years and over", "98": "18 to 24 years"}, "cert_code": {"00": null, "01": "Without a certification or license", "02": "With a certification or license", "03": "With a certification, but no license", "04": "With a license"}, "class_code": {"00": null, "01": "Wage and salary workers", "02": "Private wage and salary workers", "03": "Government wage and salary workers", "04": "Federal wage and salary workers", "05": "State wage and salary workers", "06": "Local wage and salary workers", "07": "State and local wage and salary workers", "08": "Self-employed workers, unincorporated", "09": "Unpaid family workers", "10": "All classes of workers (1, 8, and 9)", "11": "Nonagriculture government, self employed, and unpaid family worker (3, 8, and 9 above)", "12": "Self-employed unincorporated, and unpaid family workers (8 and 9)", "13": "Wage and salary and self-employed workers ('paid' workers-- 1 and 8)", "14": "Incorporated self-employed", "15": "Other", "16": "Wage and salary workers, excluding incorporated self employed", "17": "Private wage and salary workers, excluding incorporated self employed", "20": "Self-employed workers (both incorporated and unincorporated)"}, "duration_code": {"000": null, "006": "Less than 5 weeks", "018": "15 weeks and over", "031": "27 weeks and over", "058": "52 weeks and over", "105": "99 weeks and over", "106": "5 to 10 weeks", "107": "5 to 14 weeks", "108": "11 to 14 weeks", "109": "15 to 26 weeks", "110": "27 to 51 weeks", "111": "5 to 6 weeks", "112": "7 to 10 weeks"}, "education_code": {"00": "All educational levels", "10": "Some High School or High School Graduate", "11": "Less than a High School diploma", "12": "Less than 1 year of High School", "16": "4 years of High School, no diploma", "19": "High School graduates, no college", "20": "Some college or associate degree", "21": "Some college, no degree", "25": "Associate degree", "26": "Associate degree, occupational program", "27": "Associate degree, academic program", "30": "Less than a high school diploma (discontinued)", "31": "High school graduates, no college (discontinued)", "32": "Some college, no degree (discontinued)", "33": "College graduates (discontinued)", "34": "Associate degree (discontinued)", "35": "Less than a bachelor's degree (discontinued)", "36": "Some college or associate degree (discontinued)", "37": "Bachelor's degree only (discontinued)", "38": "Advanced degree (discontinued)", "39": "Bachelor's degree and higher (discontinued)", "40": "Bachelor's degree and higher", "41": "Bachelor's degree only", "45": "Advanced degree", "46": "Master's degree", "47": "Professional degree", "48": "Doctoral degree"}, "entr_code": {"0": null, "1": "Reentrants", "2": "New entrants"}, "expr_code": {"0": null, "1": "Experienced", "2": "No previous work experience"}, "hheader_code": {"00": null, "01": "Family heads"}, "hour_code": {"00": null, "01": "1 to 34 hours", "02": "1 to 4 hours", "06": "5 to 14 hours", "10": "15 to 29 hours", "14": "30 to 34 hours", "16": "35 hours and over", "17": "35 to 39 hours", "20": "40 hours", "21": "41 hours and over", "23": "41 to 48 hours", "27": "49 to 59 hours", "29": "60 hours and over"}, "indy_code": {"0000": "All Industries", "0168": "Agriculture and related industries", "0169": "Agriculture, forestry, fishing, and hunting", "0170": "Crop production", "0180": "Animal production and aquaculture", "0188": "Nonfarm Industries", "0190": "Forestry, except logging", "0270": "Logging", "0280": "Fishing, hunting, and trapping", "0290": "Support activities for agriculture and forestry", "0368": "Nonagriculture industries", "0369": "Mining, quarrying, and oil and gas extraction", "0370": "Oil and gas extraction", "0380": "Coal mining", "0390": "Metal ore mining", "0470": "Nonmetallic mineral mining and quarrying", "0480": "Not specified type of mining", "0490": "Support activities for mining", "0569": "Utilities", "0570": "Electric power generation, transmission, and distribution", "0580": "Natural gas distribution", "0590": "Electric and gas, and other combinations", "0670": "Water, steam, air-conditioning, and irrigation systems", "0680": "Sewage treatment facilities", "0690": "Not specified utilities", "0770": "Construction", "1068": "Nondurable goods manufacturing", "1069": "Food manufacturing", "1070": "Animal food, grain, and oilseed milling", "1080": "Sugar and confectionery products", "1090": "Fruit and vegetable preserving and specialty food manufacturing", "1170": "Dairy product manufacturing", "1180": "Animal slaughtering and processing", "1190": "Retail bakeries", "1270": "Bakeries and tortilla manufacturing, except retail bakeries", "1280": "Seafood and other miscellaneous foods, n.e.c.", "1290": "Not specified food industries", "1369": "Beverage and tobacco products", "1370": "Beverage manufacturing", "1390": "Tobacco manufacturing", "1469": "Textiles, apparel, and leather", "1470": "Fiber, yarn, and thread mills", "1480": "Fabric mills, except knitting", "1490": "Textile and fabric finishing and coating mills", "1570": "Carpet and rug mills", "1590": "Textile product mills, except carpets and rugs", "1670": "Knitting mills", "1680": "Cut and sew apparel manufacturing", "1690": "Apparel accessories and other apparel manufacturing", "1691": "Cut and sew, and apparel accessories and other apparel manufacturing", "1770": "Footwear manufacturing", "1790": "Leather tanning and products, except footwear manufacturing", "1869": "Paper and printing", "1870": "Pulp, paper, and paperboard mills", "1880": "Paperboard container manufacturing", "1890": "Miscellaneous paper and pulp products", "1990": "Printing and related support activities", "2069": "Petroleum and coal products", "2070": "Petroleum refining", "2090": "Miscellaneous petroleum and coal products", "2169": "Chemicals", "2170": "Resin, synthetic rubber and fibers, and filaments manufacturing", "2180": "Agricultural chemical manufacturing", "2190": "Pharmaceutical and medicine manufacturing", "2270": "Paint, coating, and adhesive manufacturing", "2280": "Soap, cleaning compound, and cosmetics manufacturing", "2290": "Industrial and miscellaneous chemicals", "2369": "Plastics and rubber products", "2370": "Plastics product manufacturing", "2380": "Tire manufacturing", "2390": "Rubber product, except tire, manufacturing", "2467": "Manufacturing", "2468": "Durable goods manufacturing", "2469": "Nonmetallic mineral products", "2470": "Pottery, ceramics, and related product manufacturing", "2480": "Clay building material and refractories manufacturing", "2490": "Glass and glass product manufacturing", "2570": "Cement, concrete, lime, and gypsum product manufacturing", "2590": "Miscellaneous nonmetallic mineral product manufacturing", "2669": "Primary metals and fabricated metal  products", "2670": "Iron and steel mills and steel product manufacturing", "2680": "Aluminum production and processing", "2690": "Nonferrous metal, except aluminum, production and processing", "2770": "Foundries", "2780": "Metal forgings and stampings", "2790": "Cutlery and hand tool manufacturing", "2870": "Structural metals and tank and shipping container manufacturing", "2880": "Machine shops; turned product; screw, nut, and bolt manufacturing", "2890": "Coating, engraving, heat treating and allied activities", "2970": "Ordnance", "2980": "Miscellaneous fabricated metal product manufacturing", "2990": "Not specified metal industries", "3069": "Machinery manufacturing", "3070": "Agricultural implement manufacturing", "3080": "Construction, mining, and oil field machinery manufacturing", "3090": "Commercial and service industry machinery manufacturing", "3095": "Commercial and service industry machinery manufacturing", "3170": "Metalworking machinery manufacturing", "3180": "Engine, turbine, and power transmission equipment manufacturing", "3190": "Machinery manufacturing, n.e.c.", "3290": "Not specified machinery manufacturing", "3291": "Machinery manufacturing, n.e.c. or not specified", "3359": "Computers and electronic products", "3360": "Computer and peripheral equipment manufacturing", "3365": "Computer and peripheral equipment manufacturing", "3370": "Communications, audio, and video equipment manufacturing", "3380": "Navigational, measuring, electromedical, and control instruments manufacturing", "3390": "Electronic component and product manufacturing, n.e.c.", "3469": "Electrical equipment and appliances", "3470": "Household appliance manufacturing", "3490": "Electrical lighting, equipment, and supplies manufacturing, n.e.c.", "3569": "Transportation equipment", "3570": "Motor vehicles and motor vehicle equipment manufacturing", "3580": "Aircraft and parts manufacturing", "3590": "Aerospace product and parts manufacturing", "3670": "Railroad rolling stock manufacturing", "3680": "Ship and boat building", "3690": "Other transportation equipment manufacturing", "3769": "Wood products", "3770": "Sawmills and wood preservation", "3780": "Veneer, plywood, and engineered wood products", "3790": "Prefabricated wood buildings and mobile homes", "3870": "Miscellaneous wood products", "3875": "Miscellaneous wood products", "3890": "Furniture and related product manufacturing", "3895": "Furniture and related product manufacturing", "3959": "Miscellaneous manufacturing", "3960": "Medical equipment and supplies manufacturing", "3970": "Toys, amusement, and sporting goods manufacturing", "3980": "Miscellaneous manufacturing, n.e.c.", "3990": "Not specified manufacturing industries", "4067": "Wholesale and retail trade", "4068": "Wholesale trade", "4069": "Durable goods", "4070": "Motor vehicle and motor vehicle parts and supplies merchant wholesalers", "4080": "Furniture and home furnishing, merchant wholesalers", "4090": "Lumber and other construction materials, merchant wholesalers", "4170": "Professional and commercial equipment and supplies, merchant wholesalers", "4180": "Metals and minerals, except petroleum, merchant wholesalers", "4190": "Electrical goods, merchant wholesalers", "4195": "Household appliances and electrical and electronic goods merchant wholesalers", "4260": "Hardware, plumbing and heating equipment, and supplies, merchant wholesalers", "4265": "Hardware, plumbing and heating equipment, and supplies, merchant wholesalers", "4270": "Machinery, equipment, and supplies, merchant wholesalers", "4280": "Recyclable material, merchant wholesalers", "4290": "Miscellaneous durable goods, merchant wholesalers", "4369": "Nondurable goods", "4370": "Paper and paper products, merchant wholesalers", "4380": "Drugs, sundries, and chemical and allied products, merchant wholesalers", "4390": "Apparel, piece goods, and notions merchant wholesalers", "4470": "Grocery and related product merchant wholesalers", "4480": "Farm product raw material merchant wholesalers", "4490": "Petroleum and petroleum products, merchant wholesalers", "4560": "Alcoholic beverages, merchant wholesalers", "4570": "Farm supplies, merchant wholesalers", "4580": "Miscellaneous nondurable goods, merchant wholesalers", "4585": "Wholesale electronic markets, agents and brokers", "4590": "Not specified wholesale trade", "4669": "Retail trade", "4670": "Automobile dealers", "4680": "Other motor vehicle dealers", "4690": "Automotive parts, accessories, and tire stores", "4770": "Furniture and home furnishings stores", "4780": "Household appliance stores", "4790": "Radio, TV, and computer stores", "4795": "Electronics stores", "4870": "Building material and supplies dealers", "4880": "Hardware stores", "4890": "Lawn and garden equipment and supplies stores", "4970": "Grocery stores", "4971": "Supermarkets and other grocery (except convenience) stores", "4972": "Convenience stores", "4980": "Specialty food stores", "4990": "Beer, wine, and liquor stores", "5070": "Pharmacies and drug stores", "5080": "Health and personal care, except drug, stores", "5090": "Gasoline stations", "5170": "Clothing and accessories, except shoe, stores", "5180": "Shoe stores", "5190": "Jewelry, luggage, and leather goods stores", "5270": "Sporting goods, camera, and hobby and toy stores", "5275": "Sporting goods, and hobby and toy stores", "5280": "Sewing, needlework, and piece goods stores", "5290": "Music stores", "5295": "Musical instrument and supplies stores", "5370": "Book stores and news dealers", "5380": "Department stores and discount stores", "5381": "Department stores", "5390": "Miscellaneous general merchandise stores", "5391": "General merchandise stores, including warehouse clubs and supercenters", "5470": "Florists", "5480": "Office supplies and stationery stores", "5490": "Used merchandise stores", "5570": "Gift, novelty, and souvenir shops", "5580": "Miscellaneous retail stores", "5590": "Electronic shopping", "5591": "Electronic auctions", "5592": "Mail-order houses", "5593": "Electronic shopping and mail-order houses", "5670": "Vending machine operators", "5680": "Fuel dealers", "5690": "Other direct selling establishments", "5790": "Not specified retail trade", "6068": "Transportation and utilities", "6069": "Transportation and warehousing", "6070": "Air transportation", "6080": "Rail transportation", "6090": "Water transportation", "6170": "Truck transportation", "6180": "Bus service and urban transit", "6190": "Taxi and limousine service", "6270": "Pipeline transportation", "6280": "Scenic and sightseeing transportation", "6290": "Services incidental to transportation", "6370": "Postal Service", "6380": "Couriers and messengers", "6390": "Warehousing and storage", "6468": "Information", "6469": "Publishing, except Internet", "6470": "Newspaper publishers", "6480": "Periodical, book, and directory publishers", "6490": "Software publishers", "6569": "Motion pictures and sound recording industries", "6570": "Motion pictures and video industries", "6590": "Sound recording industries", "6670": "Broadcasting (except internet)", "6672": "Internet publishing and broadcasting and web search portals", "6675": "Internet publishing and broadcasting", "6679": "Telecommunications", "6680": "Wired telecommunications carriers", "6690": "Telecommunications, except wired telecommunications carriers", "6691": "Internet service providers and data processing services", "6692": "Internet service providers", "6695": "Data processing, hosting, and related services", "6769": "Libraries, archives, and other information services", "6770": "Libraries and archives", "6780": "Other information services", "6867": "Financial activities", "6868": "Finance and insurance", "6869": "Finance", "6870": "Banking and related activities", "6880": "Savings institutions, including credit unions", "6890": "Nondepository credit and related activities", "6970": "Securities, commodities, funds, trusts, and other financial investments", "6990": "Insurance carriers and related activities", "6991": "Insurance carriers", "6992": "Agencies, brokerages, and other insurance related activities", "7069": "Real estate and rental and leasing", "7070": "Real estate", "7071": "Lessors of real estate, and offices of real estate agents and brokers", "7072": "Real estate property managers, offices of real estate appraisers, and other activities related to re", "7079": "Rental and leasing services", "7080": "Automotive equipment rental and leasing", "7170": "Video tape and disk rental", "7180": "Other consumer goods rental", "7181": "Other consumer goods rental", "7190": "Commercial, industrial, and other intangible assets rental and leasing", "7268": "Professional and business services", "7269": "Professional and technical services", "7270": "Legal services", "7280": "Accounting, tax preparation, bookkeeping, and payroll services", "7290": "Architectural, engineering, and related services", "7370": "Specialized design services", "7380": "Computer systems design and related services", "7390": "Management, scientific, and technical consulting services", "7460": "Scientific research and development services", "7470": "Advertising, public relations, and related services", "7480": "Veterinary services", "7490": "Other professional, scientific, and technical services", "7569": "Management, administrative, and waste services", "7570": "Management of companies and enterprises", "7579": "Administrative and support services", "7580": "Employment services", "7590": "Business support services", "7670": "Travel arrangement and reservation services", "7680": "Investigation and security services", "7690": "Services to buildings and dwellings", "7770": "Landscaping services", "7780": "Other administrative and other support services", "7790": "Waste management and remediation services", "7858": "Education and health services", "7859": "Educational services", "7860": "Elementary and secondary schools", "7870": "Colleges, universities, and professional schools, including junior colleges", "7880": "Business, technical, and trade schools and training", "7890": "Other schools, instruction, and educational services", "7968": "Health care and social assistance", "7969": "Health service, except hospitals", "7970": "Offices of physicians", "7980": "Offices of dentists", "7990": "Offices of chiropractors", "8070": "Offices of optometrists", "8080": "Offices of other health practitioners", "8090": "Outpatient care centers", "8170": "Home health care services", "8180": "Other health care services", "8190": "Hospitals", "8191": "General medical and surgical hospitals, and specialty (except psychiatric and substance abuse) hospi", "8192": "Psychiatric and substance abuse hospitals", "8270": "Nursing care facilities (skilled nursing facilities)", "8290": "Residential care facilities, except skilled nursing facilities", "8369": "Social assistance", "8370": "Individual and family services", "8380": "Community food and housing, and emergency services", "8390": "Vocational rehabilitation services", "8470": "Child day care services", "8558": "Leisure and hospitality", "8559": "Arts, entertainment, and recreation", "8560": "Performing arts, spectator sports, and related industries", "8561": "Performing arts companies", "8562": "Spectator sports", "8563": "Promoters of performing arts, sports, and similar events, agents and managers for artists, athletes,", "8564": "Independent artists, writers, and performers", "8570": "Museums, art galleries, historical sites, and similar institutions", "8580": "Bowling centers", "8590": "Other amusement, gambling, and recreation industries", "8658": "Accommodation and food services", "8659": "Accommodation", "8660": "Traveler accommodation", "8670": "Recreational vehicle parks and camps, and rooming and boarding houses, dormitories, and workers' cam", "8679": "Food services and drinking places", "8680": "Restaurants and other food services", "8690": "Drinking places, alcoholic beverages", "8767": "Other services", "8768": "Other services, except private households", "8769": "Repair and maintenance", "8770": "Automotive repair and maintenance", "8780": "Car washes", "8790": "Electronic and precision equipment repair and maintenance", "8870": "Commercial and industrial machinery and equipment repair and maintenance", "8880": "Personal and household goods repair and maintenance", "8890": "Footwear and leather goods repair", "8891": "Personal and household goods repair and maintenance", "8969": "Personal and laundry services", "8970": "Barber shops", "8980": "Beauty salons", "8990": "Nail salons and other personal care services", "9070": "Dry cleaning and laundry services", "9080": "Funeral homes, cemeteries, and crematories", "9090": "Other personal services", "9159": "Membership associations and organizations", "9160": "Religious organizations", "9170": "Civic, social, advocacy organizations, and grant making and giving services", "9180": "Labor unions", "9190": "Business, professional, political, and similar organizations", "9290": "Other services, private households", "9369": "Public administration", "9370": "Executive offices and legislative bodies", "9380": "Public finance activities", "9390": "Other general government and support", "9470": "Justice, public order, and safety activities", "9480": "Administration of human resource programs", "9490": "Administration of environmental quality and housing programs", "9570": "Administration of economic programs and space research", "9590": "National security and international affairs", "9600": "Other industries", "9890": "Armed Forces"}, "jdes_code": {"0": null, "1": "Want a job now", "2": "Don't want a job now"}, "look_code": {"00": null, "01": "Job losers and persons who completed temporary jobs", "02": "Total on layoff", "03": "Temporary layoff", "04": "Indefinite layoff", "05": "All other job losers", "06": "Quit job (job leavers)", "07": "Other reasons (than 1 and 6)", "08": "Left school", "09": "Want temporary work", "10": "Other reasons (than 8 and 9) left last job", "12": "Personal, family, or school", "13": "Ill health, disability", "14": "Retirement, old age", "15": "Seasonal job completed", "16": "Slack work or business conditions", "17": "Temporary job completed (nonseasonal)", "18": "Unsatisfactory work arrangements", "19": "Reasons (other than 12,13,14,15,16,17,and 18)", "20": "Total economic reasons (15, 16, and 17)", "21": "Other reasons (other than 12,13,14,15,16,and 17)", "31": "Aggregated total reasons", "40": "Job losers (new code for 1994)", "41": "Permanent job losers"}, "mari_code": {"00": null, "01": "Never married", "02": "Married, spouse present", "03": "Civilian spouse present", "05": "Separated (including married, spouse absent)", "06": "Widowed", "07": "Divorced", "08": "Widowed and divorced", "09": "Widowed, divorced, and separated (including married, spouse absent)", "10": "Married (codes 2 and 5)", "11": "Never married, widowed, divorced, and separated (including married, spouse absent)"}, "mjhs_code": {"00": null, "01": "Multiple job holders", "02": "Multiple job holders, primary job full time, secondary job part time", "03": "Multiple job holders, primary and secondary job both part time", "04": "Multiple job holders, primary and secondary job both full time", "05": "Multiple job holders, hours vary on primary or secondary job"}, "occupation_code": {"0000": "All Occupations", "0007": "Management, professional and related occupations", "0008": "Management, business, and financial operations occupations", "0009": "Management occupations", "0010": "Chief executives", "0020": "General and operations managers", "0030": "Legislators", "0040": "Advertising and promotions managers", "0050": "Marketing and sales managers", "0051": "Marketing managers", "0052": "Sales managers", "0060": "Public relations and fundraising managers", "0100": "Administrative services managers", "0101": "Administrative services managers", "0102": "Facilities managers", "0110": "Computer and information systems managers", "0120": "Financial managers", "0130": "Human resources managers", "0135": "Compensation and benefits managers", "0136": "Human resources managers", "0137": "Training and development managers", "0140": "Industrial production managers", "0150": "Purchasing managers", "0160": "Transportation, storage, and distribution managers", "0200": "Farm, ranch, and other agricultural managers", "0205": "Farmers, ranchers, and other agricultural managers", "0210": "Farmers and ranchers", "0220": "Construction managers", "0230": "Education and childcare administrators", "0300": "Architectural and engineering managers", "0310": "Food service managers", "0320": "Funeral directors", "0325": "Funeral home managers", "0330": "Gaming managers", "0335": "Entertainment and recreation managers", "0340": "Lodging managers", "0350": "Medical and health services managers", "0360": "Natural sciences managers", "0400": "Postmasters and mail superintendents", "0410": "Property, real estate, and community association managers", "0420": "Social and community service managers", "0425": "Emergency management directors", "0426": "Personal service managers, all other", "0430": "Managers, all other", "0440": "Managers, all other", "0499": "Business and financial operations occupations", "0500": "Agents and business managers of artists, performers, and athletes", "0510": "Buyers and purchasing agents, farm products", "0520": "Wholesale and retail buyers, except farm products", "0530": "Purchasing agents, except wholesale, retail, and farm products", "0540": "Claims adjusters, appraisers, examiners, and investigators", "0560": "Compliance officers, except agri, construction, health & safety, and transportation", "0565": "Compliance officers", "0600": "Cost estimators", "0620": "Human resources, training, and labor relations specialists", "0630": "Human resources workers", "0640": "Compensation, benefits, and job analysis specialists", "0650": "Training and development specialists", "0700": "Logisticians", "0705": "Project management specialists", "0710": "Management analysts", "0720": "Meeting and convention planners", "0725": "Meeting, convention, and event planners", "0726": "Fundraisers", "0730": "Other business operations specialists", "0735": "Market research analysts and marketing specialists", "0740": "Business operations specialists, all other", "0750": "Business operations specialists, all other", "0800": "Accountants and auditors", "0810": "Property appraisers and assessors", "0820": "Budget analysts", "0830": "Credit analysts", "0840": "Financial analysts", "0845": "Financial and investment analysts", "0850": "Personal financial advisors", "0860": "Insurance underwriters", "0900": "Financial examiners", "0910": "Credit counselors and loan officers", "0930": "Tax examiners and collectors, and revenue agents", "0940": "Tax preparers", "0950": "Financial specialists, all other", "0960": "Other financial specialists", "0998": "Professional and related occupations", "0999": "Computer and mathematical occupations", "1000": "Computer scientists and systems analysts", "1005": "Computer and information research scientists", "1006": "Computer systems analysts", "1007": "Information security analysts", "1010": "Computer programmers", "1020": "Software developers, applications and systems software", "1021": "Software developers", "1022": "Software quality assurance analysts and testers", "1030": "Web developers", "1031": "Web developers", "1032": "Web or digital interface designers", "1040": "Computer support specialists", "1050": "Computer support specialists", "1060": "Database administrators", "1065": "Database administrators and architects", "1100": "Network and computer systems administrators", "1105": "Network and computer systems administrators", "1106": "Computer network architects", "1107": "Computer occupations, all other", "1108": "Computer occupations, all other", "1110": "Network systems and data communications analysts", "1200": "Actuaries", "1210": "Mathematicians", "1220": "Operations research analysts", "1230": "Statisticians", "1240": "Other mathematical science occupations", "1299": "Architecture and engineering occupations", "1300": "Architects, except naval", "1305": "Architects, except landscape and naval", "1306": "Landscape architects", "1310": "Surveyors, cartographers, and photogrammetrists", "1320": "Aerospace engineers", "1330": "Agricultural engineers", "1340": "Biomedical engineers", "1350": "Chemical engineers", "1360": "Civil engineers", "1400": "Computer hardware engineers", "1410": "Electrical and electronics engineers", "1420": "Environmental engineers", "1430": "Industrial engineers, including health and safety", "1440": "Marine engineers and naval architects", "1450": "Materials engineers", "1460": "Mechanical engineers", "1500": "Mining and geological engineers, including mining safety engineers", "1510": "Nuclear engineers", "1520": "Petroleum engineers", "1530": "Engineers, all other", "1540": "Drafters", "1541": "Architectural and civil drafters", "1545": "Other drafters", "1550": "Engineering technicians, except drafters", "1551": "Electrical and electronic engineering technologists and technicians", "1555": "Other engineering technologists and technicians, except drafters", "1560": "Surveying and mapping technicians", "1599": "Life, physical, and social science occupations", "1600": "Agricultural and food scientists", "1610": "Biological scientists", "1640": "Conservation scientists and foresters", "1650": "Medical scientists", "1660": "Life scientists, all other", "1700": "Astronomers and physicists", "1710": "Atmospheric and space scientists", "1720": "Chemists and materials scientists", "1740": "Environmental scientists and geoscientists", "1745": "Environmental scientists and specialists, including health", "1750": "Geoscientists and hydrologists, except geographers", "1760": "Physical scientists, all other", "1800": "Economists", "1810": "Market and survey researchers", "1815": "Survey researchers", "1820": "Psychologists", "1821": "Clinical and counseling psychologists", "1822": "School psychologists", "1825": "Other psychologists", "1830": "Sociologists", "1840": "Urban and regional planners", "1860": "Miscellaneous social scientists and related workers", "1900": "Agricultural and food science technicians", "1910": "Biological technicians", "1920": "Chemical technicians", "1930": "Geological and petroleum technicians", "1935": "Geoscience and environmental science technicians", "1940": "Nuclear technicians", "1950": "Social science research assistants", "1960": "Other life, physical, and social science technicians", "1965": "Miscellaneous life, physical, and social science technicians", "1970": "Other life, physical, and social science technicians", "1980": "Occupational health and safety specialists and technicians", "1999": "Community and social service occupations", "2000": "Counselors", "2001": "Substance abuse and behavioral disorder counselors", "2002": "Educational, guidance, and career counselors and advisors", "2003": "Marriage and family therapists", "2004": "Mental health counselors", "2005": "Rehabilitation counselors", "2006": "Counselors, all other", "2010": "Social workers", "2011": "Child, family, and school social workers", "2012": "Healthcare social workers", "2013": "Mental health and substance abuse social workers", "2014": "Social workers, all other", "2015": "Probation officers and correctional treatment specialists", "2016": "Social and human service assistants", "2020": "Miscellaneous community and social service specialists", "2025": "Other community and social service specialists", "2040": "Clergy", "2050": "Directors, religious activities and education", "2060": "Religious workers, all other", "2099": "Legal occupations", "2100": "Lawyers", "2105": "Judicial law clerks", "2110": "Judges, magistrates, and other judicial workers", "2140": "Paralegals and legal assistants", "2145": "Paralegals and legal assistants", "2150": "Miscellaneous legal support workers", "2160": "Miscellaneous legal support workers", "2170": "Title examiners, abstractors, and searchers", "2180": "Legal support workers, all other", "2199": "Education, training, and library occupations", "2200": "Postsecondary teachers", "2205": "Postsecondary teachers", "2300": "Preschool and kindergarten teachers", "2310": "Elementary and middle school teachers", "2320": "Secondary school teachers", "2330": "Special education teachers", "2340": "Other teachers and instructors", "2350": "Tutors", "2360": "Other teachers and instructors", "2400": "Archivists, curators, and museum technicians", "2430": "Librarians", "2435": "Librarians and media collections specialists", "2440": "Library technicians", "2540": "Teacher assistants", "2545": "Teacher assistants", "2550": "Other education, training, and library workers", "2555": "Other educational instruction and library workers", "2599": "Arts, design, entertainment, sports, and media occupations", "2600": "Artists and related workers", "2630": "Designers", "2631": "Commercial and industrial designers", "2632": "Fashion designers", "2633": "Floral designers", "2634": "Graphic designers", "2635": "Interior designers", "2636": "Merchandise displayers and window trimmers", "2640": "Other designers", "2700": "Actors", "2710": "Producers and directors", "2720": "Athletes, coaches, umpires, and related workers", "2721": "Athletes and sports competitors", "2722": "Coaches and scouts", "2723": "Umpires, referees, and other sports officials", "2740": "Dancers and choreographers", "2750": "Musicians, singers, and related workers", "2751": "Music directors and composers", "2752": "Musicians and singers", "2755": "Disc jockeys, except radio disc jockeys", "2760": "Entertainers and performers, sports and related workers, all other", "2770": "Entertainers and performers, sports and related workers, all other", "2800": "Announcers", "2805": "Broadcast announcers and radio disc jockeys", "2810": "News analysts, reporters, and journalists", "2820": "Public relations specialists", "2825": "Public relations specialists", "2830": "Editors", "2840": "Technical writers", "2850": "Writers and authors", "2860": "Miscellaneous media and communication workers", "2861": "Interpreters and translators", "2862": "Court reporters and simultaneous captioners", "2865": "Media and communication workers, all other", "2900": "Broadcast and sound engineering technicians and radio operators", "2905": "Broadcast, sound, and lighting technicians", "2910": "Photographers", "2920": "Television, video, and film camera operators and editors", "2960": "Media and communication equipment workers, all other", "2970": "Media and communication equipment workers, all other", "2999": "Healthcare practitioners and technical occupations", "3000": "Chiropractors", "3010": "Dentists", "3030": "Dietitians and nutritionists", "3040": "Optometrists", "3050": "Pharmacists", "3060": "Physicians and surgeons", "3065": "Emergency medicine physicians", "3070": "Radiologists", "3090": "Other physicians", "3100": "Surgeons", "3110": "Physician assistants", "3120": "Podiatrists", "3130": "Registered nurses", "3140": "Audiologists", "3150": "Occupational therapists", "3160": "Physical therapists", "3200": "Radiation therapists", "3210": "Recreational therapists", "3220": "Respiratory therapists", "3230": "Speech-language pathologists", "3235": "Exercise physiologists", "3240": "Therapists, all other", "3245": "Therapists, all other", "3250": "Veterinarians", "3255": "Registered nurses", "3256": "Nurse anesthetists", "3257": "Nurse midwives", "3258": "Nurse practitioners", "3260": "Health diagnosing and treating practitioners, all other", "3261": "Acupuncturists", "3270": "Healthcare diagnosing or treating practitioners, all other", "3300": "Clinical laboratory technologists and technicians", "3310": "Dental hygienists", "3320": "Diagnostic related technologists and technicians", "3321": "Cardiovascular technologists and technicians", "3322": "Diagnostic medical sonographers", "3323": "Radiologic technologists and technicians", "3324": "Magnetic resonance imaging technologists", "3330": "Nuclear medicine technologists and medical dosimetrists", "3400": "Emergency medical technicians and paramedics", "3401": "Emergency medical technicians", "3402": "Paramedics", "3410": "Health diagnosing and treating practitioner support technicians", "3420": "Health practitioner support technologists and technicians", "3421": "Pharmacy technicians", "3422": "Psychiatric technicians", "3423": "Surgical technologists", "3424": "Veterinary technologists and technicians", "3430": "Dietetic technicians and ophthalmic medical technicians", "3500": "Licensed practical and licensed vocational nurses", "3510": "Medical records and health information technicians", "3515": "Medical records specialists", "3520": "Opticians, dispensing", "3530": "Miscellaneous health technologists and technicians", "3535": "Miscellaneous health technologists and technicians", "3540": "Other healthcare practitioners and technical occupations", "3545": "Miscellaneous health technologists and technicians", "3550": "Other healthcare practitioners and technical occupations", "3597": "Service occupations", "3598": "Service occupations, except protective", "3599": "Healthcare support occupations", "3600": "Nursing, psychiatric, and home health aides", "3601": "Home health aides", "3602": "Personal care aides", "3603": "Nursing assistants", "3605": "Orderlies and psychiatric aides", "3610": "Occupational therapy assistants and aides", "3620": "Physical therapist assistants and aides", "3630": "Massage therapists", "3640": "Dental assistants", "3645": "Medical assistants", "3646": "Medical transcriptionists", "3647": "Pharmacy aides", "3648": "Veterinary assistants and laboratory animal caretakers", "3649": "Phlebotomists", "3650": "Medical assistants and other healthcare support occupations", "3655": "Other healthcare support workers", "3699": "Protective service occupations", "3700": "First-line supervisors of correctional officers", "3710": "First-line supervisors of police and detectives", "3720": "First-line supervisors of fire fighting and prevention workers", "3725": "First-line supervisors of security workers", "3730": "First-line supervisors of protective service workers, all other", "3735": "First-line supervisors of protective service workers, all other", "3740": "Firefighters", "3750": "Fire inspectors", "3800": "Bailiffs, correctional officers, and jailers", "3801": "Bailiffs", "3802": "Correctional officers and jailers", "3820": "Detectives and criminal investigators", "3830": "Fish and game wardens", "3840": "Parking enforcement workers", "3850": "Police and sheriff's patrol officers", "3860": "Transit and railroad police", "3870": "Police officers", "3900": "Animal control workers", "3910": "Private detectives and investigators", "3920": "Security guards and gaming surveillance officers", "3930": "Security guards and gaming surveillance officers", "3940": "Crossing guards and flaggers", "3945": "Transportation security screeners", "3946": "School bus monitors", "3950": "Lifeguards and other protective service workers", "3955": "Lifeguards and other recreational, and all other protective service workers", "3960": "Other protective service workers", "3999": "Food preparation and serving related occupations", "4000": "Chefs and head cooks", "4010": "First-line supervisors of food preparation and serving workers", "4020": "<PERSON><PERSON>", "4030": "Food preparation workers", "4040": "Bart<PERSON>s", "4050": "Combined food preparation and serving workers, including fast food", "4055": "Fast food and counter workers", "4060": "Counter attendants, cafeteria, food concession, and coffee shop", "4110": "Waiters and waitresses", "4120": "Food servers, nonrestaurant", "4130": "Dining room and cafeteria attendants and bartender helpers", "4140": "Dishwashers", "4150": "Hosts and hostesses, restaurant, lounge, and coffee shop", "4160": "Food preparation and serving related workers, all other", "4199": "Building and grounds cleaning and maintenance occupations", "4200": "First-line supervisors of housekeeping and janitorial workers", "4210": "First-line supervisors of landscaping, lawn service, and groundskeeping workers", "4220": "Janitors and building cleaners", "4230": "Maids and housekeeping cleaners", "4240": "Pest control workers", "4250": "Grounds maintenance workers", "4251": "Landscaping and groundskeeping workers", "4252": "Tree trimmers and pruners", "4255": "Other grounds maintenance workers", "4299": "Personal care and service occupations", "4300": "First-line supervisors of gaming workers", "4320": "First-line supervisors of personal service workers", "4330": "Supervisors of personal care and service workers", "4340": "Animal trainers", "4350": "Animal caretakers", "4400": "Gambling services workers", "4410": "Motion picture projectionists", "4420": "Ushers, lobby attendants, and ticket takers", "4430": "Miscellaneous entertainment attendants and related workers", "4435": "Other entertainment attendants and related workers", "4460": "Embalmers and funeral attendants", "4461": "Embalmers, crematory operators and funeral attendants", "4465": "Morticians, undertakers, and funeral arrangers", "4500": "Barbers", "4510": "Hairdressers, hairstylists, and cosmetologists", "4520": "Miscellaneous personal appearance workers", "4521": "Manicurists and pedicurists", "4522": "Skincare specialists", "4525": "Other personal appearance workers", "4530": "Baggage porters, bellhops, and concierges", "4540": "Tour and travel guides", "4550": "Transportation attendants", "4600": "Childcare workers", "4610": "Personal care aides", "4620": "Recreation and fitness workers", "4621": "Exercise trainers and group fitness instructors", "4622": "Recreation workers", "4640": "Residential advisors", "4650": "Personal care and service workers, all other", "4655": "Personal care and service workers, all other", "4698": "Sales and office occupations", "4699": "Sales and related occupations", "4700": "First-line supervisors of retail sales workers", "4710": "First-line supervisors of non-retail sales workers", "4720": "Cashiers", "4740": "Counter and rental clerks", "4750": "Parts salespersons", "4760": "Retail salespersons", "4800": "Advertising sales agents", "4810": "Insurance sales agents", "4820": "Securities, commodities, and financial services sales agents", "4830": "Travel agents", "4840": "Sales representatives of services, except advertising, insurance, travel, and financial services", "4850": "Sales representatives, wholesale and manufacturing", "4900": "Models, demonstrators, and product promoters", "4920": "Real estate brokers and sales agents", "4930": "Sales engineers", "4940": "Telemarketers", "4950": "Door-to-door sales workers, news and street vendors, and related workers", "4960": "Sales and related workers, all other", "4965": "Sales and related workers, all other", "4999": "Office and administrative support occupations", "5000": "First-line supervisors of office and administrative support workers", "5010": "Switchboard operators, including answering service", "5020": "Telephone operators", "5030": "Communications equipment operators, all other", "5040": "Communications equipment operators, all other", "5100": "Bill and account collectors", "5110": "Billing and posting clerks", "5120": "Bookkeeping, accounting, and auditing clerks", "5130": "Gambling cage workers", "5140": "Payroll and timekeeping clerks", "5150": "Procurement clerks", "5160": "Tellers", "5165": "Financial clerks, all other", "5200": "Brokerage clerks", "5210": "Correspondence clerks", "5220": "Court, municipal, and license clerks", "5230": "Credit authorizers, checkers, and clerks", "5240": "Customer service representatives", "5250": "Eligibility interviewers, government programs", "5260": "File Clerks", "5300": "Hotel, motel, and resort desk clerks", "5310": "Interviewers, except eligibility and loan", "5320": "Library assistants, clerical", "5330": "Loan interviewers and clerks", "5340": "New accounts clerks", "5350": "Order clerks", "5360": "Human resources assistants, except payroll and timekeeping", "5400": "Receptionists and information clerks", "5410": "Reservation and transportation ticket agents and travel clerks", "5420": "Information and record clerks, all other", "5500": "Cargo and freight agents", "5510": "Couriers and messengers", "5520": "Dispatchers", "5521": "Public safety telecommunicators", "5522": "Dispatchers, except police, fire, and ambulance", "5530": "Meter readers, utilities", "5540": "Postal service clerks", "5550": "Postal service mail carriers", "5560": "Postal service mail sorters, processors, and processing machine operators", "5600": "Production, planning, and expediting clerks", "5610": "Shipping, receiving, and inventory clerks", "5620": "Stock clerks and order fillers", "5630": "Weighers, measurers, checkers, and samplers, recordkeeping", "5700": "Secretaries and administrative assistants", "5710": "Executive secretaries and executive administrative assistants", "5720": "Legal secretaries and administrative assistants", "5730": "Medical secretaries and administrative assistants", "5740": "Secretaries and administrative assistants, except legal, medical, and executive", "5800": "Computer operators", "5810": "Data entry keyers", "5820": "Word processors and typists", "5830": "Desktop publishers", "5840": "Insurance claims and policy processing clerks", "5850": "Mail clerks and mail machine operators, except postal service", "5860": "Office clerks, general", "5900": "Office machine operators, except computer", "5910": "Proofreaders and copy markers", "5920": "Statistical assistants", "5930": "Office and administrative support workers, all other", "5940": "Office and administrative support workers, all other", "5998": "Natural resources, construction, and maintenance occupations", "5999": "Farming, fishing, and forestry occupations", "6000": "First line supervisors/managers of farming, fishing, and forestry workers", "6005": "First-line supervisors of farming, fishing, and forestry workers", "6010": "Agricultural inspectors", "6020": "Animal breeders", "6040": "Graders and sorters, agricultural products", "6050": "Miscellaneous agricultural workers", "6100": "Fishers and related fishing workers", "6110": "Hunters and trappers", "6115": "Fishing and hunting workers", "6120": "Forest and conservation workers", "6130": "Logging workers", "6199": "Construction and extraction occupations", "6200": "First-line supervisors of construction trades and extraction workers", "6210": "Boilermakers", "6220": "Brickmasons, blockmasons, and stonemasons", "6230": "<PERSON><PERSON>", "6240": "Carpet, floor, and tile installers and finishers", "6250": "Cement masons, concrete finishers, and terrazzo workers", "6260": "Construction laborers", "6300": "Paving, surfacing, and tamping equipment operators", "6305": "Construction equipment operators", "6310": "Pile-driver operators", "6320": "Operating engineers and other construction equipment operators", "6330": "Drywall installers, ceiling tile installers, and tapers", "6350": "Electricians", "6355": "Electricians", "6360": "Glaziers", "6400": "Insulation workers", "6410": "Painters and paperhangers", "6420": "Painters, construction and maintenance", "6430": "Paperhangers", "6440": "Pipelayers, plumbers, pipefitters, and steamfitters", "6441": "Pipelayers", "6442": "Plumbers, pipefitters, and steamfitters", "6460": "Plasterers and stucco masons", "6500": "Reinforcing iron and rebar workers", "6510": "Roofers", "6515": "Roofers", "6520": "Sheet metal workers", "6530": "Structural iron and steel workers", "6540": "Solar photovoltaic installers", "6600": "Helpers, construction trades", "6660": "Construction and building inspectors", "6700": "Elevator installers and repairers", "6710": "Fence erectors", "6720": "Hazardous materials removal workers", "6730": "Highway maintenance workers", "6740": "Rail-track laying and maintenance equipment operators", "6750": "Septic tank servicers and sewer pipe cleaners", "6760": "Miscellaneous construction and related workers", "6765": "Miscellaneous construction and related workers", "6800": "Derrick, rotary drill, and service unit operators, oil and gas", "6820": "Earth drillers, except oil and gas", "6821": "Excavating and loading machine and dragline operators, surface mining", "6825": "Earth drillers, except oil and gas", "6830": "Explosives workers, ordnance handling experts, and blasters", "6835": "Explosives workers, ordnance handling experts, and blasters", "6840": "Mining machine operators", "6850": "Underground mining machine operators", "6910": "Roof bolters, mining", "6920": "Roustabouts, oil and gas", "6930": "Helpers?extraction workers", "6940": "Other extraction workers", "6950": "Other extraction workers", "6999": "Installation, maintenance, and repair occupations", "7000": "First-line supervisors of mechanics, installers, and repairers", "7010": "Computer, automated teller, and office machine repairers", "7020": "Radio and telecommunications equipment installers and repairers", "7030": "Avionics technicians", "7040": "Electric motor, power tool, and related repairers", "7050": "Electrical and electronics installers and repairers, transportation equipment", "7100": "Electrical and electronics repairers, industrial and utility", "7110": "Electronic equipment installers and repairers, motor vehicles", "7120": "Electronic home entertainment equipment installers and repairers", "7130": "Security and fire alarm systems installers", "7140": "Aircraft mechanics and service technicians", "7150": "Automotive body and related repairers", "7160": "Automotive glass installers and repairers", "7200": "Automotive service technicians and mechanics", "7210": "Bus and truck mechanics and diesel engine specialists", "7220": "Heavy vehicle and mobile equipment service technicians and mechanics", "7240": "Small engine mechanics", "7260": "Miscellaneous vehicle and mobile equipment mechanics, installers, and repairers", "7300": "Control and valve installers and repairers", "7310": "Heating, air conditioning, and refrigeration mechanics and installers", "7315": "Heating, air conditioning, and refrigeration mechanics and installers", "7320": "Home appliance repairers", "7330": "Industrial and refractory machinery mechanics", "7340": "Industrial and refractory machinery mechanics", "7350": "Maintenance workers, machinery", "7360": "Millwrights", "7410": "Electrical power-line installers and repairers", "7420": "Telecommunications line installers and repairers", "7430": "Precision instrument and equipment repairers", "7440": "Wind turbine service technicians", "7510": "Coin, vending, and amusement machine servicers and repairers", "7520": "Commercial divers", "7540": "Locksmiths and safe repairers", "7550": "Manufactured building and mobile home installers", "7560": "Riggers", "7600": "Signal and track switch repairers", "7610": "Helpers, installation, maintenance, and repair workers", "7620": "Other installation, maintenance, and repair workers", "7630": "Other installation, maintenance, and repair workers", "7640": "Other installation, maintenance, and repair workers", "7698": "Production, transportation, and material moving occupations", "7699": "Production occupations", "7700": "First-line supervisors of production and operating workers", "7710": "Aircraft structure, surfaces, rigging, and systems assemblers", "7720": "Electrical, electronics, and electromechanical assemblers", "7730": "Engine and other machine assemblers", "7740": "Structural metal fabricators and fitters", "7750": "Other assemblers and fabricators", "7800": "<PERSON><PERSON>", "7810": "Butchers and other meat, poultry, and fish processing workers", "7830": "Food and tobacco roasting, baking, and drying machine operators and tenders", "7840": "Food batchmakers", "7850": "Food cooking machine operators and tenders", "7855": "Food processing workers, all other", "7900": "Computer control programmers and operators", "7905": "Computer numerically controlled tool programmers and operators", "7920": "Extruding and drawing machine setters, operators, and tenders, metal and plastic", "7925": "Forming machine setters, operators, and tenders, metal and plastic", "7930": "Forging machine setters, operators, and tenders, metal and plastic", "7940": "Rolling machine setters, operators, and tenders, metal and plastic", "7950": "Cutting, punching, and press machine setters, operators, and tenders, metal and plastic", "7960": "Drilling and boring machine tool setters, operators, and tenders, metal and plastic", "8000": "Grinding, lapping, polishing, and buffing machine tool setters, operators, and tenders, metal and pl", "8010": "Lathe and turning machine tool setters, operators, and tenders, metal and plastic", "8020": "Milling and planing machine setters, operators, and tenders, metal and plastic", "8025": "Other machine tool setters, operators, and tenders, metal and plastic", "8030": "Machinists", "8040": "Metal furnace operators, tenders, pourers, and casters", "8060": "Model makers and patternmakers, metal and plastic", "8100": "Molders and molding machine setters, operators, and tenders, metal and plastic", "8120": "Multiple machine tool setters, operators, and tenders, metal and plastic", "8130": "Tool and die makers", "8140": "Welding, soldering, and brazing workers", "8150": "Heat treating equipment setters, operators, and tenders, metal and plastic", "8160": "Layout workers, metal and plastic", "8200": "Plating and coating machine setters, operators, and tenders, metal and plastic", "8210": "Tool grinders, filers, and sharpeners", "8220": "Metal workers and plastic workers, all other", "8225": "Other metal workers and plastic workers", "8230": "Bookbinders and bindery workers", "8240": "Job printers", "8250": "Prepress technicians and workers", "8255": "Printing press operators", "8256": "Print binding and finishing workers", "8260": "Printing machine operators", "8300": "Laundry and dry-cleaning workers", "8310": "Pressers, textile, garment, and related materials", "8320": "Sewing machine operators", "8330": "Shoe and leather workers and repairers", "8335": "Shoe and leather workers", "8340": "Shoe machine operators and tenders", "8350": "Tailors, dressmakers, and sewers", "8360": "Textile bleaching and dyeing machine operators and tenders", "8365": "Textile machine setters, operators, and tenders", "8400": "Textile cutting machine setters, operators, and tenders", "8410": "Textile knitting and weaving machine setters, operators, and tenders", "8420": "Textile winding, twisting, and drawing out machine setters, operators, and tenders", "8430": "Extruding and forming machine setters, operators, and tenders, synthetic and glass fibers", "8440": "Fabric and apparel patternmakers", "8450": "Upholsterers", "8460": "Textile, apparel, and furnishings workers, all other", "8465": "Other textile, apparel, and furnishings workers", "8500": "Cabinetmakers and bench carpenters", "8510": "Furniture finishers", "8520": "Model makers and patternmakers, wood", "8530": "Sawing machine setters, operators, and tenders, wood", "8540": "Woodworking machine setters, operators, and tenders, except sawing", "8550": "Woodworkers, all other", "8555": "Other woodworkers", "8600": "Power plant operators, distributors, and dispatchers", "8610": "Stationary engineers and boiler operators", "8620": "Water and wastewater treatment plant and system operators", "8630": "Miscellaneous plant and system operators", "8640": "Chemical processing machine setters, operators, and tenders", "8650": "Crushing, grinding, polishing, mixing, and blending workers", "8710": "Cutting workers", "8720": "Extruding, forming, pressing, and compacting machine setters, operators, and tenders", "8730": "Furnace, kiln, oven, drier, and kettle operators and tenders", "8740": "Inspectors, testers, sorters, samplers, and weighers", "8750": "Jewelers and precious stone and metal workers", "8760": "Dental and ophthalmic laboratory technicians and medical appliance technicians", "8800": "Packaging and filling machine operators and tenders", "8810": "Painting workers", "8830": "Photographic process workers and processing machine operators", "8840": "Semiconductor processors", "8850": "Adhesive bonding machine operators and tenders", "8860": "Cleaning, washing, and metal pickling equipment operators and tenders", "8865": "Other production equipment operators and tenders", "8900": "Cooling and freezing equipment operators and tenders", "8910": "Etchers and engravers", "8920": "Molders, shapers, and casters, except metal and plastic", "8930": "Paper goods machine setters, operators, and tenders", "8940": "Tire builders", "8950": "Helpers, production workers", "8960": "Production workers, all other", "8965": "Production workers, all other", "8990": "Other production workers", "8999": "Transportation and material moving occupations", "9000": "Supervisors of transportation and material moving workers", "9005": "Supervisors of transportation and material moving workers", "9030": "Aircraft pilots and flight engineers", "9040": "Air traffic controllers and airfield operations specialists", "9050": "Flight attendants", "9110": "Ambulance drivers and attendants, except emergency medical technicians", "9120": "Bus drivers", "9121": "Bus drivers, school", "9122": "Bus drivers, transit and intercity", "9130": "Driver/sales workers and truck drivers", "9140": "Taxi drivers and chauffeurs", "9141": "Shuttle drivers and chauffeurs", "9142": "Taxi drivers", "9150": "Motor vehicle operators, all other", "9200": "Locomotive engineers and operators", "9210": "Locomotive engineers and operators", "9230": "Railroad brake, signal, and switch operators", "9240": "Railroad conductors and yardmasters", "9260": "Subway, streetcar, and other rail transportation workers", "9265": "Other rail transportation workers", "9300": "Sailors and marine oilers", "9310": "Ship and boat captains and operators", "9330": "Ship engineers", "9340": "Bridge and lock tenders", "9350": "Parking attendants", "9360": "Automotive and watercraft service attendants", "9365": "Transportation service attendants", "9410": "Transportation inspectors", "9415": "Passenger attendants", "9420": "Other transportation workers", "9430": "Other transportation workers", "9500": "Conveyor operators and tenders", "9510": "Crane and tower operators", "9520": "Dredge, excavating, and loading machine operators", "9560": "Hoist and winch operators", "9570": "Conveyor, dredge, and hoist and winch operators", "9600": "Industrial truck and tractor operators", "9610": "Cleaners of vehicles and equipment", "9620": "Laborers and freight, stock, and material movers, hand", "9630": "Machine feeders and offbearers", "9640": "Packers and packagers, hand", "9645": "Stockers and order fillers", "9650": "Pumping station operators", "9720": "Refuse and recyclable material collectors", "9730": "Mine shuttle car operators", "9740": "Tank car, truck, and ship loaders", "9750": "Material moving workers, all other", "9760": "Other material moving workers", "9800": "Total nonfarm occupations -Excludes farming, fishing and forestry occ.", "9840": "Armed Forces (last job)"}, "orig_code": {"00": "All Origins", "01": "Hispanic or Latino", "02": "Mexican", "05": "Mexican (discontinued)", "06": "Puerto Rican", "07": "Cuban", "08": "Central or South American (discontinued)", "09": "Other Hispanic or Latino (discontinued)", "10": "Non-Hispanic", "15": "Central or South American", "20": "Central American", "21": "Salvadoran", "25": "Other Central American (excludes Salvadoran)", "30": "South American", "40": "Other Hispanic or Latino", "41": "Dominican", "45": "Other Hispanic or Latino (excludes Dominican)"}, "pcts_code": {"00": null, "01": "Percent of civilian noninstitutional population", "02": "Percent of civilian noninstitutional population", "03": "Percent of civilian labor force", "04": "Percent of not in labor force", "05": "Percent of employed within group", "06": "Percent of total employed", "07": "Percent of total unemployed", "08": "Percent of unemployed within group", "11": "Percent of total job seekers", "18": "Percent of total job losers", "19": "Percent of total job leavers", "20": "Percent of total reentrants", "21": "Percent of total new entrants", "24": "Percent of employed in all agri industries", "25": "Percent of employed in all nonagri industries", "26": "Percent of at work in all nonagri industries", "27": "Percent of at work in all agri industries", "33": "Percent of employed by industry", "34": "Percent of employed by occupation", "35": "Percent of at work by industry", "36": "Percent of at work by occupation", "38": "Percent of total at work", "39": "Percent of job losers on layoff", "40": "Percent of job losers for other reasons", "46": "Unemployed and discouraged workers as a percent of the labor force and discouraged workers", "47": "Unemployed and marginally attached workers as a percent of the labor force and marg attached", "48": "Unemployed and mrg attached and pt for econ reas as percent of labor force plus marg attached", "51": "Percent of persons who teleworked or worked at home for pay", "52": "Hours teleworked or worked at home for pay as a percent of hours worked"}, "race_code": {"00": "All Races", "01": "White", "03": "Black or African American", "04": "Asian", "05": "American Indian or Alaska Native", "06": "Native Hawaiian or Other Pacific Islander", "07": "Two or more races", "10": "Asian - Asian Indian", "15": "Asian - Chinese", "25": "Asian - Filipino", "26": "Asian - Japanese", "27": "Asian - Korean", "28": "Asian - Vietnamese", "30": "Asian - Other Asian"}, "rjnw_code": {"00": null, "01": "Own illness, injury, or medical problems", "02": "Vacation or personal days", "03": "Weather affected job (bad weather)", "04": "Labor dispute", "05": "All reasons other than own illness, vacation, bad weather, or labor dispute", "06": "All reasons other than own illness or vacation", "07": "Other reasons (than 1 and 2)", "08": "Child care problems, maternity/paternity leave, or other family/personal obligations", "20": "Own illness/injury, Child care problems, other personal obligations, maternity leave, or civic duty", "21": "Child care problems, other personal obligation, maternity/paternity leave, or civic/military duty", "30": "Childcare problems", "31": "Other family or personal obligations", "32": "Maternity or paternity leave", "33": "School or training", "34": "Civic or military duty", "35": "Other reasons, not elsewhere classified"}, "rnlf_code": {"00": null, "60": "Did not search for work in previous year", "61": "Searched for work in previous year - Total", "62": "Searched for work in previous year - Not available to work now", "63": "Searched for work in previous year - Available to work now (Marginally attached)", "64": "Discouragement over job prospects (believes no job is available)", "65": "Reasons other than discouragement - Total", "66": "Reasons other than discouragement - Family responsibilities", "67": "Reasons other than discouragement - In school or training", "68": "Reasons other than discouragement - Ill health or disability", "69": "Reasons other than discouragement - Other (child care, transportation problems, other reasons)"}, "rwns_code": {"00": null, "01": "Economic reasons", "02": "Slack work", "03": "Material shortage", "04": "Plant or machine repair", "05": "Job started during week", "06": "Job terminated during week", "07": "Could only find part time work", "08": "Other economic reasons (than 2 through 7)", "09": "Material shortages and repairs to plant and equipment", "10": "Noneconomic reasons", "11": "Labor dispute", "12": "Holiday (legal or religious)", "13": "Bad weather", "14": "Illness", "15": "Vacation", "16": "Personal reasons (business, home or school)", "17": "Do not want, or unavailable for, full time work", "18": "Full-time work week under 35 hours", "30": "Child-care problems", "31": "In school or training", "32": "Retired or social security limit on earnings", "33": "Other family or personal obligations", "34": "Health or medical limitations", "35": "All other reasons (than 12-15 and 30-33)", "40": "Seasonal work (economic reason)", "41": "Job started or ended during the week (economic reason)"}, "seek_code": {"00": null, "01": "Job Seeker (Looking for work)"}, "sexs_code": {"0": "Both Sexes", "1": "Men", "2": "Women"}, "tdat_code": {"00": "Number in thousands", "01": "Percent or rate", "02": "Average weeks", "03": "Median weeks", "04": "Average hours at work", "05": "Median hours at work", "06": "Average years", "07": "Median years", "08": "Average number of methods used for seeking jobs", "09": "Total methods used for seeking jobs", "10": "hours at work worked", "11": "hours at work offered", "12": "hours at work lost", "13": "Aggregate-hours at work", "14": "Aggregate-weeks", "15": "Aggregate weeks of unemployment", "16": "Number of families", "17": "Aggregate usual hours", "18": "Average weekly hours teleworked or worked at home for pay", "19": "Aggregate weekly hours teleworked or worked at home for pay"}, "vets_code": {"00": null, "01": "Veteran", "03": "World War II or Korean War or Vietnam Era", "09": "Gulf War Era", "12": "Veterans who served in Gulf War Era 2 (whether or not they served in Era 1)", "13": "Veterans who served in Gulf War Era 1 but not Gulf War Era 2", "16": "Other Service Periods (may include peacetime)", "25": "Nonveteran"}, "wkst_code": {"00": null, "01": "At work", "02": "At work part time", "03": "At work part time, usually work full time", "04": "At work part time, usually work part time", "05": "At work on full time schedules  (only use to distinguish >35 hrs)", "07": "With a job not at work", "08": "Usually work full-time schedules (with a job not at work)", "09": "Usually work part-time schedules (with a job not at work)", "19": "At work full time", "20": "At work 35+ hours, usually work part time", "21": "At work 35+ hours, usually work full time"}, "born_code": {"00": null, "01": "Native born", "02": "Foreign born", "03": "Citizen", "04": "Non-citizen (Year of Entry)", "05": "Before 1965", "06": "1965-74", "07": "1975-84", "08": "1985-89", "09": "1990-94", "10": "1995-99", "11": "2000-04", "12": "2005"}, "chld_code": {"00": null, "01": "With own children under 18", "02": "With own children 6 to 17, none younger", "03": "With own children under 6", "04": "With own children under 3", "05": "With no own children under 18"}, "disa_code": {"00": null, "01": "Not disabled", "02": "Disabled person"}, "tlwk_code": {"00": null, "01": "Persons who teleworked or worked at home for pay", "02": "Persons who teleworked or worked at home for pay, Teleworked some hours", "03": "Persons who teleworked or worked at home for pay, Teleworked all hours", "04": "Persons who did not telework or work at home for pay", "05": "Teleworked, Up to 8 hours", "06": "Teleworked, 9 to 16 hours", "07": "Teleworked, 17 to 24 hours", "08": "Teleworked, 25 to 32 hours", "09": "Teleworked, 33 to 39 hours", "10": "Teleworked, 40 hours or more", "11": "Teleworked, 40 hours", "12": "Teleworked, 41 hours or more"}, "footnote_code": {"1": "Data affected by changes in population controls.", "2": "Constructed on the 2002 Census Industry Classification from data originally coded on earlier classifications. Official series was not revised.", "3": "2000 forward coded on the 2002 Census Occupation Classification. 1983-99 constructed from data originally coded on earlier classifications.", "4": "2000 forward coded on the 2002 Census Industry Classification. 1983-99 constructed from data originally coded on earlier classifications.", "7": "Data do not meet publication criteria.", "8": "This series id code has been discontinued; data are available using the database tool at www.bls.gov/webapps/legacy/cpsatab8.htm.", "9": "Data from 1994 through 2002 were revised in February 2014 with updated seasonal adjustments.", "C": "Corrected"}}, "fm": {"fchld_code": {"00": null, "01": "With own children under 18", "02": "With own children 6 to 17, none younger", "03": "With own children under 6"}, "fdat_code": {"00": "Numbers in thousands", "01": "Percent of total families", "02": "Percent of total families with unemployment"}, "fhlf_code": {"00": null, "01": "In labor force", "02": "Employed", "03": "Unemployed", "04": "Unemployed or NILF", "05": "Employed or NILF", "06": "Not in labor force"}, "fnmatwk_code": {"00": null, "01": "With no one at work", "02": "With at least 1 person at work", "03": "With at least 2 person at work", "04": "With at least 3 person at work", "05": "With only 1 person at work", "06": "With only 2 persons at work", "07": "With only 3 persons at work"}, "fnme_code": {"00": null, "01": "With no one employed", "02": "With at least 1 person employed", "03": "With at least 2 person employed", "04": "With at least 3 person employed", "05": "With only 1 person employed", "06": "With only 2 persons employed", "07": "With only 3 persons employed"}, "fnmlf_code": {"00": null, "01": "With no one in labor force", "02": "With at least 1 person in labor force", "03": "With at least 2 person in labor force", "04": "With at least 3 person in labor force", "05": "With only 1 person in labor force", "06": "With only 2 persons in labor force", "07": "With only 3 persons in labor force"}, "fnmu_code": {"00": null, "02": "With at least 1 person unemployed"}, "fnmws_code": {"00": null, "01": "With at least 1 wage and salary worker", "02": "With at least 2 wage and salary workers", "03": "With only 1 wage and salary worker"}, "forig_code": {"00": null, "01": "Hispanic or Latino"}, "frace_code": {"00": null, "01": "White", "03": "Black or African American", "04": "Asian"}, "ftpt_code": {"00": null, "01": "Some family members usually work full time", "02": "Some family members usually work part time"}, "ftyp_code": {"00": null, "01": "Total families", "02": "Opposite-sex married-couple families", "03": "Families maintained by women", "04": "Families maintained by men", "05": "Total married-couple families"}, "hhlf_code": {"00": null, "01": "In labor force", "02": "Employed", "03": "Unemployed", "04": "Unemployed or NILF", "05": "Employed or NILF", "06": "Not in labor force"}, "misclf_code": {"00": null, "01": "Other employment combinations"}, "mwlf_code": {"00": null, "01": "In labor force", "02": "Employed", "03": "Unemployed", "04": "Unemployed or NILF", "05": "Employed or NILF", "06": "Not in labor force"}, "prlf_code": {"00": null, "01": "At least 1 spouse employed", "02": "With only 1 spouse employed", "03": "At least one spouse unemployed", "04": "With only 1 spouse unemployed"}, "chld_code": {"00": null, "01": "With own children under 18", "02": "With own children 6 to 17, none younger", "03": "With own children under 6", "04": "With own children under 3", "05": "With no own children under 18", "06": "With own children 14 to 17, none younger", "07": "With own children 6 to 13, none younger", "08": "With own children 3 to 5, none younger", "09": "With own child 2 years old", "10": "With own child 1 year old", "11": "With own child under 1 year old", "20": "With own child under 1 year old", "21": "With own child 1 year old", "22": "With own child 2 years old"}, "lfst_code": {"00": "Civilian noninstitutional population", "10": "Civilian labor force", "13": "Civilian labor force participation rate", "20": "Employed", "23": "Employment-population ratio", "25": "Employed full time (persons who usually work 35 hours or more)", "26": "Employed part time (persons who usually work less than 35 hours)", "30": "Unemployed", "40": "Unemployment rate", "50": "Not in labor force", "99": null}, "mari_code": {"00": null, "02": "Married, spouse present", "11": "Other marital status (never married; widowed; divorced; married, spouse absent; or separated)"}, "orig_code": {"00": null, "01": "Hispanic or Latino"}, "race_code": {"00": null, "01": "White", "03": "Black or African American", "04": "Asian"}, "sexs_code": {"0": null, "1": "Men", "2": "Women"}, "tdat_code": {"00": "Numbers in thousands", "01": "Percent"}, "wkst_code": {"00": null, "04": "At work part time, usually work part time", "21": "At work 35+ hours, usually work full time"}, "footnote_code": {"1": "Beginning with 2012, estimates reflect improved methodology that provides more accurate estimates of mothers and fathers. Data for 2012 were revised; earlier years are not strictly comparable."}}, "in": {"economicseries_code": {"0017": "LABOR FORCE", "0018": "LABOR FORCE PARTICIPATION RATES", "0019": "EMPLOYMENT", "0020": "EMPLOYMENT-POPULATION RATIOS", "0021": "UNEMPLOYMENT", "0022": "UNEMPLOYMENT RATES, MONTHLY AND QUARTERLY, SEASONALLY ADJUSTED", "0023": "UNEMPLOYMENT RATES, ANNUAL, NOT SEASONALLY ADJUSTED", "0103": "UNEMPLOYMENT RATES FOR MEN", "0104": "UNEMPLOYMENT RATES FOR WOMEN", "0105": "UNEMPLOYMENT RATES FOR TEENAGERS", "0106": "UNEMPLOYMENT RATES FOR PERSONS AGES 20 TO 24", "0107": "UNEMPLOYMENT RATES FOR YOUTH", "0108": "UNEMPLOYMENT RATES FOR ADULTS", "0202": "EMPLOYMENT IN AGRICULTURE", "0203": "EMPLOYMENT IN INDUSTRY", "0204": "EMPLOYMENT IN MANUFACTURING", "0205": "EMPLOYMENT IN SERVICES", "0206": "PERCENT OF <PERSON>MPLOYMENT IN AGRICULTURE", "0207": "PERCENT OF <PERSON>MPLOYMENT IN INDUSTRY", "0208": "PERCENT OF <PERSON>MPLOYMENT IN MANUFACTURING", "0209": "PERCENT OF <PERSON>MPLOYMENT IN SERVICES", "0211": "EMPLOYMENT-P<PERSON><PERSON>LATION RATIOS FOR MEN", "0212": "EMPLOYMENT-<PERSON><PERSON><PERSON><PERSON><PERSON>ON RATIOS FOR WOMEN", "0302": "WOMENS SHARE OF THE LABOR FORCE", "0304": "LABOR FORCE PARTICIPATION RATES FOR MEN", "0305": "LABOR FORCE PARTICIPATION RATES FOR WOMEN", "0306": "INACTIVITY RATES", "0307": "INACTIVITY RATES FOR MEN", "0308": "INACTIVITY RATES FOR WOMEN", "0401": "WORKING-AGE POPULATION", "0402": "WORKING-AGE POPULATION FOR MEN", "0403": "WORKING-<PERSON>E POPULATION FOR WOMEN", "1000": "INDEXES OF CONSUMER PRICES, MONTH<PERSON><PERSON>, SEASONALLY ADJUSTED", "1001": "INDEXES OF CONSUMER PRICES, ANNUAL, NOT SEASONALLY ADJUSTED", "2000": "HARMONIZED INDEXES OF CONSUMER PRICES, MON<PERSON><PERSON><PERSON>, SEASONALLY ADJUSTED", "2001": "HARMONIZED INDEXES OF CONSUMER PRICES, ANNUAL, NOT SEASONALLY ADJUSTED", "3011": "MANUFACTURING HR COMPENSATION INDEX, U.S. DOLLAR BASIS (U.S.=100)", "3012": "MANUFACTURING HR COMPENSATION, U.S. DOLLARS", "3013": "MANUFACTURING HR COMPENSATION INDEX, U.S. DOLLAR BASIS (YEAR 2000=100)", "3014": "MANUFACTURING HR COMPENSATION, NATL CURRENCY", "3015": "MANUFACTURING HR COMPENSATION INDEX, NATL CURRENCY BASIS (YEAR 2000=100)", "3021": "MANUFACTURING HR DIRECT PAY INDEX, U.S. DOLLAR BASIS (U.S.=100)", "3022": "MANUFACTURING HR DIRECT PAY, U.S. DOLLARS", "3023": "MANUFACTURING HR DIRECT PAY, NATL CURRENCY", "3031": "MANUFACTURING HR PAY FOR TIME WORKED INDEX, U.S. DOLLAR BASIS (U.S.=100)", "3032": "MANUFACTURING HR PAY FOR TIME WORKED, U.S. DOLLARS", "3033": "MANUFACTURING HR PAY FOR TIME WORKED, NATL CURRENCY", "3041": "MANUFACTURING HR DIRECT BENEFITS INDEX, U.S. DOLLAR BASIS (U.S.=100)", "3042": "MANUFACTURING HR DIRECT BENEFITS, U.S. DOLLARS", "3043": "MANUFACTURING HR DIRECT BENEFITS, NATL CURRENCY", "3051": "MANUFACTURING HR SOCIAL INSURANCE AND <PERSON><PERSON><PERSON> TAXES INDEX, U.S. DOLLAR BASIS (U.S.=100)", "3052": "MANUFACTURING HR SOCIAL INSURANCE AND <PERSON>BOR TAXES, U.S. DOLLARS", "3053": "MANUFACTURING HR SOCIAL INSURANCE AND <PERSON>BOR TAXES, NATL CURRENCY", "3061": "MANUFACTURING HR DIRECT PAY, PERCENT OF HR COMPENSATION", "3062": "MANUFACTURING HR PAY FOR TIME WORKED, PERCENT OF HR COMPENSATION", "3063": "MANUFACTURING HR DIRECT BENEFITS, PERCENT OF HR COMPENSATION", "3064": "MANUFACTURING HR SOCIAL INSURANCE AND <PERSON><PERSON><PERSON> TAXES, PERCENT OF HR COMPENSATION", "3071": "EXCHANGE RATE, NATL CURRENCY PER U.S. DOLLAR", "4002": "MANUFACTURING OUTPUT INDEX", "4003": "MANUFACTURING <PERSON><PERSON><PERSON><PERSON><PERSON> HOURS INDEX", "4004": "MANUFACTURING AG<PERSON><PERSON>ATE COMPENSATION INDEX, PRODUCTIVITY SERIES", "4005": "MANUFACTURING OUTPUT PER HOUR INDEX", "4006": "MANUFACTURING HR COMPENSATION INDEX, NATL CURRENCY BASIS, PRODUCTIVITY SERIES", "4007": "MANUFACTURING HR COMPENSATION INDEX, US DOLLAR BASIS, PRODUCTIVITY SERIES", "4008": "MANUFACTURING <PERSON>IT LABOR COST INDEX, NATL CURRENCY BASIS", "4009": "MANUFACTURING <PERSON>IT LABOR COST INDEX, US DOLLAR BASIS", "4010": "EXCHANGE RATE INDEX", "4012": "AVERAGE ANNUAL COMPENSATION, NATIONAL CURRENCY BASIS, PRODUCTIVITY SERIES", "4013": "REAL HOURLY COMPENSATION, CPI BASIS, PRODUCTIVITY SERIES", "4014": "REAL AVERAGE ANNUAL COMPENSATION, CPI BASIS, PRODUCTIVITY SERIES", "4020": "OUTPUT PER EMPLOYED PERSON", "4024": "MANUFACTURING EMPLOYMENT INDEX", "4025": "MANUFACTURING AVERAGE HOURS INDEX", "5001": "REAL GDP PER CAPITA, IN U.S. DOLLARS", "5002": "REAL GDP PER EMPLOYED PERSON, IN U.S. DOLLARS", "5003": "REAL GDP PER HOUR WORKED, IN U.S. DOLLARS", "5004": "REAL GDP IN U.S. DOLLARS", "5005": "POPULATION", "5006": "EMPLOYMENT", "5007": "AVERAGE ANNUAL HOURS WORKED PER EMPLOYED PERSON", "5008": "EMPLOYMENT AS PERCENTAGE OF POPULATION", "5009": "PURCHASING POWER PARITIES"}, "industry_code": {"Whole economy": "0", "Manufacturing": "0", "Food products": "3", "Beverages": "3", "Tobacco products": "2", "Textiles": "3", "Wearing apparel": "3", "Leather and related products": "2", "Wood and wood products, except furniture": "2", "Paper and paper products": "2", "Printing and reproduction of recorded media": "2", "Coke and refined petroleum products": "1", "Chemicals and chemical products, except pharmaceuticals": "2", "Pharmaceutical products": "2", "Rubber and plastics products": "2", "Other nonmetallic mineral products": "2", "Basic metals": "2", "Fabricated metal products, except machinery and equipment": "2", "Computer, electronic and optical products": "1", "Electrical equipment": "1", "Machinery and equipment n.e.c.": "1", "Motor vehicles, trailers and semi-trailers": "2", "Other transport equipment": "2", "Furniture": "1", "Other manufacturing": "1", "Food products, beverages, and tobacco": "1", "Textiles, wearing apparel, and leather": "1", "Wood and paper products and printing": "1", "Chemicals and pharmaceutical products": "1", "Rubber, plastics, and other non-metallic mineral products": "1", "Primary and fabricated metal products, except machinery and equipment": "1", "Motor vehicles and other transport equipment": "1", "Food products and beverages": "2", "Textiles and wearing apparel": "2"}, "country_code": {"AR": "ARGENTINA", "AT": "AUSTRIA", "AU": "AUSTRALIA", "BE": "BELGIUM", "BR": "BRAZIL", "CA": "CANADA", "CH": "SWITZERLAND", "CZ": "CZECH REPUBLIC", "DE": "GERMANY", "DK": "DENMARK", "EE": "ESTONIA", "FI": "FINLAND", "FR": "FRANCE", "GR": "GREECE", "HU": "HUNGARY", "IE": "IRELAND", "IL": "ISRAEL", "IT": "ITALY", "JP": "JAPAN", "KR": "KOREA, REPUBLIC OF", "MX": "MEXICO", "NL": "NETHERLANDS", "NO": "NORWAY", "NZ": "NEW ZEALAND", "PH": "PHILIPPINES", "PL": "POLAND", "PT": "PORTUGAL", "SE": "SWEDEN", "SG": "SINGAPORE", "SK": "SLOVAKIA", "SP": "SPAIN", "TR": "TURKEY", "TW": "TAIWAN", "UK": "UNITED KINGDOM", "US": "UNITED STATES", "ZA": "SOUTH AFRICA"}, "economicgroup_code": {"0": "CIVILIAN LABOR FORCE, EMPLOYMENT, AND UNEMPLOYMENT", "1": "INDEXES OF CONSUMER PRICES", "2": "HARMONIZED INDEXES OF CONSUMER PRICES", "3": "HOURLY COMPENSATION", "4": "PRODUCTIVITY, MANUFACTURING", "5": "PRODUCTIVITY AND GDP, WHOLE ECONOMY"}, "footnote_code": {"1": "Break in series"}}, "ws": {"measure_code": {"001": "Days of idleness (in 1,000s) from all work stoppages in effect in the period", "002": "Days of idleness (as % of total estimated working time) from work stoppages in effect in the period", "010": "Number of workers (in 1,000s) involved in all work stoppages beginning in the period", "020": "Number of workers (in 1,000s) involved in all work stoppages in effect in the period", "100": "Number of work stoppages involving 1,000 workers or more beginning in the period", "200": "Number of work stoppages involving 1,000 workers or more in effect in the period"}, "footnote_code": {"1": "Not available", "2": "Less than 0.005", "3": "Reflects an increase in the number of workers idled by a work stoppage in effect", "D": "Days of idleness (as % of total estimated working time) from work stoppages in effect in the period are archived and will not be updated after April 2024", "P": "Preliminary"}}}