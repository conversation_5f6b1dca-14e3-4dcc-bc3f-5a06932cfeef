Metadata-Version: 2.3
Name: openbb-regulators
Version: 1.4.3
Summary: Markets and Agency Regulators extension for OpenBB
License: AGPL-3.0-only
Author: OpenBB Team
Author-email: <EMAIL>
Requires-Python: >=3.9.21,<3.13
Classifier: License :: OSI Approved :: GNU Affero General Public License v3
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Requires-Dist: openbb-core (>=1.4.8,<2.0.0)
Description-Content-Type: text/markdown

# OpenBB Regulators Extension

This extension provides a structure for data sourced from various global market regulators.

## Installation

To install the extension, run the following command in this folder:

```bash
pip install openbb-regulators
```

Documentation available [here](https://docs.openbb.co/platform/developer_guide/contributing).

