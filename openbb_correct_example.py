#!/usr/bin/env python3
"""
OpenBB Platform Correct Usage Example
This script demonstrates the proper usage of OpenBB Platform using the obb object
"""

from openbb import obb
from datetime import datetime, timedelta

def main():
    print("🚀 OpenBB Platform Correct Usage Example")
    print("=" * 50)
    
    try:
        # Show available modules
        print("\n📋 Available OpenBB Modules:")
        print("-" * 40)
        
        modules = [attr for attr in dir(obb) if not attr.startswith('_')]
        for module in modules:
            print(f"  - {module}")
        
        # Example 1: Get stock price data using the correct API
        print("\n📈 Example 1: Getting Stock Price Data")
        print("-" * 40)
        
        symbol = "AAPL"
        print(f"Fetching {symbol} stock data...")
        
        try:
            # Using the correct OpenBB API structure
            stock_data = obb.equity.price.historical(
                symbol=symbol,
                provider="yfinance"  # Specify provider
            )
            
            if stock_data is not None:
                print(f"✓ Successfully retrieved stock data")
                print(f"Data type: {type(stock_data)}")
                
                # Try to access the data
                if hasattr(stock_data, 'results'):
                    results = stock_data.results
                    if results:
                        print(f"Number of data points: {len(results)}")
                        latest = results[-1] if results else None
                        if latest:
                            print(f"Latest data point: {latest}")
                elif hasattr(stock_data, 'to_df'):
                    df = stock_data.to_df()
                    print(f"DataFrame shape: {df.shape}")
                    print(f"Latest close price: ${df['close'].iloc[-1]:.2f}")
                else:
                    print(f"Stock data structure: {stock_data}")
            else:
                print("⚠ No data retrieved")
                
        except Exception as e:
            print(f"⚠ Could not fetch stock data: {e}")
            print("This might be due to API limitations, network issues, or missing API keys")
        
        # Example 2: Explore equity module capabilities
        print(f"\n🏢 Example 2: Exploring Equity Module")
        print("-" * 40)
        
        try:
            equity_attrs = [attr for attr in dir(obb.equity) if not attr.startswith('_')]
            print("✓ Available equity functions:")
            for attr in equity_attrs[:10]:  # Show first 10
                print(f"  - {attr}")
            if len(equity_attrs) > 10:
                print(f"  ... and {len(equity_attrs) - 10} more")
                
        except Exception as e:
            print(f"⚠ Could not explore equity module: {e}")
        
        # Example 3: Check coverage and providers
        print(f"\n🔌 Example 3: Coverage and Providers")
        print("-" * 40)
        
        try:
            # Check available providers
            if hasattr(obb, 'coverage'):
                print("✓ Coverage module available")
                coverage_attrs = [attr for attr in dir(obb.coverage) if not attr.startswith('_')]
                print(f"Coverage functions: {', '.join(coverage_attrs)}")
                
                # Try to get providers information
                try:
                    providers = obb.coverage.providers()
                    if providers:
                        print(f"✓ Available providers: {providers}")
                    else:
                        print("⚠ No providers information available")
                except Exception as e:
                    print(f"⚠ Could not get providers: {e}")
            else:
                print("⚠ Coverage module not available")
                
        except Exception as e:
            print(f"⚠ Could not check coverage: {e}")
        
        # Example 4: Try a simple crypto example
        print(f"\n₿ Example 4: Crypto Data")
        print("-" * 40)
        
        try:
            if hasattr(obb, 'crypto'):
                crypto_attrs = [attr for attr in dir(obb.crypto) if not attr.startswith('_')]
                print(f"✓ Crypto module available with functions: {', '.join(crypto_attrs)}")
                
                # Try to get some crypto data
                try:
                    # This is just an example - actual function names may vary
                    print("Attempting to fetch crypto data...")
                    # crypto_data = obb.crypto.price.historical(symbol="BTC-USD", provider="yfinance")
                    print("⚠ Crypto data fetch skipped (to avoid API errors)")
                except Exception as e:
                    print(f"⚠ Could not fetch crypto data: {e}")
            else:
                print("⚠ Crypto module not available")
                
        except Exception as e:
            print(f"⚠ Could not explore crypto module: {e}")
            
        print(f"\n✅ OpenBB Platform example completed!")
        print("\n💡 Next Steps:")
        print("  1. Set up API keys for data providers (if needed)")
        print("  2. Explore specific modules: obb.equity, obb.crypto, obb.economy, etc.")
        print("  3. Check documentation: https://docs.openbb.co")
        print("  4. Use help(obb.module.function) for specific function help")
        
    except Exception as e:
        print(f"❌ An error occurred: {e}")

if __name__ == "__main__":
    main()
