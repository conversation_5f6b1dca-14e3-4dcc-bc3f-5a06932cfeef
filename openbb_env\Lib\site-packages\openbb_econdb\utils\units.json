{"ACPOPAT": "PERSONS", "ACPOPBE": "PERSONS", "ACPOPBG": "PERSONS", "ACPOPBR": "PERSONS", "ACPOPCA": "PERSONS", "ACPOPCH": "PERSONS", "ACPOPCY": "PERSONS", "ACPOPCZ": "PERSONS", "ACPOPDE": "PERSONS", "ACPOPDK": "PERSONS", "ACPOPEE": "PERSONS", "ACPOPES": "PERSONS", "ACPOPFI": "PERSONS", "ACPOPFR": "PERSONS", "ACPOPGR": "PERSONS", "ACPOPHR": "PERSONS", "ACPOPHU": "PERSONS", "ACPOPIE": "PERSONS", "ACPOPIS": "PERSONS", "ACPOPIT": "PERSONS", "ACPOPJP": "PERSONS", "ACPOPLT": "PERSONS", "ACPOPLU": "PERSONS", "ACPOPLV": "PERSONS", "ACPOPME": "PERSONS", "ACPOPMK": "PERSONS", "ACPOPMT": "PERSONS", "ACPOPMX": "PERCENT", "ACPOPNL": "PERSONS", "ACPOPNO": "PERSONS", "ACPOPPH": "PERSONS", "ACPOPPK": "PERSONS", "ACPOPPL": "PERSONS", "ACPOPPT": "PERSONS", "ACPOPQA": "PERSONS", "ACPOPRO": "PERSONS", "ACPOPRS": "PERSONS", "ACPOPSE": "PERSONS", "ACPOPSI": "PERSONS", "ACPOPSK": "PERSONS", "ACPOPTR": "PERSONS", "ACPOPTW": "PERSONS", "ACPOPUS": "PERSONS", "ACPOPVN": "PERSONS", "CAAR": "USD", "CAAT": "DOMESTIC", "CAAU": "DOMESTIC", "CAAZ": "USD", "CABD": "DOMESTIC", "CABE": "DOMESTIC", "CABG": "DOMESTIC", "CABR": "USD", "CABT": "DOMESTIC", "CABY": "USD", "CACA": "DOMESTIC", "CACH": "DOMESTIC", "CACL": "USD", "CACN": "USD", "CACO": "USD", "CACR": "USD", "CACY": "DOMESTIC", "CACZ": "DOMESTIC", "CADE": "DOMESTIC", "CADK": "DOMESTIC", "CAEE": "DOMESTIC", "CAEG": "USD", "CAES": "DOMESTIC", "CAEU": "DOMESTIC", "CAFI": "DOMESTIC", "CAFR": "DOMESTIC", "CAGR": "DOMESTIC", "CAHK": "DOMESTIC", "CAHR": "DOMESTIC", "CAHU": "DOMESTIC", "CAID": "USD", "CAIE": "DOMESTIC", "CAIL": "USD", "CAIN": "USD", "CAIT": "DOMESTIC", "CAJO": "DOMESTIC", "CAJP": "DOMESTIC", "CAKH": "DOMESTIC", "CAKR": "USD", "CAKZ": "USD", "CALA": "USD", "CALT": "DOMESTIC", "CALU": "DOMESTIC", "CALV": "DOMESTIC", "CAMA": "DOMESTIC", "CAMT": "DOMESTIC", "CAMX": "USD", "CAMY": "DOMESTIC", "CANL": "DOMESTIC", "CANO": "DOMESTIC", "CANP": "DOMESTIC", "CANZ": "DOMESTIC", "CAPA": "DOMESTIC", "CAPH": "USD", "CAPK": "USD", "CAPL": "DOMESTIC", "CAPT": "DOMESTIC", "CARAT": "OTHER NUMBER", "CARBE": "OTHER NUMBER", "CARBG": "OTHER NUMBER", "CARCH": "OTHER NUMBER", "CARCZ": "OTHER NUMBER", "CARDE": "OTHER NUMBER", "CARDK": "OTHER NUMBER", "CAREE": "OTHER NUMBER", "CARES": "OTHER NUMBER", "CAREU": "OTHER NUMBER", "CARFI": "OTHER NUMBER", "CARFR": "OTHER NUMBER", "CARGR": "OTHER NUMBER", "CARHR": "OTHER NUMBER", "CARHU": "OTHER NUMBER", "CARIE": "OTHER NUMBER", "CARIS": "OTHER NUMBER", "CARIT": "OTHER NUMBER", "CARLT": "OTHER NUMBER", "CARLU": "OTHER NUMBER", "CARLV": "OTHER NUMBER", "CARNL": "OTHER NUMBER", "CARNO": "OTHER NUMBER", "CARO": "DOMESTIC", "CARPL": "OTHER NUMBER", "CARPT": "OTHER NUMBER", "CARRO": "OTHER NUMBER", "CARSE": "OTHER NUMBER", "CARSI": "OTHER NUMBER", "CARSK": "OTHER NUMBER", "CARU": "USD", "CARUK": "OTHER NUMBER", "CARZA": "OTHER NUMBER", "CASA": "DOMESTIC", "CASE": "DOMESTIC", "CASG": "DOMESTIC", "CASI": "DOMESTIC", "CASK": "DOMESTIC", "CASV": "DOMESTIC", "CATH": "USD", "CATN": "DOMESTIC", "CATR": "USD", "CATW": "USD", "CAUA": "USD", "CAUK": "DOMESTIC", "CAUS": "DOMESTIC", "CAUY": "USD", "CAVN": "USD", "CAZA": "DOMESTIC", "CBALAL": "DOMESTIC", "CBALAT": "DOMESTIC", "CBALAZ": "USD", "CBALBE": "DOMESTIC", "CBALBG": "DOMESTIC", "CBALCH": "DOMESTIC", "CBALCY": "DOMESTIC", "CBALCZ": "DOMESTIC", "CBALDE": "DOMESTIC", "CBALDK": "DOMESTIC", "CBALEA": "DOMESTIC", "CBALEE": "DOMESTIC", "CBALES": "DOMESTIC", "CBALEU": "DOMESTIC", "CBALFI": "DOMESTIC", "CBALFR": "DOMESTIC", "CBALGR": "DOMESTIC", "CBALHR": "DOMESTIC", "CBALHU": "DOMESTIC", "CBALIE": "DOMESTIC", "CBALIR": "DOMESTIC", "CBALIT": "DOMESTIC", "CBALLT": "DOMESTIC", "CBALLU": "DOMESTIC", "CBALLV": "DOMESTIC", "CBALME": "DOMESTIC", "CBALMT": "DOMESTIC", "CBALNL": "DOMESTIC", "CBALNO": "DOMESTIC", "CBALPH": "DOMESTIC", "CBALPL": "DOMESTIC", "CBALPT": "DOMESTIC", "CBALRO": "DOMESTIC", "CBALRS": "DOMESTIC", "CBALSE": "DOMESTIC", "CBALSI": "DOMESTIC", "CBALSK": "DOMESTIC", "CBALTR": "DOMESTIC", "CBALUK": "DOMESTIC", "CBALUS": "DOMESTIC", "CIAE": "DOMESTIC", "CIAR": "DOMESTIC", "CIAT": "DOMESTIC", "CIAU": "DOMESTIC", "CIAZ": "DOMESTIC", "CIBE": "DOMESTIC", "CIBG": "DOMESTIC", "CIBR": "DOMESTIC", "CIBW": "DOMESTIC", "CIBY": "DOMESTIC", "CICH": "DOMESTIC", "CICN": "DOMESTIC", "CICY": "DOMESTIC", "CICZ": "DOMESTIC", "CIDE": "DOMESTIC", "CIDK": "DOMESTIC", "CIEE": "DOMESTIC", "CIES": "DOMESTIC", "CIFI": "DOMESTIC", "CIFR": "DOMESTIC", "CIGR": "DOMESTIC", "CIHK": "DOMESTIC", "CIHR": "DOMESTIC", "CIHU": "DOMESTIC", "CIID": "DOMESTIC", "CIIE": "DOMESTIC", "CIIL": "DOMESTIC", "CIIN": "DOMESTIC", "CIIR": "DOMESTIC", "CIIT": "DOMESTIC", "CIKR": "DOMESTIC", "CIKZ": "DOMESTIC", "CILT": "DOMESTIC", "CILU": "DOMESTIC", "CILV": "DOMESTIC", "CIMA": "DOMESTIC", "CIME": "DOMESTIC", "CIMN": "DOMESTIC", "CIMT": "DOMESTIC", "CIMX": "DOMESTIC", "CIMY": "DOMESTIC", "CING": "DOMESTIC", "CINL": "DOMESTIC", "CINO": "DOMESTIC", "CINZ": "DOMESTIC", "CIPH": "DOMESTIC", "CIPK": "DOMESTIC", "CIPL": "DOMESTIC", "CIPT": "DOMESTIC", "CIRO": "DOMESTIC", "CIRS": "DOMESTIC", "CIRU": "DOMESTIC", "CISA": "DOMESTIC", "CISE": "DOMESTIC", "CISG": "DOMESTIC", "CISI": "DOMESTIC", "CISK": "DOMESTIC", "CITH": "DOMESTIC", "CITR": "DOMESTIC", "CITW": "DOMESTIC", "CIUA": "DOMESTIC", "CIUK": "DOMESTIC", "CIUS": "DOMESTIC", "CIVN": "DOMESTIC", "CIZA": "DOMESTIC", "CKABE": "DOMESTIC", "CKABG": "DOMESTIC", "CKACZ": "DOMESTIC", "CKADE": "DOMESTIC", "CKADK": "DOMESTIC", "CKAEE": "DOMESTIC", "CKAES": "DOMESTIC", "CKAFI": "DOMESTIC", "CKAFR": "DOMESTIC", "CKAGR": "DOMESTIC", "CKAHR": "DOMESTIC", "CKAHU": "DOMESTIC", "CKAIT": "DOMESTIC", "CKALT": "DOMESTIC", "CKALU": "DOMESTIC", "CKALV": "DOMESTIC", "CKAMT": "DOMESTIC", "CKANL": "DOMESTIC", "CKAPL": "DOMESTIC", "CKAPT": "DOMESTIC", "CKARO": "DOMESTIC", "CKASE": "DOMESTIC", "CKASI": "DOMESTIC", "CKASK": "DOMESTIC", "CLAIMSUS": "PERSONS", "CLIAT": "INDEX", "CLIAU": "INDEX", "CLIBE": "INDEX", "CLIBR": "INDEX", "CLICA": "INDEX", "CLICH": "INDEX", "CLICL": "INDEX", "CLICN": "INDEX", "CLICZ": "INDEX", "CLIDE": "INDEX", "CLIDK": "INDEX", "CLIEE": "INDEX", "CLIES": "INDEX", "CLIFI": "INDEX", "CLIFR": "INDEX", "CLIGR": "INDEX", "CLIHU": "INDEX", "CLIID": "INDEX", "CLIIE": "INDEX", "CLIIL": "INDEX", "CLIIN": "INDEX", "CLIIS": "INDEX", "CLIIT": "INDEX", "CLIJP": "INDEX", "CLIKR": "INDEX", "CLILU": "INDEX", "CLIMX": "INDEX", "CLINL": "INDEX", "CLINO": "INDEX", "CLINZ": "INDEX", "CLIPL": "INDEX", "CLIPT": "INDEX", "CLIRU": "INDEX", "CLISE": "INDEX", "CLISI": "INDEX", "CLISK": "INDEX", "CLITR": "INDEX", "CLIUK": "INDEX", "CLIUS": "INDEX", "CLIZA": "INDEX", "CONAE": "DOMESTIC", "CONAL": "DOMESTIC", "CONAT": "DOMESTIC", "CONBD": "DOMESTIC", "CONBE": "DOMESTIC", "CONBG": "DOMESTIC", "CONBY": "DOMESTIC", "CONCA": "DOMESTIC", "CONCH": "DOMESTIC", "CONCL": "USD", "CONCN": "DOMESTIC", "CONCY": "DOMESTIC", "CONCZ": "DOMESTIC", "CONDE": "DOMESTIC", "CONDK": "DOMESTIC", "CONEA": "DOMESTIC", "CONEE": "DOMESTIC", "CONES": "DOMESTIC", "CONEU": "DOMESTIC", "CONFAL": "INDEX", "CONFAT": "INDEX", "CONFAU": null, "CONFBE": "INDEX", "CONFBG": "INDEX", "CONFBR": "INDEX", "CONFCY": "INDEX", "CONFCZ": "INDEX", "CONFDE": "INDEX", "CONFDK": "INDEX", "CONFEE": "INDEX", "CONFES": "INDEX", "CONFEU": "INDEX", "CONFFI": "INDEX", "CONFFR": "INDEX", "CONFGR": "INDEX", "CONFHR": "INDEX", "CONFHU": "INDEX", "CONFI": "DOMESTIC", "CONFID": "INDEX", "CONFIE": "INDEX", "CONFIL": null, "CONFIT": "INDEX", "CONFJP": "INDEX", "CONFKR": null, "CONFLT": "INDEX", "CONFLU": "INDEX", "CONFLV": "INDEX", "CONFME": "INDEX", "CONFMK": "INDEX", "CONFMT": "INDEX", "CONFMX": "INDEX", "CONFNL": "INDEX", "CONFPL": "INDEX", "CONFPT": "INDEX", "CONFR": "DOMESTIC", "CONFRO": "INDEX", "CONFRS": "INDEX", "CONFSE": "INDEX", "CONFSI": "INDEX", "CONFSK": "INDEX", "CONFTR": "INDEX", "CONFUK": "INDEX", "CONFUS": null, "CONGR": "DOMESTIC", "CONHR": "DOMESTIC", "CONHU": "DOMESTIC", "CONIE": "DOMESTIC", "CONIT": "DOMESTIC", "CONKR": "DOMESTIC", "CONLT": "DOMESTIC", "CONLU": "DOMESTIC", "CONLV": "DOMESTIC", "CONMK": "DOMESTIC", "CONMT": "DOMESTIC", "CONMX": "DOMESTIC", "CONNL": "DOMESTIC", "CONNO": "DOMESTIC", "CONPL": "DOMESTIC", "CONPT": "DOMESTIC", "CONQA": "DOMESTIC", "CONRO": "DOMESTIC", "CONRS": "DOMESTIC", "CONRU": "DOMESTIC", "CONSE": "DOMESTIC", "CONSI": "DOMESTIC", "CONSK": "DOMESTIC", "CONUA": "DOMESTIC", "CONUK": "DOMESTIC", "CONZA": "DOMESTIC", "COREAR": "INDEX", "COREAT": "INDEX", "COREAU": "INDEX", "COREBD": "INDEX", "COREBE": "INDEX", "COREBG": "INDEX", "COREBR": "INDEX", "CORECA": "INDEX", "CORECH": "INDEX", "CORECY": "INDEX", "CORECZ": "INDEX", "COREDE": "INDEX", "COREDK": "INDEX", "COREEA": "INDEX", "COREEE": "INDEX", "COREES": "INDEX", "COREEU": "INDEX", "COREFI": "INDEX", "COREFR": "INDEX", "COREGR": "INDEX", "COREHR": "INDEX", "COREHU": "INDEX", "COREIE": "INDEX", "COREIN": "INDEX", "COREIS": "INDEX", "COREIT": "INDEX", "COREJP": "INDEX", "COREKR": "INDEX", "CORELT": "INDEX", "CORELU": "INDEX", "CORELV": "INDEX", "COREMK": "INDEX", "COREMT": "INDEX", "COREMX": "INDEX", "CORENL": "INDEX", "CORENO": "INDEX", "CORENP": "INDEX", "COREPL": "INDEX", "COREPT": "INDEX", "CORERO": "INDEX", "CORERS": "INDEX", "CORERU": "INDEX", "CORESE": "INDEX", "CORESG": "INDEX", "CORESI": "INDEX", "CORESK": "INDEX", "CORETH": "INDEX", "CORETR": "INDEX", "CORETW": "INDEX", "COREUK": "INDEX", "COREUS": "INDEX", "CPAT": "INDEX", "CPBE": "INDEX", "CPBG": "INDEX", "CPCZ": "INDEX", "CPDE": "INDEX", "CPDK": "INDEX", "CPES": "INDEX", "CPEU": "INDEX", "CPFI": "INDEX", "CPFR": "INDEX", "CPHR": "INDEX", "CPHU": "INDEX", "CPIAE": "INDEX", "CPIAF": "INDEX", "CPIAG": "INDEX", "CPIAL": "INDEX", "CPIAM": "INDEX", "CPIAR": "INDEX", "CPIAT": "INDEX", "CPIAU": "INDEX", "CPIAW": "INDEX", "CPIAZ": "INDEX", "CPIBA": "INDEX", "CPIBB": "INDEX", "CPIBD": "INDEX", "CPIBE": "INDEX", "CPIBF": "INDEX", "CPIBG": "INDEX", "CPIBH": "INDEX", "CPIBI": "INDEX", "CPIBJ": "INDEX", "CPIBN": "INDEX", "CPIBO": "INDEX", "CPIBR": "INDEX", "CPIBS": "INDEX", "CPIBT": "INDEX", "CPIBW": "INDEX", "CPIBY": "INDEX", "CPIBZ": "INDEX", "CPICA": "INDEX", "CPICF": "INDEX", "CPICG": "INDEX", "CPICH": "INDEX", "CPICI": "INDEX", "CPICL": "INDEX", "CPICM": "INDEX", "CPICN": "INDEX", "CPICO": "INDEX", "CPICR": "INDEX", "CPICV": "INDEX", "CPICW": "INDEX", "CPICY": "INDEX", "CPICZ": "INDEX", "CPIDE": "INDEX", "CPIDJ": "INDEX", "CPIDK": "INDEX", "CPIDM": "INDEX", "CPIDO": "INDEX", "CPIEA": "INDEX", "CPIEC": "INDEX", "CPIEE": "INDEX", "CPIEG": "INDEX", "CPIES": "INDEX", "CPIET": "INDEX", "CPIEU": "INDEX", "CPIFI": "INDEX", "CPIFJ": "INDEX", "CPIFR": "INDEX", "CPIGA": "INDEX", "CPIGD": "INDEX", "CPIGE": "INDEX", "CPIGN": "INDEX", "CPIGQ": "INDEX", "CPIGR": "INDEX", "CPIGT": "INDEX", "CPIGW": "INDEX", "CPIGY": "INDEX", "CPIHK": "INDEX", "CPIHN": "INDEX", "CPIHR": "INDEX", "CPIHT": "INDEX", "CPIHU": "INDEX", "CPIID": "INDEX", "CPIIE": "INDEX", "CPIIL": "INDEX", "CPIIN": "INDEX", "CPIIQ": "INDEX", "CPIIR": "INDEX", "CPIIS": "INDEX", "CPIIT": "INDEX", "CPIJM": "INDEX", "CPIJO": "INDEX", "CPIJP": "INDEX", "CPIKG": "INDEX", "CPIKH": "INDEX", "CPIKI": "INDEX", "CPIKN": "INDEX", "CPIKR": "INDEX", "CPIKW": "INDEX", "CPIKZ": "INDEX", "CPILA": "INDEX", "CPILB": "INDEX", "CPILC": "INDEX", "CPILK": "OTHER NUMBER", "CPILR": "INDEX", "CPILS": "INDEX", "CPILT": "INDEX", "CPILU": "INDEX", "CPILV": "INDEX", "CPIMA": "INDEX", "CPIMD": "INDEX", "CPIME": "INDEX", "CPIMG": "INDEX", "CPIMK": "INDEX", "CPIML": "INDEX", "CPIMM": "INDEX", "CPIMN": "INDEX", "CPIMO": "INDEX", "CPIMR": "INDEX", "CPIMS": "INDEX", "CPIMT": "INDEX", "CPIMU": "INDEX", "CPIMV": "INDEX", "CPIMW": "INDEX", "CPIMX": "INDEX", "CPIMY": "INDEX", "CPIMZ": "INDEX", "CPINE": "INDEX", "CPING": "INDEX", "CPINI": "INDEX", "CPINL": "INDEX", "CPINO": "INDEX", "CPINP": "INDEX", "CPINZ": "INDEX", "CPIOM": "INDEX", "CPIPA": "INDEX", "CPIPE": "INDEX", "CPIPH": "INDEX", "CPIPK": "INDEX", "CPIPL": "INDEX", "CPIPS": "INDEX", "CPIPT": "INDEX", "CPIQA": "INDEX", "CPIRO": "INDEX", "CPIRS": "INDEX", "CPIRU": "INDEX", "CPISA": "INDEX", "CPISB": "INDEX", "CPISC": "INDEX", "CPISD": "INDEX", "CPISE": "INDEX", "CPISG": "INDEX", "CPISI": "INDEX", "CPISK": "INDEX", "CPISR": "INDEX", "CPISS": "INDEX", "CPIST": "INDEX", "CPISV": "INDEX", "CPISZ": "INDEX", "CPIT": "INDEX", "CPITD": "INDEX", "CPITG": "INDEX", "CPITH": "INDEX", "CPITJ": "INDEX", "CPITL": "INDEX", "CPITN": "INDEX", "CPITO": "INDEX", "CPITR": "INDEX", "CPITT": "INDEX", "CPITW": "INDEX", "CPIUA": "INDEX", "CPIUG": "INDEX", "CPIUK": "INDEX", "CPIUS": "INDEX", "CPIUY": "INDEX", "CPIUZ": "INDEX", "CPIVC": "INDEX", "CPIVE": "INDEX", "CPIVN": "INDEX", "CPIWS": "INDEX", "CPIXK": "INDEX", "CPIZA": "INDEX", "CPIZM": "INDEX", "CPIZW": "INDEX", "CPLU": "INDEX", "CPMK": "INDEX", "CPMX": "DOMESTIC", "CPNL": "INDEX", "CPNO": "INDEX", "CPPL": "INDEX", "CPPT": "INDEX", "CPRO": "INDEX", "CPSE": "INDEX", "CPSG": "DOMESTIC", "CPSI": "INDEX", "CPSK": "INDEX", "CPUK": "INDEX", "CREDAR": "DOMESTIC", "CREDAT": "DOMESTIC", "CREDAU": "DOMESTIC", "CREDBE": "DOMESTIC", "CREDBR": "DOMESTIC", "CREDCA": "DOMESTIC", "CREDCH": "DOMESTIC", "CREDCL": "DOMESTIC", "CREDCN": "DOMESTIC", "CREDCO": "DOMESTIC", "CREDCZ": "DOMESTIC", "CREDDE": "DOMESTIC", "CREDDK": "DOMESTIC", "CREDEA": "DOMESTIC", "CREDES": "DOMESTIC", "CREDFI": "DOMESTIC", "CREDFR": "DOMESTIC", "CREDGR": "DOMESTIC", "CREDHK": "DOMESTIC", "CREDHU": "DOMESTIC", "CREDID": "DOMESTIC", "CREDIE": "DOMESTIC", "CREDIL": "DOMESTIC", "CREDIN": "DOMESTIC", "CREDIT": "DOMESTIC", "CREDJP": "DOMESTIC", "CREDKR": "DOMESTIC", "CREDLU": "DOMESTIC", "CREDMO": "DOMESTIC", "CREDMX": "DOMESTIC", "CREDMY": "DOMESTIC", "CREDNL": "DOMESTIC", "CREDNO": "DOMESTIC", "CREDNZ": "DOMESTIC", "CREDPL": "DOMESTIC", "CREDPT": "DOMESTIC", "CREDRU": "DOMESTIC", "CREDSA": "DOMESTIC", "CREDSE": "DOMESTIC", "CREDSG": "DOMESTIC", "CREDTH": "DOMESTIC", "CREDTN": "DOMESTIC", "CREDTR": "DOMESTIC", "CREDUK": "DOMESTIC", "CREDUS": "DOMESTIC", "CREDZA": "DOMESTIC", "DWPEBE": "INDEX", "DWPECY": "INDEX", "DWPEDE": "INDEX", "DWPEES": "INDEX", "DWPEEU": "INDEX", "DWPEFI": "INDEX", "DWPEFR": "INDEX", "DWPEGR": "INDEX", "DWPEHU": "INDEX", "DWPEMK": "INDEX", "DWPENL": "INDEX", "DWPENO": "INDEX", "DWPEPT": "INDEX", "DWPERO": "INDEX", "DWPESE": "INDEX", "DWPESI": "INDEX", "ELEAT": "INDEX", "ELEBE": "INDEX", "ELEBG": "INDEX", "ELECH": "INDEX", "ELECN": "OTHER NUMBER", "ELECY": "INDEX", "ELECZ": "INDEX", "ELEDE": "INDEX", "ELEDK": "INDEX", "ELEEE": "INDEX", "ELEES": "INDEX", "ELEEU": "INDEX", "ELEFI": "INDEX", "ELEFR": "INDEX", "ELEGR": "INDEX", "ELEHR": "INDEX", "ELEHU": "INDEX", "ELEIE": "INDEX", "ELEIT": "INDEX", "ELELT": "INDEX", "ELELU": "INDEX", "ELELV": "INDEX", "ELEMK": "INDEX", "ELEMT": "INDEX", "ELENL": "INDEX", "ELENO": "INDEX", "ELEPL": "INDEX", "ELEPT": "INDEX", "ELERO": "INDEX", "ELESE": "INDEX", "ELESI": "INDEX", "ELESK": "INDEX", "ELETR": "INDEX", "ELEUK": "INDEX", "ELEUS": "PERCENT", "ELEZA": "INDEX", "EMPAR": "PERCENT", "EMPAT": "PERSONS", "EMPAU": "PERSONS", "EMPAZ": "OTHER NUMBER", "EMPBD": "PERSONS", "EMPBE": "PERSONS", "EMPBG": "PERSONS", "EMPBR": "PERSONS", "EMPBY": "PERSONS", "EMPCA": "PERSONS", "EMPCH": "PERSONS", "EMPCL": "PERSONS", "EMPCN": "PERSONS", "EMPCO": "PERCENT", "EMPCR": "PERSONS", "EMPCY": "PERSONS", "EMPCZ": "PERSONS", "EMPDE": "PERSONS", "EMPDK": "PERSONS", "EMPDO": "PERSONS", "EMPEE": "PERSONS", "EMPES": "PERSONS", "EMPFI": "PERSONS", "EMPFR": "PERSONS", "EMPGR": "PERSONS", "EMPHR": "PERSONS", "EMPHU": "PERSONS", "EMPID": "PERSONS", "EMPIE": "PERSONS", "EMPIS": "PERSONS", "EMPIT": "PERSONS", "EMPJO": "PERCENT", "EMPJP": "PERSONS", "EMPKR": "PERSONS", "EMPKZ": "PERSONS", "EMPLK": "PERCENT", "EMPLT": "PERSONS", "EMPLU": "PERSONS", "EMPLV": "PERSONS", "EMPME": "PERSONS", "EMPMK": "PERSONS", "EMPMO": "PERSONS", "EMPMT": "PERSONS", "EMPMX": "PERSONS", "EMPMY": "PERSONS", "EMPNL": "PERSONS", "EMPNO": "PERSONS", "EMPNZ": "PERSONS", "EMPPA": "PERSONS", "EMPPE": "INDEX", "EMPPH": "PERSONS", "EMPPK": "PERSONS", "EMPPL": "PERSONS", "EMPPT": "PERSONS", "EMPQA": "PERSONS", "EMPRO": "PERSONS", "EMPRS": "PERSONS", "EMPRU": "PERSONS", "EMPSE": "PERSONS", "EMPSG": "PERSONS", "EMPSI": "PERSONS", "EMPSK": "PERSONS", "EMPTH": "PERSONS", "EMPTN": "PERSONS", "EMPTR": "PERSONS", "EMPTW": "PERSONS", "EMPUA": "PERSONS", "EMPUS": "PERSONS", "EMPUY": "PERCENT", "EMPVN": "PERSONS", "EMPZA": "INDEX", "EMRATIOAT": "PERCENT", "EMRATIOBE": "PERCENT", "EMRATIOBG": "PERCENT", "EMRATIOCH": "PERCENT", "EMRATIOCY": "PERCENT", "EMRATIOCZ": "PERCENT", "EMRATIODE": "PERCENT", "EMRATIODK": "PERCENT", "EMRATIOEE": "PERCENT", "EMRATIOES": "PERCENT", "EMRATIOFI": "PERCENT", "EMRATIOFR": "PERCENT", "EMRATIOGR": "PERCENT", "EMRATIOHR": "PERCENT", "EMRATIOHU": "PERCENT", "EMRATIOIE": "PERCENT", "EMRATIOIS": "PERCENT", "EMRATIOIT": "PERCENT", "EMRATIOJP": "PERCENT", "EMRATIOLT": "PERCENT", "EMRATIOLU": "PERCENT", "EMRATIOLV": "PERCENT", "EMRATIOME": "PERCENT", "EMRATIOMK": "PERCENT", "EMRATIOMT": "PERCENT", "EMRATIONL": "PERCENT", "EMRATIONO": "PERCENT", "EMRATIOPL": "PERCENT", "EMRATIOPT": "PERCENT", "EMRATIORO": "PERCENT", "EMRATIORS": "PERCENT", "EMRATIOSE": "PERCENT", "EMRATIOSI": "PERCENT", "EMRATIOSK": "PERCENT", "EMRATIOTR": "PERCENT", "EMRATIOTW": "PERCENT", "EMRATIOUS": "RATIO", "EQYCAPCN": "DOMESTIC", "EXPAE": "DOMESTIC", "EXPAL": "DOMESTIC", "EXPAR": "DOMESTIC", "EXPAT": "DOMESTIC", "EXPAU": "DOMESTIC", "EXPAZ": "DOMESTIC", "EXPBD": "DOMESTIC", "EXPBE": "DOMESTIC", "EXPBG": "DOMESTIC", "EXPBR": "DOMESTIC", "EXPBW": "DOMESTIC", "EXPBY": "DOMESTIC", "EXPCA": "DOMESTIC", "EXPCH": "DOMESTIC", "EXPCL": "DOMESTIC", "EXPCM": "DOMESTIC", "EXPCN": "DOMESTIC", "EXPCO": "DOMESTIC", "EXPCY": "DOMESTIC", "EXPCZ": "DOMESTIC", "EXPDE": "DOMESTIC", "EXPDK": "DOMESTIC", "EXPEA": "DOMESTIC", "EXPEE": "DOMESTIC", "EXPES": "DOMESTIC", "EXPEU": "DOMESTIC", "EXPFI": "DOMESTIC", "EXPFR": "DOMESTIC", "EXPGR": "DOMESTIC", "EXPHK": "DOMESTIC", "EXPHR": "DOMESTIC", "EXPHU": "DOMESTIC", "EXPID": "DOMESTIC", "EXPIE": "DOMESTIC", "EXPIL": "DOMESTIC", "EXPIN": "DOMESTIC", "EXPIR": "DOMESTIC", "EXPIT": "DOMESTIC", "EXPJO": "DOMESTIC", "EXPJP": "DOMESTIC", "EXPKR": "DOMESTIC", "EXPKZ": "DOMESTIC", "EXPLT": "DOMESTIC", "EXPLU": "DOMESTIC", "EXPLV": "DOMESTIC", "EXPMA": "DOMESTIC", "EXPME": "DOMESTIC", "EXPMK": "DOMESTIC", "EXPMN": "DOMESTIC", "EXPMONAR": "USD", "EXPMONAU": "DOMESTIC", "EXPMONBD": "USD", "EXPMONBE": "DOMESTIC", "EXPMONBG": "DOMESTIC", "EXPMONBR": "USD", "EXPMONCA": "DOMESTIC", "EXPMONCL": "USD", "EXPMONCN": "USD", "EXPMONCO": "USD", "EXPMONCR": "USD", "EXPMONCZ": "DOMESTIC", "EXPMONDE": "DOMESTIC", "EXPMONDK": "DOMESTIC", "EXPMONEE": "DOMESTIC", "EXPMONEG": "USD", "EXPMONES": "DOMESTIC", "EXPMONEU": "DOMESTIC", "EXPMONFI": "DOMESTIC", "EXPMONFR": "DOMESTIC", "EXPMONGR": "DOMESTIC", "EXPMONHK": "DOMESTIC", "EXPMONHR": "DOMESTIC", "EXPMONHU": "DOMESTIC", "EXPMONID": "USD", "EXPMONIN": "USD", "EXPMONIT": "DOMESTIC", "EXPMONJP": "DOMESTIC", "EXPMONKH": "DOMESTIC", "EXPMONKR": "USD", "EXPMONKZ": "USD", "EXPMONLT": "DOMESTIC", "EXPMONLU": "DOMESTIC", "EXPMONLV": "DOMESTIC", "EXPMONMK": "DOMESTIC", "EXPMONMT": "DOMESTIC", "EXPMONMX": "USD", "EXPMONMY": "DOMESTIC", "EXPMONNL": "DOMESTIC", "EXPMONNP": "USD", "EXPMONPH": "USD", "EXPMONPK": "DOMESTIC", "EXPMONPL": "DOMESTIC", "EXPMONPT": "DOMESTIC", "EXPMONQA": "DOMESTIC", "EXPMONRO": "DOMESTIC", "EXPMONRS": "DOMESTIC", "EXPMONRU": "USD", "EXPMONSA": "DOMESTIC", "EXPMONSE": "DOMESTIC", "EXPMONSG": "DOMESTIC", "EXPMONSI": "DOMESTIC", "EXPMONSK": "DOMESTIC", "EXPMONTH": "USD", "EXPMONTR": "USD", "EXPMONTW": "USD", "EXPMONUA": "USD", "EXPMONUK": "DOMESTIC", "EXPMONUS": "DOMESTIC", "EXPMONUY": "USD", "EXPMONVN": "USD", "EXPMONZA": "DOMESTIC", "EXPMT": "DOMESTIC", "EXPMX": "DOMESTIC", "EXPMY": "DOMESTIC", "EXPNG": "DOMESTIC", "EXPNL": "DOMESTIC", "EXPNO": "DOMESTIC", "EXPNZ": "DOMESTIC", "EXPPE": "DOMESTIC", "EXPPH": "DOMESTIC", "EXPPK": "DOMESTIC", "EXPPL": "DOMESTIC", "EXPPT": "DOMESTIC", "EXPQA": "DOMESTIC", "EXPRO": "DOMESTIC", "EXPRS": "DOMESTIC", "EXPRU": "DOMESTIC", "EXPRW": "DOMESTIC", "EXPSA": "DOMESTIC", "EXPSE": "DOMESTIC", "EXPSG": "DOMESTIC", "EXPSI": "DOMESTIC", "EXPSK": "DOMESTIC", "EXPSV": "DOMESTIC", "EXPTH": "DOMESTIC", "EXPTR": "DOMESTIC", "EXPTW": "DOMESTIC", "EXPUA": "DOMESTIC", "EXPUK": "DOMESTIC", "EXPUS": "DOMESTIC", "EXPZA": "DOMESTIC", "GASDEMAL": "CUBIC METERS", "GASDEMAT": "CUBIC METERS", "GASDEMAU": "CUBIC METERS", "GASDEMAZ": "CUBIC METERS", "GASDEMBE": "CUBIC METERS", "GASDEMBG": "CUBIC METERS", "GASDEMBH": "CUBIC METERS", "GASDEMBN": "CUBIC METERS", "GASDEMBO": "CUBIC METERS", "GASDEMBY": "CUBIC METERS", "GASDEMCA": "CUBIC METERS", "GASDEMCH": "CUBIC METERS", "GASDEMCL": "CUBIC METERS", "GASDEMCZ": "CUBIC METERS", "GASDEMDE": "CUBIC METERS", "GASDEMDK": "CUBIC METERS", "GASDEMDZ": "CUBIC METERS", "GASDEMEC": "CUBIC METERS", "GASDEMEE": "CUBIC METERS", "GASDEMEG": "CUBIC METERS", "GASDEMES": "CUBIC METERS", "GASDEMFI": "CUBIC METERS", "GASDEMFR": "CUBIC METERS", "GASDEMGE": "CUBIC METERS", "GASDEMGQ": "CUBIC METERS", "GASDEMGR": "CUBIC METERS", "GASDEMHK": "CUBIC METERS", "GASDEMHR": "CUBIC METERS", "GASDEMHU": "CUBIC METERS", "GASDEMID": "CUBIC METERS", "GASDEMIE": "CUBIC METERS", "GASDEMIN": "CUBIC METERS", "GASDEMIT": "CUBIC METERS", "GASDEMJP": "CUBIC METERS", "GASDEMKR": "CUBIC METERS", "GASDEMKW": "CUBIC METERS", "GASDEMLT": "CUBIC METERS", "GASDEMLU": "CUBIC METERS", "GASDEMLV": "CUBIC METERS", "GASDEMLY": "CUBIC METERS", "GASDEMMA": "CUBIC METERS", "GASDEMMD": "CUBIC METERS", "GASDEMMK": "CUBIC METERS", "GASDEMMT": "CUBIC METERS", "GASDEMMX": "CUBIC METERS", "GASDEMNG": "CUBIC METERS", "GASDEMNL": "CUBIC METERS", "GASDEMNO": "CUBIC METERS", "GASDEMNZ": "CUBIC METERS", "GASDEMPE": "CUBIC METERS", "GASDEMPH": "CUBIC METERS", "GASDEMPL": "CUBIC METERS", "GASDEMPT": "CUBIC METERS", "GASDEMQA": "CUBIC METERS", "GASDEMRO": "CUBIC METERS", "GASDEMSE": "CUBIC METERS", "GASDEMSI": "CUBIC METERS", "GASDEMSK": "CUBIC METERS", "GASDEMTN": "CUBIC METERS", "GASDEMTR": "CUBIC METERS", "GASDEMTT": "CUBIC METERS", "GASDEMTW": "CUBIC METERS", "GASDEMUA": "CUBIC METERS", "GASDEMUK": "CUBIC METERS", "GASDEMUS": "CUBIC METERS", "GASDEMVE": "CUBIC METERS", "GASDEMZA": "CUBIC METERS", "GASODEMAE": "BARRELS PER DAY", "GASODEMAL": "BARRELS PER DAY", "GASODEMAM": "BARRELS PER DAY", "GASODEMAO": "BARRELS PER DAY", "GASODEMAR": "BARRELS PER DAY", "GASODEMAT": "BARRELS PER DAY", "GASODEMAU": "BARRELS PER DAY", "GASODEMAZ": "BARRELS PER DAY", "GASODEMBB": "BARRELS PER DAY", "GASODEMBD": "BARRELS PER DAY", "GASODEMBE": "BARRELS PER DAY", "GASODEMBG": "BARRELS PER DAY", "GASODEMBH": "BARRELS PER DAY", "GASODEMBM": "BARRELS PER DAY", "GASODEMBN": "BARRELS PER DAY", "GASODEMBO": "BARRELS PER DAY", "GASODEMBR": "BARRELS PER DAY", "GASODEMBY": "BARRELS PER DAY", "GASODEMBZ": "BARRELS PER DAY", "GASODEMCA": "BARRELS PER DAY", "GASODEMCH": "BARRELS PER DAY", "GASODEMCL": "BARRELS PER DAY", "GASODEMCN": "BARRELS PER DAY", "GASODEMCO": "BARRELS PER DAY", "GASODEMCR": "BARRELS PER DAY", "GASODEMCU": "BARRELS PER DAY", "GASODEMCY": "BARRELS PER DAY", "GASODEMCZ": "BARRELS PER DAY", "GASODEMDE": "BARRELS PER DAY", "GASODEMDK": "BARRELS PER DAY", "GASODEMDO": "BARRELS PER DAY", "GASODEMDZ": "BARRELS PER DAY", "GASODEMEC": "BARRELS PER DAY", "GASODEMEE": "BARRELS PER DAY", "GASODEMEG": "BARRELS PER DAY", "GASODEMES": "BARRELS PER DAY", "GASODEMFI": "BARRELS PER DAY", "GASODEMFR": "BARRELS PER DAY", "GASODEMGA": "BARRELS PER DAY", "GASODEMGD": "BARRELS PER DAY", "GASODEMGE": "BARRELS PER DAY", "GASODEMGM": "BARRELS PER DAY", "GASODEMGQ": "BARRELS PER DAY", "GASODEMGR": "BARRELS PER DAY", "GASODEMGT": "BARRELS PER DAY", "GASODEMGY": "BARRELS PER DAY", "GASODEMHK": "BARRELS PER DAY", "GASODEMHN": "BARRELS PER DAY", "GASODEMHR": "BARRELS PER DAY", "GASODEMHT": "BARRELS PER DAY", "GASODEMHU": "BARRELS PER DAY", "GASODEMID": "BARRELS PER DAY", "GASODEMIE": "BARRELS PER DAY", "GASODEMIN": "BARRELS PER DAY", "GASODEMIQ": "BARRELS PER DAY", "GASODEMIR": "BARRELS PER DAY", "GASODEMIS": "BARRELS PER DAY", "GASODEMIT": "BARRELS PER DAY", "GASODEMJM": "BARRELS PER DAY", "GASODEMJP": "BARRELS PER DAY", "GASODEMKR": "BARRELS PER DAY", "GASODEMKW": "BARRELS PER DAY", "GASODEMKZ": "BARRELS PER DAY", "GASODEMLT": "BARRELS PER DAY", "GASODEMLU": "BARRELS PER DAY", "GASODEMLV": "BARRELS PER DAY", "GASODEMLY": "BARRELS PER DAY", "GASODEMMA": "BARRELS PER DAY", "GASODEMMD": "BARRELS PER DAY", "GASODEMMK": "BARRELS PER DAY", "GASODEMMM": "BARRELS PER DAY", "GASODEMMT": "BARRELS PER DAY", "GASODEMMU": "BARRELS PER DAY", "GASODEMMX": "BARRELS PER DAY", "GASODEMMY": "BARRELS PER DAY", "GASODEMNE": "BARRELS PER DAY", "GASODEMNG": "BARRELS PER DAY", "GASODEMNI": "BARRELS PER DAY", "GASODEMNL": "BARRELS PER DAY", "GASODEMNO": "BARRELS PER DAY", "GASODEMNP": "BARRELS PER DAY", "GASODEMNZ": "BARRELS PER DAY", "GASODEMOM": "BARRELS PER DAY", "GASODEMPA": "BARRELS PER DAY", "GASODEMPE": "BARRELS PER DAY", "GASODEMPG": "BARRELS PER DAY", "GASODEMPH": "BARRELS PER DAY", "GASODEMPL": "BARRELS PER DAY", "GASODEMPT": "BARRELS PER DAY", "GASODEMPY": "BARRELS PER DAY", "GASODEMQA": "BARRELS PER DAY", "GASODEMRO": "BARRELS PER DAY", "GASODEMRU": "BARRELS PER DAY", "GASODEMSA": "BARRELS PER DAY", "GASODEMSD": "BARRELS PER DAY", "GASODEMSE": "BARRELS PER DAY", "GASODEMSG": "BARRELS PER DAY", "GASODEMSI": "BARRELS PER DAY", "GASODEMSK": "BARRELS PER DAY", "GASODEMSR": "BARRELS PER DAY", "GASODEMSV": "BARRELS PER DAY", "GASODEMSY": "BARRELS PER DAY", "GASODEMSZ": "BARRELS PER DAY", "GASODEMTH": "BARRELS PER DAY", "GASODEMTJ": "BARRELS PER DAY", "GASODEMTN": "BARRELS PER DAY", "GASODEMTR": "BARRELS PER DAY", "GASODEMTT": "BARRELS PER DAY", "GASODEMTW": "BARRELS PER DAY", "GASODEMUA": "BARRELS PER DAY", "GASODEMUK": "BARRELS PER DAY", "GASODEMUS": "BARRELS PER DAY", "GASODEMUY": "BARRELS PER DAY", "GASODEMVE": "BARRELS PER DAY", "GASODEMVN": "BARRELS PER DAY", "GASODEMYE": "BARRELS PER DAY", "GASODEMZA": "BARRELS PER DAY", "GASOPRODAE": "BARRELS PER DAY", "GASOPRODAL": "BARRELS PER DAY", "GASOPRODAM": "BARRELS PER DAY", "GASOPRODAO": "BARRELS PER DAY", "GASOPRODAR": "BARRELS PER DAY", "GASOPRODAT": "BARRELS PER DAY", "GASOPRODAU": "BARRELS PER DAY", "GASOPRODAZ": "BARRELS PER DAY", "GASOPRODBB": "BARRELS PER DAY", "GASOPRODBD": "BARRELS PER DAY", "GASOPRODBE": "BARRELS PER DAY", "GASOPRODBG": "BARRELS PER DAY", "GASOPRODBH": "BARRELS PER DAY", "GASOPRODBM": "BARRELS PER DAY", "GASOPRODBN": "BARRELS PER DAY", "GASOPRODBO": "BARRELS PER DAY", "GASOPRODBR": "BARRELS PER DAY", "GASOPRODBY": "BARRELS PER DAY", "GASOPRODBZ": "BARRELS PER DAY", "GASOPRODCA": "BARRELS PER DAY", "GASOPRODCH": "BARRELS PER DAY", "GASOPRODCL": "BARRELS PER DAY", "GASOPRODCN": "BARRELS PER DAY", "GASOPRODCO": "BARRELS PER DAY", "GASOPRODCR": "BARRELS PER DAY", "GASOPRODCU": "BARRELS PER DAY", "GASOPRODCY": "BARRELS PER DAY", "GASOPRODCZ": "BARRELS PER DAY", "GASOPRODDE": "BARRELS PER DAY", "GASOPRODDK": "BARRELS PER DAY", "GASOPRODDO": "BARRELS PER DAY", "GASOPRODDZ": "BARRELS PER DAY", "GASOPRODEC": "BARRELS PER DAY", "GASOPRODEE": "BARRELS PER DAY", "GASOPRODEG": "BARRELS PER DAY", "GASOPRODES": "BARRELS PER DAY", "GASOPRODFI": "BARRELS PER DAY", "GASOPRODFR": "BARRELS PER DAY", "GASOPRODGA": "BARRELS PER DAY", "GASOPRODGD": "BARRELS PER DAY", "GASOPRODGE": "BARRELS PER DAY", "GASOPRODGM": "BARRELS PER DAY", "GASOPRODGQ": "BARRELS PER DAY", "GASOPRODGR": "BARRELS PER DAY", "GASOPRODGT": "BARRELS PER DAY", "GASOPRODGY": "BARRELS PER DAY", "GASOPRODHK": "BARRELS PER DAY", "GASOPRODHN": "BARRELS PER DAY", "GASOPRODHR": "BARRELS PER DAY", "GASOPRODHT": "BARRELS PER DAY", "GASOPRODHU": "BARRELS PER DAY", "GASOPRODID": "BARRELS PER DAY", "GASOPRODIE": "BARRELS PER DAY", "GASOPRODIN": "BARRELS PER DAY", "GASOPRODIQ": "BARRELS PER DAY", "GASOPRODIR": "BARRELS PER DAY", "GASOPRODIS": "BARRELS PER DAY", "GASOPRODIT": "BARRELS PER DAY", "GASOPRODJM": "BARRELS PER DAY", "GASOPRODJP": "BARRELS PER DAY", "GASOPRODKR": "BARRELS PER DAY", "GASOPRODKW": "BARRELS PER DAY", "GASOPRODKZ": "BARRELS PER DAY", "GASOPRODLT": "BARRELS PER DAY", "GASOPRODLU": "BARRELS PER DAY", "GASOPRODLV": "BARRELS PER DAY", "GASOPRODLY": "BARRELS PER DAY", "GASOPRODMA": "BARRELS PER DAY", "GASOPRODMD": "BARRELS PER DAY", "GASOPRODMK": "BARRELS PER DAY", "GASOPRODMM": "BARRELS PER DAY", "GASOPRODMT": "BARRELS PER DAY", "GASOPRODMU": "BARRELS PER DAY", "GASOPRODMX": "BARRELS PER DAY", "GASOPRODMY": "BARRELS PER DAY", "GASOPRODNE": "BARRELS PER DAY", "GASOPRODNG": "BARRELS PER DAY", "GASOPRODNI": "BARRELS PER DAY", "GASOPRODNL": "BARRELS PER DAY", "GASOPRODNO": "BARRELS PER DAY", "GASOPRODNP": "BARRELS PER DAY", "GASOPRODNZ": "BARRELS PER DAY", "GASOPRODOM": "BARRELS PER DAY", "GASOPRODPA": "BARRELS PER DAY", "GASOPRODPE": "BARRELS PER DAY", "GASOPRODPG": "BARRELS PER DAY", "GASOPRODPH": "BARRELS PER DAY", "GASOPRODPL": "BARRELS PER DAY", "GASOPRODPT": "BARRELS PER DAY", "GASOPRODPY": "BARRELS PER DAY", "GASOPRODQA": "BARRELS PER DAY", "GASOPRODRO": "BARRELS PER DAY", "GASOPRODRU": "BARRELS PER DAY", "GASOPRODSA": "BARRELS PER DAY", "GASOPRODSD": "BARRELS PER DAY", "GASOPRODSE": "BARRELS PER DAY", "GASOPRODSG": "BARRELS PER DAY", "GASOPRODSI": "BARRELS PER DAY", "GASOPRODSK": "BARRELS PER DAY", "GASOPRODSR": "BARRELS PER DAY", "GASOPRODSV": "BARRELS PER DAY", "GASOPRODSY": "BARRELS PER DAY", "GASOPRODSZ": "BARRELS PER DAY", "GASOPRODTH": "BARRELS PER DAY", "GASOPRODTJ": "BARRELS PER DAY", "GASOPRODTN": "BARRELS PER DAY", "GASOPRODTR": "BARRELS PER DAY", "GASOPRODTT": "BARRELS PER DAY", "GASOPRODTW": "BARRELS PER DAY", "GASOPRODUA": "BARRELS PER DAY", "GASOPRODUK": "BARRELS PER DAY", "GASOPRODUS": "BARRELS PER DAY", "GASOPRODUY": "BARRELS PER DAY", "GASOPRODVE": "BARRELS PER DAY", "GASOPRODVN": "BARRELS PER DAY", "GASOPRODYE": "BARRELS PER DAY", "GASOPRODZA": "BARRELS PER DAY", "GASPRODAL": "CUBIC METERS", "GASPRODAT": "CUBIC METERS", "GASPRODAU": "CUBIC METERS", "GASPRODAZ": "CUBIC METERS", "GASPRODBE": "CUBIC METERS", "GASPRODBG": "CUBIC METERS", "GASPRODBH": "CUBIC METERS", "GASPRODBN": "CUBIC METERS", "GASPRODBO": "CUBIC METERS", "GASPRODBY": "CUBIC METERS", "GASPRODCA": "CUBIC METERS", "GASPRODCH": "CUBIC METERS", "GASPRODCL": "CUBIC METERS", "GASPRODCN": "CUBIC METERS", "GASPRODCZ": "CUBIC METERS", "GASPRODDE": "CUBIC METERS", "GASPRODDK": "CUBIC METERS", "GASPRODDZ": "CUBIC METERS", "GASPRODEC": "CUBIC METERS", "GASPRODEE": "CUBIC METERS", "GASPRODEG": "CUBIC METERS", "GASPRODES": "CUBIC METERS", "GASPRODFI": "CUBIC METERS", "GASPRODFR": "CUBIC METERS", "GASPRODGA": "CUBIC METERS", "GASPRODGE": "CUBIC METERS", "GASPRODGQ": "CUBIC METERS", "GASPRODGR": "CUBIC METERS", "GASPRODHK": "CUBIC METERS", "GASPRODHR": "CUBIC METERS", "GASPRODHU": "CUBIC METERS", "GASPRODID": "CUBIC METERS", "GASPRODIE": "CUBIC METERS", "GASPRODIN": "CUBIC METERS", "GASPRODIQ": "CUBIC METERS", "GASPRODIT": "CUBIC METERS", "GASPRODJP": "CUBIC METERS", "GASPRODKR": "CUBIC METERS", "GASPRODKW": "CUBIC METERS", "GASPRODKZ": "CUBIC METERS", "GASPRODLT": "CUBIC METERS", "GASPRODLU": "CUBIC METERS", "GASPRODLV": "CUBIC METERS", "GASPRODLY": "CUBIC METERS", "GASPRODMA": "CUBIC METERS", "GASPRODMD": "CUBIC METERS", "GASPRODMK": "CUBIC METERS", "GASPRODMT": "CUBIC METERS", "GASPRODMX": "CUBIC METERS", "GASPRODMY": "CUBIC METERS", "GASPRODNG": "CUBIC METERS", "GASPRODNL": "CUBIC METERS", "GASPRODNO": "CUBIC METERS", "GASPRODNZ": "CUBIC METERS", "GASPRODPE": "CUBIC METERS", "GASPRODPG": "CUBIC METERS", "GASPRODPH": "CUBIC METERS", "GASPRODPL": "CUBIC METERS", "GASPRODPT": "CUBIC METERS", "GASPRODQA": "CUBIC METERS", "GASPRODRO": "CUBIC METERS", "GASPRODRU": "CUBIC METERS", "GASPRODSE": "CUBIC METERS", "GASPRODSG": "CUBIC METERS", "GASPRODSI": "CUBIC METERS", "GASPRODSK": "CUBIC METERS", "GASPRODTH": "CUBIC METERS", "GASPRODTN": "CUBIC METERS", "GASPRODTR": "CUBIC METERS", "GASPRODTT": "CUBIC METERS", "GASPRODTW": "CUBIC METERS", "GASPRODUA": "CUBIC METERS", "GASPRODUK": "CUBIC METERS", "GASPRODUS": "CUBIC METERS", "GASPRODVE": "CUBIC METERS", "GASPRODVN": "CUBIC METERS", "GASPRODZA": "CUBIC METERS", "GBALAR": "DOMESTIC", "GBALAT": "DOMESTIC", "GBALAU": "DOMESTIC", "GBALAZ": "DOMESTIC", "GBALBD": "DOMESTIC", "GBALBE": "DOMESTIC", "GBALBG": "DOMESTIC", "GBALBR": "DOMESTIC", "GBALCA": "DOMESTIC", "GBALCH": "DOMESTIC", "GBALCN": "DOMESTIC", "GBALCO": "DOMESTIC", "GBALCR": "DOMESTIC", "GBALCY": "DOMESTIC", "GBALCZ": "DOMESTIC", "GBALDE": "DOMESTIC", "GBALDK": "DOMESTIC", "GBALEE": "DOMESTIC", "GBALES": "DOMESTIC", "GBALEU": "DOMESTIC", "GBALFI": "DOMESTIC", "GBALFR": "DOMESTIC", "GBALGR": "DOMESTIC", "GBALHK": "DOMESTIC", "GBALHR": "DOMESTIC", "GBALHU": "DOMESTIC", "GBALID": "DOMESTIC", "GBALIE": "DOMESTIC", "GBALIL": "DOMESTIC", "GBALIN": "DOMESTIC", "GBALIS": "DOMESTIC", "GBALIT": "DOMESTIC", "GBALJP": "DOMESTIC", "GBALKH": "DOMESTIC", "GBALKR": "DOMESTIC", "GBALKZ": "DOMESTIC", "GBALLT": "DOMESTIC", "GBALLU": "DOMESTIC", "GBALLV": "DOMESTIC", "GBALMO": "DOMESTIC", "GBALMT": "DOMESTIC", "GBALMX": "DOMESTIC", "GBALMY": "DOMESTIC", "GBALNL": "DOMESTIC", "GBALNO": "DOMESTIC", "GBALPE": "DOMESTIC", "GBALPH": "DOMESTIC", "GBALPL": "DOMESTIC", "GBALPT": "DOMESTIC", "GBALRO": "DOMESTIC", "GBALRU": "DOMESTIC", "GBALSA": "DOMESTIC", "GBALSE": "DOMESTIC", "GBALSG": "DOMESTIC", "GBALSI": "DOMESTIC", "GBALSK": "DOMESTIC", "GBALTH": "DOMESTIC", "GBALTR": "DOMESTIC", "GBALTW": "DOMESTIC", "GBALUA": "DOMESTIC", "GBALUK": "DOMESTIC", "GBALUS": "DOMESTIC", "GBALZA": "DOMESTIC", "GCFAT": "DOMESTIC", "GCFBE": "DOMESTIC", "GCFBG": "DOMESTIC", "GCFBR": "DOMESTIC", "GCFBY": "DOMESTIC", "GCFCH": "DOMESTIC", "GCFCN": "DOMESTIC", "GCFCY": "DOMESTIC", "GCFCZ": "DOMESTIC", "GCFDE": "DOMESTIC", "GCFDK": "DOMESTIC", "GCFEA": "DOMESTIC", "GCFEE": "DOMESTIC", "GCFES": "DOMESTIC", "GCFEU": "DOMESTIC", "GCFFI": "DOMESTIC", "GCFFR": "DOMESTIC", "GCFGR": "DOMESTIC", "GCFHR": "DOMESTIC", "GCFHU": "DOMESTIC", "GCFIE": "DOMESTIC", "GCFIT": "DOMESTIC", "GCFKR": "DOMESTIC", "GCFLT": "DOMESTIC", "GCFLU": "DOMESTIC", "GCFLV": "DOMESTIC", "GCFME": "DOMESTIC", "GCFMK": "DOMESTIC", "GCFMT": "DOMESTIC", "GCFNL": "DOMESTIC", "GCFNO": "DOMESTIC", "GCFNZ": "DOMESTIC", "GCFPE": "DOMESTIC", "GCFPL": "DOMESTIC", "GCFPT": "DOMESTIC", "GCFQA": "DOMESTIC", "GCFRO": "DOMESTIC", "GCFRS": "DOMESTIC", "GCFRU": "DOMESTIC", "GCFSE": "DOMESTIC", "GCFSI": "DOMESTIC", "GCFSK": "DOMESTIC", "GCFTH": "DOMESTIC", "GCFUA": "DOMESTIC", "GCFUK": "DOMESTIC", "GDEBTAR": "USD", "GDEBTAT": "DOMESTIC", "GDEBTAU": "DOMESTIC", "GDEBTBE": "DOMESTIC", "GDEBTBG": "DOMESTIC", "GDEBTBR": "DOMESTIC", "GDEBTCA": "DOMESTIC", "GDEBTCL": "DOMESTIC", "GDEBTCN": "DOMESTIC", "GDEBTCO": "DOMESTIC", "GDEBTCR": "DOMESTIC", "GDEBTCY": "DOMESTIC", "GDEBTCZ": "DOMESTIC", "GDEBTDE": "DOMESTIC", "GDEBTDK": "DOMESTIC", "GDEBTEE": "DOMESTIC", "GDEBTES": "DOMESTIC", "GDEBTEU": "DOMESTIC", "GDEBTFI": "DOMESTIC", "GDEBTFR": "DOMESTIC", "GDEBTGR": "DOMESTIC", "GDEBTHK": "DOMESTIC", "GDEBTHR": "DOMESTIC", "GDEBTHU": "DOMESTIC", "GDEBTID": "DOMESTIC", "GDEBTIE": "DOMESTIC", "GDEBTIL": "DOMESTIC", "GDEBTIN": "DOMESTIC", "GDEBTIT": "DOMESTIC", "GDEBTJO": "DOMESTIC", "GDEBTJP": "DOMESTIC", "GDEBTKR": "USD", "GDEBTKZ": "USD", "GDEBTLT": "DOMESTIC", "GDEBTLU": "DOMESTIC", "GDEBTLV": "DOMESTIC", "GDEBTMT": "DOMESTIC", "GDEBTMX": "DOMESTIC", "GDEBTMY": "DOMESTIC", "GDEBTNAT": "DOMESTIC", "GDEBTNBE": "DOMESTIC", "GDEBTNBG": "DOMESTIC", "GDEBTNCY": "DOMESTIC", "GDEBTNCZ": "DOMESTIC", "GDEBTNDE": "DOMESTIC", "GDEBTNDK": "DOMESTIC", "GDEBTNEE": "DOMESTIC", "GDEBTNES": "DOMESTIC", "GDEBTNEU": "DOMESTIC", "GDEBTNFI": "DOMESTIC", "GDEBTNFR": "DOMESTIC", "GDEBTNGR": "DOMESTIC", "GDEBTNHR": "DOMESTIC", "GDEBTNHU": "DOMESTIC", "GDEBTNIE": "DOMESTIC", "GDEBTNIT": "DOMESTIC", "GDEBTNL": "DOMESTIC", "GDEBTNLT": "DOMESTIC", "GDEBTNLU": "DOMESTIC", "GDEBTNLV": "DOMESTIC", "GDEBTNMT": "DOMESTIC", "GDEBTNNL": "DOMESTIC", "GDEBTNNO": "DOMESTIC", "GDEBTNO": "DOMESTIC", "GDEBTNPL": "DOMESTIC", "GDEBTNPT": "DOMESTIC", "GDEBTNRO": "DOMESTIC", "GDEBTNSE": "DOMESTIC", "GDEBTNSI": "DOMESTIC", "GDEBTNSK": "DOMESTIC", "GDEBTNUK": "DOMESTIC", "GDEBTPH": "DOMESTIC", "GDEBTPK": "DOMESTIC", "GDEBTPL": "DOMESTIC", "GDEBTPT": "DOMESTIC", "GDEBTQA": "DOMESTIC", "GDEBTRO": "DOMESTIC", "GDEBTRU": "DOMESTIC", "GDEBTSA": "DOMESTIC", "GDEBTSE": "DOMESTIC", "GDEBTSG": "DOMESTIC", "GDEBTSI": "DOMESTIC", "GDEBTSK": "DOMESTIC", "GDEBTSV": "DOMESTIC", "GDEBTTH": "DOMESTIC", "GDEBTTR": "DOMESTIC", "GDEBTTW": "DOMESTIC", "GDEBTUK": "DOMESTIC", "GDEBTUS": "DOMESTIC", "GDEBTZA": "DOMESTIC", "GDPAE": "DOMESTIC", "GDPAL": "DOMESTIC", "GDPAR": "DOMESTIC", "GDPAT": "DOMESTIC", "GDPAU": "DOMESTIC", "GDPAZ": "DOMESTIC", "GDPBD": "DOMESTIC", "GDPBE": "DOMESTIC", "GDPBG": "DOMESTIC", "GDPBR": "DOMESTIC", "GDPBW": "DOMESTIC", "GDPBY": "DOMESTIC", "GDPCA": "DOMESTIC", "GDPCH": "DOMESTIC", "GDPCL": "DOMESTIC", "GDPCM": "DOMESTIC", "GDPCN": "DOMESTIC", "GDPCO": "DOMESTIC", "GDPCR": "DOMESTIC", "GDPCY": "DOMESTIC", "GDPCZ": "DOMESTIC", "GDPDE": "DOMESTIC", "GDPDEFLK": "INDEX", "GDPDK": "DOMESTIC", "GDPEA": "DOMESTIC", "GDPEE": "DOMESTIC", "GDPEG": "DOMESTIC", "GDPES": "DOMESTIC", "GDPEU": "DOMESTIC", "GDPFI": "DOMESTIC", "GDPFR": "DOMESTIC", "GDPGR": "DOMESTIC", "GDPHK": "DOMESTIC", "GDPHN": "DOMESTIC", "GDPHR": "DOMESTIC", "GDPHU": "DOMESTIC", "GDPID": "DOMESTIC", "GDPIE": "DOMESTIC", "GDPIL": "DOMESTIC", "GDPIN": "DOMESTIC", "GDPIR": "DOMESTIC", "GDPIT": "DOMESTIC", "GDPJO": "DOMESTIC", "GDPJP": "DOMESTIC", "GDPKH": "DOMESTIC", "GDPKR": "DOMESTIC", "GDPKZ": "DOMESTIC", "GDPLA": "DOMESTIC", "GDPLT": "DOMESTIC", "GDPLU": "DOMESTIC", "GDPLV": "DOMESTIC", "GDPMA": "DOMESTIC", "GDPME": "DOMESTIC", "GDPMK": "DOMESTIC", "GDPMO": "DOMESTIC", "GDPMT": "DOMESTIC", "GDPMX": "DOMESTIC", "GDPMY": "DOMESTIC", "GDPNG": "DOMESTIC", "GDPNL": "DOMESTIC", "GDPNO": "DOMESTIC", "GDPNZ": "DOMESTIC", "GDPPA": "DOMESTIC", "GDPPCAL": "DOMESTIC", "GDPPCAT": "DOMESTIC", "GDPPCAU": "DOMESTIC", "GDPPCBE": "DOMESTIC", "GDPPCBG": "DOMESTIC", "GDPPCBR": "INDEX", "GDPPCCH": "DOMESTIC", "GDPPCCN": "DOMESTIC", "GDPPCCY": "DOMESTIC", "GDPPCCZ": "DOMESTIC", "GDPPCDE": "DOMESTIC", "GDPPCDK": "DOMESTIC", "GDPPCEA": "DOMESTIC", "GDPPCEE": "DOMESTIC", "GDPPCES": "DOMESTIC", "GDPPCEU": "DOMESTIC", "GDPPCFI": "DOMESTIC", "GDPPCFR": "DOMESTIC", "GDPPCGR": "DOMESTIC", "GDPPCHR": "DOMESTIC", "GDPPCHU": "DOMESTIC", "GDPPCIE": "DOMESTIC", "GDPPCIS": "DOMESTIC", "GDPPCIT": "DOMESTIC", "GDPPCKH": "DOMESTIC", "GDPPCKZ": "DOMESTIC", "GDPPCLI": "DOMESTIC", "GDPPCLT": "DOMESTIC", "GDPPCLU": "DOMESTIC", "GDPPCLV": "DOMESTIC", "GDPPCME": "DOMESTIC", "GDPPCMK": "DOMESTIC", "GDPPCMT": "DOMESTIC", "GDPPCNL": "DOMESTIC", "GDPPCNO": "DOMESTIC", "GDPPCNZ": "DOMESTIC", "GDPPCPL": "DOMESTIC", "GDPPCPT": "DOMESTIC", "GDPPCRO": "DOMESTIC", "GDPPCRS": "DOMESTIC", "GDPPCSE": "DOMESTIC", "GDPPCSG": "DOMESTIC", "GDPPCSI": "DOMESTIC", "GDPPCSK": "DOMESTIC", "GDPPCTR": "DOMESTIC", "GDPPCTW": "DOMESTIC", "GDPPCUS": "DOMESTIC", "GDPPE": "DOMESTIC", "GDPPH": "DOMESTIC", "GDPPK": "DOMESTIC", "GDPPL": "DOMESTIC", "GDPPT": "DOMESTIC", "GDPQA": "DOMESTIC", "GDPRO": "DOMESTIC", "GDPRS": "DOMESTIC", "GDPRU": "DOMESTIC", "GDPRW": "DOMESTIC", "GDPSA": "DOMESTIC", "GDPSE": "DOMESTIC", "GDPSG": "DOMESTIC", "GDPSI": "DOMESTIC", "GDPSK": "DOMESTIC", "GDPSV": "DOMESTIC", "GDPTH": "DOMESTIC", "GDPTN": "DOMESTIC", "GDPTR": "DOMESTIC", "GDPTW": "DOMESTIC", "GDPUA": "DOMESTIC", "GDPUK": "DOMESTIC", "GDPUS": "DOMESTIC", "GDPUZ": "DOMESTIC", "GDPVN": "DOMESTIC", "GDPZA": "DOMESTIC", "GFCFAE": "DOMESTIC", "GFCFAL": "DOMESTIC", "GFCFAR": "DOMESTIC", "GFCFAT": "DOMESTIC", "GFCFAU": "DOMESTIC", "GFCFAZ": "DOMESTIC", "GFCFBD": "DOMESTIC", "GFCFBE": "DOMESTIC", "GFCFBG": "DOMESTIC", "GFCFBR": "DOMESTIC", "GFCFBW": "DOMESTIC", "GFCFBY": "DOMESTIC", "GFCFCA": "DOMESTIC", "GFCFCH": "DOMESTIC", "GFCFCL": "DOMESTIC", "GFCFCM": "DOMESTIC", "GFCFCN": "INDEX", "GFCFCO": "DOMESTIC", "GFCFCY": "DOMESTIC", "GFCFCZ": "DOMESTIC", "GFCFDE": "DOMESTIC", "GFCFDK": "DOMESTIC", "GFCFEA": "DOMESTIC", "GFCFEE": "DOMESTIC", "GFCFES": "DOMESTIC", "GFCFEU": "DOMESTIC", "GFCFFI": "DOMESTIC", "GFCFFR": "DOMESTIC", "GFCFGR": "DOMESTIC", "GFCFHK": "DOMESTIC", "GFCFHR": "DOMESTIC", "GFCFHU": "DOMESTIC", "GFCFID": "DOMESTIC", "GFCFIE": "DOMESTIC", "GFCFIL": "DOMESTIC", "GFCFIN": "DOMESTIC", "GFCFIR": "DOMESTIC", "GFCFIT": "DOMESTIC", "GFCFJP": "DOMESTIC", "GFCFKR": "DOMESTIC", "GFCFKZ": "DOMESTIC", "GFCFLT": "DOMESTIC", "GFCFLU": "DOMESTIC", "GFCFLV": "DOMESTIC", "GFCFMA": "DOMESTIC", "GFCFME": "DOMESTIC", "GFCFMN": "DOMESTIC", "GFCFMT": "DOMESTIC", "GFCFMX": "DOMESTIC", "GFCFMY": "DOMESTIC", "GFCFNG": "DOMESTIC", "GFCFNL": "DOMESTIC", "GFCFNO": "DOMESTIC", "GFCFNZ": "DOMESTIC", "GFCFPH": "DOMESTIC", "GFCFPK": "DOMESTIC", "GFCFPL": "DOMESTIC", "GFCFPT": "DOMESTIC", "GFCFRO": "DOMESTIC", "GFCFRS": "DOMESTIC", "GFCFRU": "DOMESTIC", "GFCFSA": "DOMESTIC", "GFCFSE": "DOMESTIC", "GFCFSG": "DOMESTIC", "GFCFSI": "DOMESTIC", "GFCFSK": "DOMESTIC", "GFCFTH": "DOMESTIC", "GFCFTR": "DOMESTIC", "GFCFTW": "DOMESTIC", "GFCFUA": "DOMESTIC", "GFCFUK": "DOMESTIC", "GFCFUS": "DOMESTIC", "GFCFUZ": "DOMESTIC", "GFCFVN": "DOMESTIC", "GFCFZA": "DOMESTIC", "GREVAR": "DOMESTIC", "GREVAT": "DOMESTIC", "GREVAU": "DOMESTIC", "GREVAZ": "DOMESTIC", "GREVBD": "DOMESTIC", "GREVBE": "DOMESTIC", "GREVBG": "DOMESTIC", "GREVBR": "DOMESTIC", "GREVCA": "DOMESTIC", "GREVCH": "DOMESTIC", "GREVCN": "DOMESTIC", "GREVCO": "DOMESTIC", "GREVCR": "DOMESTIC", "GREVCY": "DOMESTIC", "GREVCZ": "DOMESTIC", "GREVDE": "DOMESTIC", "GREVDK": "DOMESTIC", "GREVEE": "DOMESTIC", "GREVES": "DOMESTIC", "GREVEU": "DOMESTIC", "GREVFI": "DOMESTIC", "GREVFR": "DOMESTIC", "GREVHK": "DOMESTIC", "GREVHR": "DOMESTIC", "GREVHU": "DOMESTIC", "GREVID": "DOMESTIC", "GREVIE": "DOMESTIC", "GREVIL": "DOMESTIC", "GREVIN": "DOMESTIC", "GREVJP": "DOMESTIC", "GREVKH": "DOMESTIC", "GREVKR": "DOMESTIC", "GREVKZ": "DOMESTIC", "GREVLT": "DOMESTIC", "GREVLU": "DOMESTIC", "GREVLV": "DOMESTIC", "GREVMA": "DOMESTIC", "GREVMO": "DOMESTIC", "GREVMT": "DOMESTIC", "GREVMX": "DOMESTIC", "GREVMY": "DOMESTIC", "GREVNL": "DOMESTIC", "GREVPE": "DOMESTIC", "GREVPH": "DOMESTIC", "GREVPL": "DOMESTIC", "GREVPT": "DOMESTIC", "GREVRO": "DOMESTIC", "GREVRU": "DOMESTIC", "GREVSA": "DOMESTIC", "GREVSE": "DOMESTIC", "GREVSG": "DOMESTIC", "GREVSI": "DOMESTIC", "GREVSK": "DOMESTIC", "GREVSV": "DOMESTIC", "GREVTH": "DOMESTIC", "GREVTR": "DOMESTIC", "GREVTW": "DOMESTIC", "GREVUA": "DOMESTIC", "GREVUK": "DOMESTIC", "GREVUS": "DOMESTIC", "GREVUY": "DOMESTIC", "GREVVN": "DOMESTIC", "GREVZA": "DOMESTIC", "GSPEAR": "DOMESTIC", "GSPEAT": "DOMESTIC", "GSPEAU": "DOMESTIC", "GSPEAZ": "DOMESTIC", "GSPEBD": "DOMESTIC", "GSPEBE": "DOMESTIC", "GSPEBG": "DOMESTIC", "GSPEBR": "DOMESTIC", "GSPECA": "DOMESTIC", "GSPECH": "DOMESTIC", "GSPECN": "DOMESTIC", "GSPECO": "DOMESTIC", "GSPECR": "DOMESTIC", "GSPECY": "DOMESTIC", "GSPECZ": "DOMESTIC", "GSPEDE": "DOMESTIC", "GSPEDK": "DOMESTIC", "GSPEEE": "DOMESTIC", "GSPEES": "DOMESTIC", "GSPEEU": "DOMESTIC", "GSPEFI": "DOMESTIC", "GSPEFR": "DOMESTIC", "GSPEHK": "DOMESTIC", "GSPEHR": "DOMESTIC", "GSPEHU": "DOMESTIC", "GSPEID": "DOMESTIC", "GSPEIE": "DOMESTIC", "GSPEIL": "DOMESTIC", "GSPEIN": "DOMESTIC", "GSPEJP": "DOMESTIC", "GSPEKH": "DOMESTIC", "GSPEKR": "DOMESTIC", "GSPEKZ": "DOMESTIC", "GSPELT": "DOMESTIC", "GSPELU": "DOMESTIC", "GSPELV": "DOMESTIC", "GSPEMO": "DOMESTIC", "GSPEMT": "DOMESTIC", "GSPEMX": "DOMESTIC", "GSPEMY": "DOMESTIC", "GSPENL": "DOMESTIC", "GSPEPE": "DOMESTIC", "GSPEPH": "DOMESTIC", "GSPEPL": "DOMESTIC", "GSPEPT": "DOMESTIC", "GSPERO": "DOMESTIC", "GSPERU": "DOMESTIC", "GSPESA": "DOMESTIC", "GSPESE": "DOMESTIC", "GSPESG": "DOMESTIC", "GSPESI": "DOMESTIC", "GSPESK": "DOMESTIC", "GSPETH": "DOMESTIC", "GSPETR": "DOMESTIC", "GSPETW": "DOMESTIC", "GSPEUA": "DOMESTIC", "GSPEUK": "DOMESTIC", "GSPEUS": "DOMESTIC", "GSPEUY": "DOMESTIC", "GSPEVN": "DOMESTIC", "GSPEZA": "DOMESTIC", "HHDIRAT": "PERCENT", "HHDIRBE": "PERCENT", "HHDIRCH": "PERCENT", "HHDIRCY": "PERCENT", "HHDIRCZ": "PERCENT", "HHDIRDE": "PERCENT", "HHDIRDK": "PERCENT", "HHDIREE": "PERCENT", "HHDIRES": "PERCENT", "HHDIRFI": "PERCENT", "HHDIRFR": "PERCENT", "HHDIRGR": "PERCENT", "HHDIRHR": "PERCENT", "HHDIRHU": "PERCENT", "HHDIRIE": "PERCENT", "HHDIRIT": "PERCENT", "HHDIRLT": "PERCENT", "HHDIRLU": "PERCENT", "HHDIRLV": "PERCENT", "HHDIRNL": "PERCENT", "HHDIRNO": "PERCENT", "HHDIRPL": "PERCENT", "HHDIRPT": "PERCENT", "HHDIRSE": "PERCENT", "HHDIRSI": "PERCENT", "HHDIRSK": "PERCENT", "HHDIRTR": "PERCENT", "HHSAT": "PERCENT", "HHSBE": "PERCENT", "HHSCN": "DOMESTIC", "HHSCZ": "PERCENT", "HHSDE": "PERCENT", "HHSDK": "PERCENT", "HHSES": "PERCENT", "HHSEU": "PERCENT", "HHSFI": "PERCENT", "HHSFR": "PERCENT", "HHSGR": "PERCENT", "HHSHU": "PERCENT", "HHSIE": "PERCENT", "HHSIT": "PERCENT", "HHSNL": "PERCENT", "HHSNO": "PERCENT", "HHSPL": "PERCENT", "HHSPT": "PERCENT", "HHSRO": "PERCENT", "HHSSE": "PERCENT", "HHSSI": "PERCENT", "HHSUK": "PERCENT", "HOU5R": "INDEX", "HOUAE": "INDEX", "HOUAT": "INDEX", "HOUAU": "INDEX", "HOUBE": "INDEX", "HOUBG": "INDEX", "HOUBR": "INDEX", "HOUCA": "INDEX", "HOUCH": "INDEX", "HOUCL": "INDEX", "HOUCN": "INDEX", "HOUCO": "INDEX", "HOUCY": "INDEX", "HOUCZ": "INDEX", "HOUDE": "INDEX", "HOUDK": "INDEX", "HOUEA": "INDEX", "HOUEE": "INDEX", "HOUES": "INDEX", "HOUEU": "INDEX", "HOUFI": "INDEX", "HOUFR": "INDEX", "HOUGR": "INDEX", "HOUHK": "INDEX", "HOUHR": "INDEX", "HOUHU": "INDEX", "HOUID": "INDEX", "HOUIE": "INDEX", "HOUIL": "INDEX", "HOUIN": "INDEX", "HOUIS": "INDEX", "HOUIT": "INDEX", "HOUJP": "INDEX", "HOUKR": "INDEX", "HOULT": "INDEX", "HOULU": "INDEX", "HOULV": "INDEX", "HOUMA": "INDEX", "HOUMK": "INDEX", "HOUMT": "INDEX", "HOUMX": "INDEX", "HOUMY": "INDEX", "HOUNL": "INDEX", "HOUNO": "INDEX", "HOUNZ": "INDEX", "HOUPE": "INDEX", "HOUPH": "INDEX", "HOUPL": "INDEX", "HOUPT": "INDEX", "HOURO": "INDEX", "HOURU": "INDEX", "HOUSE": "INDEX", "HOUSG": "INDEX", "HOUSI": "INDEX", "HOUSK": "INDEX", "HOUTH": "INDEX", "HOUTR": "INDEX", "HOUUK": "INDEX", "HOUUS": "INDEX", "HOUXW": "INDEX", "HOUZA": "INDEX", "IBD1AU": "PERCENT", "IBD1BG": "PERCENT", "IBD1CL": "PERCENT", "IBD1CO": "PERCENT", "IBD1CZ": "PERCENT", "IBD1DK": "PERCENT", "IBD1EA": "PERCENT", "IBD1HR": "PERCENT", "IBD1HU": "PERCENT", "IBD1KR": "PERCENT", "IBD1MX": "PERCENT", "IBD1NZ": "PERCENT", "IBD1PL": "PERCENT", "IBD1PY": "OTHER NUMBER", "IBD1RO": "PERCENT", "IBD1RU": "PERCENT", "IBD1SE": "PERCENT", "IBD1SG": "PERCENT", "IBD1TH": "PERCENT", "IBD1TR": "PERCENT", "IBD1UA": "PERCENT", "IBD1UK": "PERCENT", "IIPAAL": "DOMESTIC", "IIPAAR": "USD", "IIPAAT": "DOMESTIC", "IIPAAU": "DOMESTIC", "IIPABA": "DOMESTIC", "IIPABD": "DOMESTIC", "IIPABE": "DOMESTIC", "IIPABG": "DOMESTIC", "IIPABR": "USD", "IIPACA": "DOMESTIC", "IIPACH": "DOMESTIC", "IIPACL": "USD", "IIPACN": "USD", "IIPACO": "USD", "IIPACY": "DOMESTIC", "IIPACZ": "DOMESTIC", "IIPADE": "DOMESTIC", "IIPADK": "DOMESTIC", "IIPAEE": "DOMESTIC", "IIPAEG": "USD", "IIPAES": "DOMESTIC", "IIPAFI": "DOMESTIC", "IIPAFR": "DOMESTIC", "IIPAGR": "DOMESTIC", "IIPAHK": "DOMESTIC", "IIPAHR": "DOMESTIC", "IIPAHU": "DOMESTIC", "IIPAID": "USD", "IIPAIE": "DOMESTIC", "IIPAIL": "USD", "IIPAIN": "USD", "IIPAIS": "DOMESTIC", "IIPAIT": "DOMESTIC", "IIPAJP": "DOMESTIC", "IIPAKH": "DOMESTIC", "IIPAKR": "USD", "IIPALT": "DOMESTIC", "IIPALU": "DOMESTIC", "IIPALV": "DOMESTIC", "IIPAMT": "DOMESTIC", "IIPAMX": "USD", "IIPAMY": "DOMESTIC", "IIPANL": "DOMESTIC", "IIPANO": "DOMESTIC", "IIPANP": "DOMESTIC", "IIPAPH": "USD", "IIPAPK": "USD", "IIPAPL": "DOMESTIC", "IIPAPT": "DOMESTIC", "IIPARO": "DOMESTIC", "IIPARU": "USD", "IIPASA": "DOMESTIC", "IIPASE": "DOMESTIC", "IIPASG": "DOMESTIC", "IIPASI": "DOMESTIC", "IIPASK": "DOMESTIC", "IIPATH": "USD", "IIPATR": "USD", "IIPATW": "USD", "IIPAUK": "DOMESTIC", "IIPAUS": "DOMESTIC", "IIPAXK": "DOMESTIC", "IIPAZA": "DOMESTIC", "IIPLAL": "DOMESTIC", "IIPLAR": "USD", "IIPLAT": "DOMESTIC", "IIPLAU": "DOMESTIC", "IIPLBA": "DOMESTIC", "IIPLBD": "DOMESTIC", "IIPLBE": "DOMESTIC", "IIPLBG": "DOMESTIC", "IIPLBR": "USD", "IIPLCA": "DOMESTIC", "IIPLCH": "DOMESTIC", "IIPLCL": "USD", "IIPLCN": "USD", "IIPLCO": "USD", "IIPLCY": "DOMESTIC", "IIPLCZ": "DOMESTIC", "IIPLDE": "DOMESTIC", "IIPLDK": "DOMESTIC", "IIPLEE": "DOMESTIC", "IIPLEG": "USD", "IIPLES": "DOMESTIC", "IIPLFI": "DOMESTIC", "IIPLFR": "DOMESTIC", "IIPLGR": "DOMESTIC", "IIPLHK": "DOMESTIC", "IIPLHR": "DOMESTIC", "IIPLHU": "DOMESTIC", "IIPLID": "USD", "IIPLIE": "DOMESTIC", "IIPLIL": "USD", "IIPLIN": "USD", "IIPLIS": "DOMESTIC", "IIPLIT": "DOMESTIC", "IIPLJP": "DOMESTIC", "IIPLKH": "DOMESTIC", "IIPLKR": "USD", "IIPLLT": "DOMESTIC", "IIPLLU": "DOMESTIC", "IIPLLV": "DOMESTIC", "IIPLMT": "DOMESTIC", "IIPLMX": "USD", "IIPLMY": "DOMESTIC", "IIPLNL": "DOMESTIC", "IIPLNO": "DOMESTIC", "IIPLNP": "DOMESTIC", "IIPLPH": "USD", "IIPLPK": "USD", "IIPLPL": "DOMESTIC", "IIPLPT": "DOMESTIC", "IIPLRO": "DOMESTIC", "IIPLRU": "USD", "IIPLSA": "DOMESTIC", "IIPLSE": "DOMESTIC", "IIPLSG": "DOMESTIC", "IIPLSI": "DOMESTIC", "IIPLSK": "DOMESTIC", "IIPLTH": "USD", "IIPLTR": "USD", "IIPLTW": "USD", "IIPLUK": "DOMESTIC", "IIPLUS": "DOMESTIC", "IIPLXK": "DOMESTIC", "IIPLZA": "DOMESTIC", "IMPAE": "DOMESTIC", "IMPAL": "DOMESTIC", "IMPAR": "DOMESTIC", "IMPAT": "DOMESTIC", "IMPAU": "DOMESTIC", "IMPAZ": "DOMESTIC", "IMPBD": "DOMESTIC", "IMPBE": "DOMESTIC", "IMPBG": "DOMESTIC", "IMPBR": "DOMESTIC", "IMPBW": "DOMESTIC", "IMPBY": "DOMESTIC", "IMPCA": "DOMESTIC", "IMPCH": "DOMESTIC", "IMPCL": "DOMESTIC", "IMPCM": "DOMESTIC", "IMPCN": "DOMESTIC", "IMPCO": "DOMESTIC", "IMPCY": "DOMESTIC", "IMPCZ": "DOMESTIC", "IMPDE": "DOMESTIC", "IMPDK": "DOMESTIC", "IMPEA": "DOMESTIC", "IMPEE": "DOMESTIC", "IMPES": "DOMESTIC", "IMPEU": "DOMESTIC", "IMPFI": "DOMESTIC", "IMPFR": "DOMESTIC", "IMPGR": "DOMESTIC", "IMPHK": "DOMESTIC", "IMPHR": "DOMESTIC", "IMPHU": "DOMESTIC", "IMPID": "DOMESTIC", "IMPIE": "DOMESTIC", "IMPIL": "USD", "IMPIN": "DOMESTIC", "IMPIR": "DOMESTIC", "IMPIT": "DOMESTIC", "IMPJO": "DOMESTIC", "IMPJP": "DOMESTIC", "IMPKR": "DOMESTIC", "IMPKZ": "DOMESTIC", "IMPLT": "DOMESTIC", "IMPLU": "DOMESTIC", "IMPLV": "DOMESTIC", "IMPMA": "DOMESTIC", "IMPME": "DOMESTIC", "IMPMK": "DOMESTIC", "IMPMN": "DOMESTIC", "IMPMONAR": "USD", "IMPMONAU": "DOMESTIC", "IMPMONBD": "USD", "IMPMONBE": "DOMESTIC", "IMPMONBG": "DOMESTIC", "IMPMONBR": "USD", "IMPMONCA": "DOMESTIC", "IMPMONCL": "USD", "IMPMONCN": "USD", "IMPMONCO": "USD", "IMPMONCR": "USD", "IMPMONCZ": "DOMESTIC", "IMPMONDE": "DOMESTIC", "IMPMONDK": "DOMESTIC", "IMPMONEE": "DOMESTIC", "IMPMONEG": "USD", "IMPMONES": "DOMESTIC", "IMPMONEU": "DOMESTIC", "IMPMONFI": "DOMESTIC", "IMPMONFR": "DOMESTIC", "IMPMONGR": "DOMESTIC", "IMPMONHK": "DOMESTIC", "IMPMONHR": "DOMESTIC", "IMPMONHU": "DOMESTIC", "IMPMONID": "USD", "IMPMONIN": "USD", "IMPMONIT": "DOMESTIC", "IMPMONJP": "DOMESTIC", "IMPMONKH": "DOMESTIC", "IMPMONKR": "USD", "IMPMONKZ": "USD", "IMPMONLT": "DOMESTIC", "IMPMONLU": "DOMESTIC", "IMPMONLV": "DOMESTIC", "IMPMONMK": "DOMESTIC", "IMPMONMT": "DOMESTIC", "IMPMONMX": "USD", "IMPMONMY": "DOMESTIC", "IMPMONNL": "DOMESTIC", "IMPMONNP": "USD", "IMPMONPH": "USD", "IMPMONPK": "DOMESTIC", "IMPMONPL": "DOMESTIC", "IMPMONPT": "DOMESTIC", "IMPMONQA": "DOMESTIC", "IMPMONRO": "DOMESTIC", "IMPMONRS": "DOMESTIC", "IMPMONRU": "USD", "IMPMONSA": "DOMESTIC", "IMPMONSE": "DOMESTIC", "IMPMONSG": "DOMESTIC", "IMPMONSI": "DOMESTIC", "IMPMONSK": "DOMESTIC", "IMPMONTH": "USD", "IMPMONTR": "USD", "IMPMONTW": "USD", "IMPMONUA": "USD", "IMPMONUK": "DOMESTIC", "IMPMONUS": "DOMESTIC", "IMPMONUY": "USD", "IMPMONVN": "USD", "IMPMONZA": "DOMESTIC", "IMPMT": "DOMESTIC", "IMPMX": "DOMESTIC", "IMPMY": "DOMESTIC", "IMPNG": "DOMESTIC", "IMPNL": "DOMESTIC", "IMPNO": "DOMESTIC", "IMPNZ": "DOMESTIC", "IMPPE": "DOMESTIC", "IMPPH": "DOMESTIC", "IMPPK": "DOMESTIC", "IMPPL": "DOMESTIC", "IMPPT": "DOMESTIC", "IMPQA": "DOMESTIC", "IMPRO": "DOMESTIC", "IMPRS": "DOMESTIC", "IMPRU": "DOMESTIC", "IMPRW": "DOMESTIC", "IMPSA": "DOMESTIC", "IMPSE": "DOMESTIC", "IMPSG": "DOMESTIC", "IMPSI": "DOMESTIC", "IMPSK": "DOMESTIC", "IMPSV": "DOMESTIC", "IMPTH": "DOMESTIC", "IMPTR": "DOMESTIC", "IMPTW": "DOMESTIC", "IMPUA": "DOMESTIC", "IMPUK": "DOMESTIC", "IMPUS": "DOMESTIC", "IMPZA": "DOMESTIC", "INVERAT": "PERCENT", "INVERBE": "PERCENT", "INVERCH": "PERCENT", "INVERCY": "PERCENT", "INVERCZ": "PERCENT", "INVERDE": "PERCENT", "INVERDK": "PERCENT", "INVEREE": "PERCENT", "INVERES": "PERCENT", "INVERFI": "PERCENT", "INVERFR": "PERCENT", "INVERGR": "PERCENT", "INVERHR": "PERCENT", "INVERHU": "PERCENT", "INVERIE": "PERCENT", "INVERIT": "PERCENT", "INVERLT": "PERCENT", "INVERLV": "PERCENT", "INVERNL": "PERCENT", "INVERNO": "PERCENT", "INVERPL": "PERCENT", "INVERPT": "PERCENT", "INVERRO": "PERCENT", "INVERSE": "PERCENT", "INVERSI": "PERCENT", "INVERSK": "PERCENT", "IPAR": "INDEX", "IPAT": "INDEX", "IPAU": "INDEX", "IPAZ": "INDEX", "IPBA": "INDEX", "IPBD": "INDEX", "IPBE": "INDEX", "IPBG": "INDEX", "IPBR": "INDEX", "IPBY": "DOMESTIC", "IPCA": "INDEX", "IPCH": "INDEX", "IPCL": "INDEX", "IPCM": "INDEX", "IPCN": "DOMESTIC", "IPCO": "INDEX", "IPCR": "INDEX", "IPCY": "INDEX", "IPCZ": "INDEX", "IPDE": "INDEX", "IPDK": "INDEX", "IPEE": "INDEX", "IPEG": "INDEX", "IPES": "INDEX", "IPEU": "INDEX", "IPFI": "INDEX", "IPFR": "INDEX", "IPGR": "INDEX", "IPHK": "INDEX", "IPHR": "INDEX", "IPHU": "INDEX", "IPID": "INDEX", "IPIE": "INDEX", "IPIL": "INDEX", "IPIN": "INDEX", "IPIT": "INDEX", "IPJO": "INDEX", "IPJP": "INDEX", "IPKR": "INDEX", "IPKZ": "INDEX", "IPLT": "INDEX", "IPLU": "INDEX", "IPLV": "INDEX", "IPMA": "INDEX", "IPME": "INDEX", "IPMK": "INDEX", "IPMT": "INDEX", "IPMX": "INDEX", "IPMY": "INDEX", "IPNL": "INDEX", "IPNO": "INDEX", "IPPA": "INDEX", "IPPH": "INDEX", "IPPK": "INDEX", "IPPL": "INDEX", "IPPT": "INDEX", "IPQA": "INDEX", "IPRO": "INDEX", "IPRS": "INDEX", "IPRU": "INDEX", "IPSA": "INDEX", "IPSE": "INDEX", "IPSG": "INDEX", "IPSI": "INDEX", "IPSK": "INDEX", "IPTH": "INDEX", "IPTR": "INDEX", "IPTW": "INDEX", "IPUA": "INDEX", "IPUK": "INDEX", "IPUS": "INDEX", "IPUY": "INDEX", "IPVN": "INDEX", "IPZA": "INDEX", "JHRUS": "PERCENT", "JLRUS": "PERCENT", "JQRUS": "PERCENT", "JVRAT": "PERCENT", "JVRBE": "PERCENT", "JVRBG": "PERCENT", "JVRCH": "PERCENT", "JVRCY": "PERCENT", "JVRCZ": "PERCENT", "JVRDE": "PERCENT", "JVREA": "PERCENT", "JVREE": "PERCENT", "JVRES": "PERCENT", "JVREU": "PERCENT", "JVRFI": "PERCENT", "JVRGR": "PERCENT", "JVRHR": "PERCENT", "JVRHU": "PERCENT", "JVRIE": "PERCENT", "JVRIT": "PERCENT", "JVRLT": "PERCENT", "JVRLU": "PERCENT", "JVRLV": "PERCENT", "JVRMK": "PERCENT", "JVRMT": "PERCENT", "JVRNL": "PERCENT", "JVRNO": "PERCENT", "JVRPL": "PERCENT", "JVRPT": "PERCENT", "JVRRO": "PERCENT", "JVRSE": "PERCENT", "JVRSI": "PERCENT", "JVRSK": "PERCENT", "JVRUK": "PERCENT", "JVRUS": "PERCENT", "KABE": "DOMESTIC", "KABG": "DOMESTIC", "KACN": "USD", "KACZ": "DOMESTIC", "KADE": "DOMESTIC", "KADK": "DOMESTIC", "KAEE": "DOMESTIC", "KAES": "DOMESTIC", "KAFI": "DOMESTIC", "KAFR": "DOMESTIC", "KAGR": "DOMESTIC", "KAHR": "DOMESTIC", "KAHU": "DOMESTIC", "KAIT": "DOMESTIC", "KALT": "DOMESTIC", "KALU": "DOMESTIC", "KALV": "DOMESTIC", "KAMT": "DOMESTIC", "KANL": "DOMESTIC", "KAPH": "USD", "KAPL": "DOMESTIC", "KAPT": "DOMESTIC", "KARO": "DOMESTIC", "KASE": "DOMESTIC", "KASI": "DOMESTIC", "KASK": "DOMESTIC", "LE00CN": "OTHER NUMBER", "LMICS": "USD", "M3AR": "DOMESTIC", "M3AU": "DOMESTIC", "M3BR": "DOMESTIC", "M3BY": "DOMESTIC", "M3CA": "DOMESTIC", "M3CH": "DOMESTIC", "M3CL": "DOMESTIC", "M3CN": "DOMESTIC", "M3CR": "DOMESTIC", "M3EA": "DOMESTIC", "M3HU": "DOMESTIC", "M3ID": "DOMESTIC", "M3IN": "DOMESTIC", "M3JP": "DOMESTIC", "M3KR": "DOMESTIC", "M3KZ": "DOMESTIC", "M3MA": "DOMESTIC", "M3MX": "DOMESTIC", "M3MY": "DOMESTIC", "M3NO": "DOMESTIC", "M3NP": "DOMESTIC", "M3PH": "DOMESTIC", "M3PL": "DOMESTIC", "M3RU": "DOMESTIC", "M3SA": "DOMESTIC", "M3SG": "DOMESTIC", "M3TH": "DOMESTIC", "M3TN": "DOMESTIC", "M3TR": "DOMESTIC", "M3UA": "DOMESTIC", "M3UK": "DOMESTIC", "M3US": "DOMESTIC", "M3UY": "DOMESTIC", "M3YDAE": "PERCENT", "M3YDAU": "PERCENT", "M3YDCA": "PERCENT", "M3YDCN": "PERCENT", "M3YDCZ": "PERCENT", "M3YDDK": "PERCENT", "M3YDEA": "PERCENT", "M3YDHK": "PERCENT", "M3YDHR": "PERCENT", "M3YDHU": "PERCENT", "M3YDID": "PERCENT", "M3YDIN": "PERCENT", "M3YDJP": "PERCENT", "M3YDKR": "PERCENT", "M3YDMO": "PERCENT", "M3YDMX": "PERCENT", "M3YDMY": "PERCENT", "M3YDNP": "PERCENT", "M3YDNZ": "PERCENT", "M3YDPA": "PERCENT", "M3YDPH": "PERCENT", "M3YDPL": "PERCENT", "M3YDQA": "PERCENT", "M3YDRO": "PERCENT", "M3YDRU": "PERCENT", "M3YDSA": "PERCENT", "M3YDSE": "PERCENT", "M3YDSG": "PERCENT", "M3YDTH": "PERCENT", "M3YDTR": "PERCENT", "M3YDUA": "PERCENT", "M3YDUK": "PERCENT", "M3YDUS": "PERCENT", "M3YDVN": "PERCENT", "M3YDZA": "PERCENT", "M3ZA": "DOMESTIC", "MBAR": "DOMESTIC", "MBAU": "DOMESTIC", "MBBR": "DOMESTIC", "MBCH": "DOMESTIC", "MBCN": "DOMESTIC", "MBEG": "DOMESTIC", "MBIN": "DOMESTIC", "MBJP": "DOMESTIC", "MBKR": "DOMESTIC", "MBMX": "DOMESTIC", "MBMY": "DOMESTIC", "MBNO": "DOMESTIC", "MBRU": "DOMESTIC", "MBSA": "DOMESTIC", "MBTH": "DOMESTIC", "MBTR": "DOMESTIC", "MBUA": "DOMESTIC", "MBUK": "DOMESTIC", "MBUS": "DOMESTIC", "NCTBE": "DOMESTIC", "NCTBG": "DOMESTIC", "NCTCN": "USD", "NCTCZ": "DOMESTIC", "NCTDE": "DOMESTIC", "NCTDK": "DOMESTIC", "NCTEE": "DOMESTIC", "NCTES": "DOMESTIC", "NCTFI": "DOMESTIC", "NCTFR": "DOMESTIC", "NCTGR": "DOMESTIC", "NCTHR": "DOMESTIC", "NCTHU": "DOMESTIC", "NCTIT": "DOMESTIC", "NCTLT": "DOMESTIC", "NCTLU": "DOMESTIC", "NCTLV": "DOMESTIC", "NCTMT": "DOMESTIC", "NCTMX": "DOMESTIC", "NCTNL": "DOMESTIC", "NCTPH": "USD", "NCTPL": "DOMESTIC", "NCTPT": "DOMESTIC", "NCTRO": "DOMESTIC", "NCTSE": "DOMESTIC", "NCTSI": "DOMESTIC", "NCTSK": "DOMESTIC", "NFCIAT": "PERCENT", "NFCIBE": "PERCENT", "NFCICZ": "PERCENT", "NFCIDE": "PERCENT", "NFCIDK": "PERCENT", "NFCIEE": "PERCENT", "NFCIES": "PERCENT", "NFCIEU": "PERCENT", "NFCIFI": "PERCENT", "NFCIFR": "PERCENT", "NFCIGR": "PERCENT", "NFCIHU": "PERCENT", "NFCIIE": "PERCENT", "NFCIIT": "PERCENT", "NFCINL": "PERCENT", "NFCINO": "PERCENT", "NFCIPL": "PERCENT", "NFCIPT": "PERCENT", "NFCIRO": "PERCENT", "NFCISE": "PERCENT", "NFCIUK": "PERCENT", "NFCLOANBG": "PERCENT", "NFCLOANCY": "PERCENT", "NFCLOANEE": "PERCENT", "NFCLOANHR": "PERCENT", "NFCLOANLT": "PERCENT", "NFCLOANLV": "PERCENT", "NFCLOANMT": "PERCENT", "NFCLOANRO": "PERCENT", "NFCLOANSI": "PERCENT", "NFCLOANSK": "PERCENT", "NIIPAL": "DOMESTIC", "NIIPAR": "USD", "NIIPAT": "DOMESTIC", "NIIPAU": "DOMESTIC", "NIIPBA": "DOMESTIC", "NIIPBD": "DOMESTIC", "NIIPBE": "DOMESTIC", "NIIPBG": "DOMESTIC", "NIIPBR": "USD", "NIIPCA": "DOMESTIC", "NIIPCH": "DOMESTIC", "NIIPCL": "USD", "NIIPCN": "USD", "NIIPCO": "USD", "NIIPCY": "DOMESTIC", "NIIPCZ": "DOMESTIC", "NIIPDE": "DOMESTIC", "NIIPDK": "DOMESTIC", "NIIPEE": "DOMESTIC", "NIIPEG": "USD", "NIIPES": "DOMESTIC", "NIIPFI": "DOMESTIC", "NIIPFR": "DOMESTIC", "NIIPGR": "DOMESTIC", "NIIPHK": "DOMESTIC", "NIIPHR": "DOMESTIC", "NIIPHU": "DOMESTIC", "NIIPID": "USD", "NIIPIE": "DOMESTIC", "NIIPIL": "USD", "NIIPIN": "USD", "NIIPIS": "DOMESTIC", "NIIPIT": "DOMESTIC", "NIIPJP": "DOMESTIC", "NIIPKH": "DOMESTIC", "NIIPKR": "USD", "NIIPLT": "DOMESTIC", "NIIPLU": "DOMESTIC", "NIIPLV": "DOMESTIC", "NIIPMK": "DOMESTIC", "NIIPMT": "DOMESTIC", "NIIPMX": "USD", "NIIPMY": "DOMESTIC", "NIIPNL": "DOMESTIC", "NIIPNO": "DOMESTIC", "NIIPNP": "DOMESTIC", "NIIPPH": "USD", "NIIPPK": "USD", "NIIPPL": "DOMESTIC", "NIIPPT": "DOMESTIC", "NIIPRO": "DOMESTIC", "NIIPRS": "DOMESTIC", "NIIPRU": "USD", "NIIPSA": "DOMESTIC", "NIIPSE": "DOMESTIC", "NIIPSG": "DOMESTIC", "NIIPSI": "DOMESTIC", "NIIPSK": "DOMESTIC", "NIIPTH": "USD", "NIIPTR": "USD", "NIIPTW": "USD", "NIIPUA": "USD", "NIIPUK": "DOMESTIC", "NIIPUS": "DOMESTIC", "NIIPXK": "DOMESTIC", "NIIPZA": "DOMESTIC", "NPLAR": "PERCENT", "NPLAT": "PERCENT", "NPLAU": "PERCENT", "NPLBE": "PERCENT", "NPLBR": "PERCENT", "NPLCN": "PERCENT", "NPLDK": "PERCENT", "NPLES": "PERCENT", "NPLFI": "PERCENT", "NPLFR": "PERCENT", "NPLHK": "PERCENT", "NPLID": "PERCENT", "NPLIE": "PERCENT", "NPLIN": "PERCENT", "NPLIT": "PERCENT", "NPLJP": "PERCENT", "NPLLU": "PERCENT", "NPLMX": "PERCENT", "NPLNL": "PERCENT", "NPLNO": "PERCENT", "NPLPL": "PERCENT", "NPLRU": "PERCENT", "NPLSA": "PERCENT", "NPLSE": "PERCENT", "NPLSG": "PERCENT", "NPLTR": "PERCENT", "NPLUS": "PERCENT", "NPLZA": "PERCENT", "NYBE": "DOMESTIC", "NYBG": "DOMESTIC", "NYCN": "USD", "NYCZ": "DOMESTIC", "NYDE": "DOMESTIC", "NYDK": "DOMESTIC", "NYEE": "DOMESTIC", "NYES": "DOMESTIC", "NYFI": "DOMESTIC", "NYFR": "DOMESTIC", "NYGR": "DOMESTIC", "NYHR": "DOMESTIC", "NYHU": "DOMESTIC", "NYIT": "DOMESTIC", "NYLT": "DOMESTIC", "NYLU": "DOMESTIC", "NYLV": "DOMESTIC", "NYMT": "DOMESTIC", "NYMX": "DOMESTIC", "NYNL": "DOMESTIC", "NYPH": "USD", "NYPL": "DOMESTIC", "NYPT": "DOMESTIC", "NYRO": "DOMESTIC", "NYSE": "DOMESTIC", "NYSI": "DOMESTIC", "NYSK": "DOMESTIC", "OILDEMAE": "BARRELS PER DAY", "OILDEMAL": "BARRELS PER DAY", "OILDEMAM": "BARRELS PER DAY", "OILDEMAO": "BARRELS PER DAY", "OILDEMAR": "BARRELS PER DAY", "OILDEMAT": "BARRELS PER DAY", "OILDEMAU": "BARRELS PER DAY", "OILDEMAZ": "BARRELS PER DAY", "OILDEMBB": "BARRELS PER DAY", "OILDEMBD": "BARRELS PER DAY", "OILDEMBE": "BARRELS PER DAY", "OILDEMBG": "BARRELS PER DAY", "OILDEMBH": "BARRELS PER DAY", "OILDEMBM": "BARRELS PER DAY", "OILDEMBN": "BARRELS PER DAY", "OILDEMBO": "BARRELS PER DAY", "OILDEMBR": "BARRELS PER DAY", "OILDEMBY": "BARRELS PER DAY", "OILDEMBZ": "BARRELS PER DAY", "OILDEMCA": "BARRELS PER DAY", "OILDEMCH": "BARRELS PER DAY", "OILDEMCL": "BARRELS PER DAY", "OILDEMCN": "BARRELS PER DAY", "OILDEMCO": "BARRELS PER DAY", "OILDEMCR": "BARRELS PER DAY", "OILDEMCU": "BARRELS PER DAY", "OILDEMCY": "BARRELS PER DAY", "OILDEMCZ": "BARRELS PER DAY", "OILDEMDE": "BARRELS PER DAY", "OILDEMDK": "BARRELS PER DAY", "OILDEMDO": "BARRELS PER DAY", "OILDEMDZ": "BARRELS PER DAY", "OILDEMEC": "BARRELS PER DAY", "OILDEMEE": "BARRELS PER DAY", "OILDEMEG": "BARRELS PER DAY", "OILDEMES": "BARRELS PER DAY", "OILDEMFI": "BARRELS PER DAY", "OILDEMFR": "BARRELS PER DAY", "OILDEMGA": "BARRELS PER DAY", "OILDEMGD": "BARRELS PER DAY", "OILDEMGE": "BARRELS PER DAY", "OILDEMGM": "BARRELS PER DAY", "OILDEMGQ": "BARRELS PER DAY", "OILDEMGR": "BARRELS PER DAY", "OILDEMGT": "BARRELS PER DAY", "OILDEMGY": "BARRELS PER DAY", "OILDEMHK": "BARRELS PER DAY", "OILDEMHN": "BARRELS PER DAY", "OILDEMHR": "BARRELS PER DAY", "OILDEMHT": "BARRELS PER DAY", "OILDEMHU": "BARRELS PER DAY", "OILDEMID": "BARRELS PER DAY", "OILDEMIE": "BARRELS PER DAY", "OILDEMIN": "BARRELS PER DAY", "OILDEMIQ": "BARRELS PER DAY", "OILDEMIR": "BARRELS PER DAY", "OILDEMIS": "BARRELS PER DAY", "OILDEMIT": "BARRELS PER DAY", "OILDEMJM": "BARRELS PER DAY", "OILDEMJP": "BARRELS PER DAY", "OILDEMKR": "BARRELS PER DAY", "OILDEMKW": "BARRELS PER DAY", "OILDEMKZ": "BARRELS PER DAY", "OILDEMLT": "BARRELS PER DAY", "OILDEMLU": "BARRELS PER DAY", "OILDEMLV": "BARRELS PER DAY", "OILDEMLY": "BARRELS PER DAY", "OILDEMMA": "BARRELS PER DAY", "OILDEMMD": "BARRELS PER DAY", "OILDEMMK": "BARRELS PER DAY", "OILDEMMM": "BARRELS PER DAY", "OILDEMMT": "BARRELS PER DAY", "OILDEMMU": "BARRELS PER DAY", "OILDEMMX": "BARRELS PER DAY", "OILDEMMY": "BARRELS PER DAY", "OILDEMNE": "BARRELS PER DAY", "OILDEMNG": "BARRELS PER DAY", "OILDEMNI": "BARRELS PER DAY", "OILDEMNL": "BARRELS PER DAY", "OILDEMNO": "BARRELS PER DAY", "OILDEMNP": "BARRELS PER DAY", "OILDEMNZ": "BARRELS PER DAY", "OILDEMOM": "BARRELS PER DAY", "OILDEMPA": "BARRELS PER DAY", "OILDEMPE": "BARRELS PER DAY", "OILDEMPG": "BARRELS PER DAY", "OILDEMPH": "BARRELS PER DAY", "OILDEMPL": "BARRELS PER DAY", "OILDEMPT": "BARRELS PER DAY", "OILDEMPY": "BARRELS PER DAY", "OILDEMQA": "BARRELS PER DAY", "OILDEMRO": "BARRELS PER DAY", "OILDEMRU": "BARRELS PER DAY", "OILDEMSA": "BARRELS PER DAY", "OILDEMSD": "BARRELS PER DAY", "OILDEMSE": "BARRELS PER DAY", "OILDEMSG": "BARRELS PER DAY", "OILDEMSI": "BARRELS PER DAY", "OILDEMSK": "BARRELS PER DAY", "OILDEMSR": "BARRELS PER DAY", "OILDEMSV": "BARRELS PER DAY", "OILDEMSY": "BARRELS PER DAY", "OILDEMSZ": "BARRELS PER DAY", "OILDEMTH": "BARRELS PER DAY", "OILDEMTJ": "BARRELS PER DAY", "OILDEMTN": "BARRELS PER DAY", "OILDEMTR": "BARRELS PER DAY", "OILDEMTT": "BARRELS PER DAY", "OILDEMTW": "BARRELS PER DAY", "OILDEMUA": "BARRELS PER DAY", "OILDEMUK": "BARRELS PER DAY", "OILDEMUS": "BARRELS PER DAY", "OILDEMUY": "BARRELS PER DAY", "OILDEMVE": "BARRELS PER DAY", "OILDEMVN": "BARRELS PER DAY", "OILDEMYE": "BARRELS PER DAY", "OILDEMZA": "BARRELS PER DAY", "OILPRODAE": "BARRELS PER DAY", "OILPRODAL": "BARRELS PER DAY", "OILPRODAM": "BARRELS PER DAY", "OILPRODAO": "BARRELS PER DAY", "OILPRODAR": "BARRELS PER DAY", "OILPRODAT": "BARRELS PER DAY", "OILPRODAU": "BARRELS PER DAY", "OILPRODAZ": "BARRELS PER DAY", "OILPRODBB": "BARRELS PER DAY", "OILPRODBD": "BARRELS PER DAY", "OILPRODBE": "BARRELS PER DAY", "OILPRODBG": "BARRELS PER DAY", "OILPRODBH": "BARRELS PER DAY", "OILPRODBM": "BARRELS PER DAY", "OILPRODBN": "BARRELS PER DAY", "OILPRODBO": "BARRELS PER DAY", "OILPRODBR": "BARRELS PER DAY", "OILPRODBY": "BARRELS PER DAY", "OILPRODBZ": "BARRELS PER DAY", "OILPRODCA": "BARRELS PER DAY", "OILPRODCH": "BARRELS PER DAY", "OILPRODCL": "BARRELS PER DAY", "OILPRODCN": "BARRELS PER DAY", "OILPRODCO": "BARRELS PER DAY", "OILPRODCR": "BARRELS PER DAY", "OILPRODCU": "BARRELS PER DAY", "OILPRODCY": "BARRELS PER DAY", "OILPRODCZ": "BARRELS PER DAY", "OILPRODDE": "BARRELS PER DAY", "OILPRODDK": "BARRELS PER DAY", "OILPRODDO": "BARRELS PER DAY", "OILPRODDZ": "BARRELS PER DAY", "OILPRODEC": "BARRELS PER DAY", "OILPRODEE": "BARRELS PER DAY", "OILPRODEG": "BARRELS PER DAY", "OILPRODES": "BARRELS PER DAY", "OILPRODFI": "BARRELS PER DAY", "OILPRODFR": "BARRELS PER DAY", "OILPRODGA": "BARRELS PER DAY", "OILPRODGD": "BARRELS PER DAY", "OILPRODGE": "BARRELS PER DAY", "OILPRODGM": "BARRELS PER DAY", "OILPRODGQ": "BARRELS PER DAY", "OILPRODGR": "BARRELS PER DAY", "OILPRODGT": "BARRELS PER DAY", "OILPRODGY": "BARRELS PER DAY", "OILPRODHK": "BARRELS PER DAY", "OILPRODHN": "BARRELS PER DAY", "OILPRODHR": "BARRELS PER DAY", "OILPRODHT": "BARRELS PER DAY", "OILPRODHU": "BARRELS PER DAY", "OILPRODID": "BARRELS PER DAY", "OILPRODIE": "BARRELS PER DAY", "OILPRODIN": "BARRELS PER DAY", "OILPRODIQ": "BARRELS PER DAY", "OILPRODIR": "BARRELS PER DAY", "OILPRODIS": "BARRELS PER DAY", "OILPRODIT": "BARRELS PER DAY", "OILPRODJM": "BARRELS PER DAY", "OILPRODJP": "BARRELS PER DAY", "OILPRODKR": "BARRELS PER DAY", "OILPRODKW": "BARRELS PER DAY", "OILPRODKZ": "BARRELS PER DAY", "OILPRODLT": "BARRELS PER DAY", "OILPRODLU": "BARRELS PER DAY", "OILPRODLV": "BARRELS PER DAY", "OILPRODLY": "BARRELS PER DAY", "OILPRODMA": "BARRELS PER DAY", "OILPRODMD": "BARRELS PER DAY", "OILPRODMK": "BARRELS PER DAY", "OILPRODMM": "BARRELS PER DAY", "OILPRODMT": "BARRELS PER DAY", "OILPRODMU": "BARRELS PER DAY", "OILPRODMX": "BARRELS PER DAY", "OILPRODMY": "BARRELS PER DAY", "OILPRODNE": "BARRELS PER DAY", "OILPRODNG": "BARRELS PER DAY", "OILPRODNI": "BARRELS PER DAY", "OILPRODNL": "BARRELS PER DAY", "OILPRODNO": "BARRELS PER DAY", "OILPRODNP": "BARRELS PER DAY", "OILPRODNZ": "BARRELS PER DAY", "OILPRODOM": "BARRELS PER DAY", "OILPRODPA": "BARRELS PER DAY", "OILPRODPE": "BARRELS PER DAY", "OILPRODPG": "BARRELS PER DAY", "OILPRODPH": "BARRELS PER DAY", "OILPRODPL": "BARRELS PER DAY", "OILPRODPT": "BARRELS PER DAY", "OILPRODPY": "BARRELS PER DAY", "OILPRODQA": "BARRELS PER DAY", "OILPRODRO": "BARRELS PER DAY", "OILPRODRU": "BARRELS PER DAY", "OILPRODSA": "BARRELS PER DAY", "OILPRODSD": "BARRELS PER DAY", "OILPRODSE": "BARRELS PER DAY", "OILPRODSG": "BARRELS PER DAY", "OILPRODSI": "BARRELS PER DAY", "OILPRODSK": "BARRELS PER DAY", "OILPRODSR": "BARRELS PER DAY", "OILPRODSV": "BARRELS PER DAY", "OILPRODSY": "BARRELS PER DAY", "OILPRODSZ": "BARRELS PER DAY", "OILPRODTH": "BARRELS PER DAY", "OILPRODTJ": "BARRELS PER DAY", "OILPRODTN": "BARRELS PER DAY", "OILPRODTR": "BARRELS PER DAY", "OILPRODTT": "BARRELS PER DAY", "OILPRODTW": "BARRELS PER DAY", "OILPRODUA": "BARRELS PER DAY", "OILPRODUK": "BARRELS PER DAY", "OILPRODUS": "BARRELS PER DAY", "OILPRODUY": "BARRELS PER DAY", "OILPRODVE": "BARRELS PER DAY", "OILPRODVN": "BARRELS PER DAY", "OILPRODYE": "BARRELS PER DAY", "OILPRODZA": "BARRELS PER DAY", "PALUM": "USD", "PAPPLE": "USD", "PARTCO": "PERCENT", "PARTDO": "PERCENT", "PARTLK": "PERCENT", "PARTNZ": "PERCENT", "PARTTR": "PERCENT", "PBANSOP": "USD", "PBARL": "USD", "PBEEF": "USD", "PCEUS": "INDEX", "PCHANA": "USD", "PCHROM": "USD", "PCOALAU": "USD", "PCOALSA": "USD", "PCOBA": "USD", "PCOCO": "USD", "PCOFFOTM": "USD", "PCOFFROB": "USD", "PCOIL": "USD", "PCOPP": "USD", "PCOTTIND": "USD", "PDAP": "USD", "PFSHMEAL": "USD", "PGASO": "USD", "PGNUTS": "USD", "PGOLD": "USD", "PHEATOIL": "USD", "PHIDE": "USD", "PIORECR": "USD", "PLAMB": "USD", "PLEAD": "USD", "PLITH": "USD", "PLMMODY": "USD", "PLOGORE": "USD", "PLOGSK": "USD", "PMAIZMT": "USD", "PMANGELE": "USD", "PMILK": "USD", "PNGASEU": "USD", "PNGASJP": "USD", "PNGASUS": "USD", "PNICK": "USD", "POATS": "USD", "POILAPSP": "USD", "POILBRE": "USD", "POILDUB": "USD", "POILWTI": "USD", "POLIRAR": "PERCENT", "POLIRAU": "PERCENT", "POLIRAZ": "PERCENT", "POLIRBR": "PERCENT", "POLIRCA": "PERCENT", "POLIRCH": "PERCENT", "POLIRCL": "PERCENT", "POLIRCN": "PERCENT", "POLIRCO": "PERCENT", "POLIRCZ": "PERCENT", "POLIRDK": "PERCENT", "POLIREA": "PERCENT", "POLIRHK": "PERCENT", "POLIRHR": "PERCENT", "POLIRHU": "PERCENT", "POLIRID": "PERCENT", "POLIRIL": "PERCENT", "POLIRIN": "PERCENT", "POLIRIS": "PERCENT", "POLIRJP": "PERCENT", "POLIRKR": "PERCENT", "POLIRMK": "PERCENT", "POLIRMO": "PERCENT", "POLIRMX": "PERCENT", "POLIRMY": "PERCENT", "POLIRNO": "PERCENT", "POLIRNP": "PERCENT", "POLIRNZ": "PERCENT", "POLIRPE": "PERCENT", "POLIRPH": "PERCENT", "POLIRPL": "PERCENT", "POLIRPY": "OTHER NUMBER", "POLIRQA": "PERCENT", "POLIRRO": "PERCENT", "POLIRRS": "PERCENT", "POLIRRU": "PERCENT", "POLIRSA": "PERCENT", "POLIRSE": "PERCENT", "POLIRSG": "PERCENT", "POLIRTH": "PERCENT", "POLIRTN": "PERCENT", "POLIRTR": "PERCENT", "POLIRTW": "PERCENT", "POLIRUA": "PERCENT", "POLIRUK": "PERCENT", "POLIRUS": "PERCENT", "POLIRUZ": "PERCENT", "POLIRZA": "PERCENT", "POLVOIL": "USD", "POPAD": "PERSONS", "POPAE": "PERSONS", "POPAL": "PERSONS", "POPAM": "PERSONS", "POPAR": "PERSONS", "POPAT": "PERSONS", "POPAU": "PERSONS", "POPAZ": "OTHER NUMBER", "POPBD": "PERSONS", "POPBE": "PERSONS", "POPBG": "PERSONS", "POPBO": "PERSONS", "POPBR": "PERSONS", "POPBY": "PERSONS", "POPCA": "PERSONS", "POPCD": "PERSONS", "POPCH": "PERSONS", "POPCL": "PERSONS", "POPCN": "PERSONS", "POPCO": "PERSONS", "POPCR": "PERSONS", "POPCY": "PERSONS", "POPCZ": "PERSONS", "POPDE": "PERSONS", "POPDK": "PERSONS", "POPDO": "PERSONS", "POPDZ": "PERSONS", "POPEC": "PERSONS", "POPEE": "PERSONS", "POPEG": "PERSONS", "POPES": "PERSONS", "POPET": "PERSONS", "POPEU": "PERSONS", "POPFI": "PERSONS", "POPFR": "PERSONS", "POPGE": "PERSONS", "POPGR": "PERSONS", "POPGT": "PERSONS", "POPHK": "PERSONS", "POPHR": "PERSONS", "POPHU": "PERSONS", "POPID": "PERSONS", "POPIE": "PERSONS", "POPIL": "PERSONS", "POPIN": "PERSONS", "POPIQ": "PERSONS", "POPIR": "PERSONS", "POPIS": "PERSONS", "POPIT": "PERSONS", "POPJO": "PERSONS", "POPJP": "PERSONS", "POPKG": "PERSONS", "POPKH": "PERSONS", "POPKR": "PERSONS", "POPKW": "PERSONS", "POPKZ": "PERSONS", "POPLB": "PERSONS", "POPLI": "PERSONS", "POPLK": "PERSONS", "POPLT": "PERSONS", "POPLU": "PERSONS", "POPLV": "PERSONS", "POPLY": "PERSONS", "POPMA": "PERSONS", "POPMD": "PERSONS", "POPME": "PERSONS", "POPMK": "PERSONS", "POPMM": "PERSONS", "POPMO": "PERSONS", "POPMT": "PERSONS", "POPMX": "PERSONS", "POPMY": "PERSONS", "POPNG": "PERSONS", "POPNI": "PERSONS", "POPNL": "PERSONS", "POPNO": "PERSONS", "POPNZ": "PERSONS", "POPPH": "PERSONS", "POPPL": "PERSONS", "POPPT": "PERSONS", "POPPY": "PERSONS", "POPQA": "PERSONS", "POPRO": "PERSONS", "POPRS": "PERSONS", "POPRU": "PERSONS", "POPSA": "PERSONS", "POPSD": "PERSONS", "POPSE": "PERSONS", "POPSG": "PERSONS", "POPSI": "PERSONS", "POPSK": "PERSONS", "POPSM": "PERSONS", "POPSN": "PERSONS", "POPSV": "PERSONS", "POPTH": "PERSONS", "POPTJ": "PERSONS", "POPTM": "PERSONS", "POPTN": "PERSONS", "POPTR": "PERSONS", "POPTW": "PERSONS", "POPUA": "PERSONS", "POPUK": "PERSONS", "POPUS": "PERSONS", "POPUY": "PERSONS", "POPUZ": "PERSONS", "POPVE": "PERSONS", "POPVN": "PERSONS", "POPXK": "PERSONS", "POPZA": "PERSONS", "PORANG": "USD", "PPALLA": "USD", "PPIAL": "INDEX", "PPIAR": "INDEX", "PPIAT": "INDEX", "PPIAU": "INDEX", "PPIBD": "INDEX", "PPIBE": "INDEX", "PPIBG": "INDEX", "PPIBR": "INDEX", "PPIBY": "INDEX", "PPICA": "INDEX", "PPICH": "INDEX", "PPICL": "INDEX", "PPICN": "INDEX", "PPICO": "INDEX", "PPICR": "INDEX", "PPICY": "INDEX", "PPICZ": "INDEX", "PPIDE": "INDEX", "PPIDK": "INDEX", "PPIEE": "INDEX", "PPIEG": "INDEX", "PPIES": "INDEX", "PPIEU": "INDEX", "PPIFI": "INDEX", "PPIFR": "INDEX", "PPIGR": "INDEX", "PPIHK": "INDEX", "PPIHR": "INDEX", "PPIHU": "INDEX", "PPIID": "INDEX", "PPIIE": "INDEX", "PPIIL": "INDEX", "PPIIN": "INDEX", "PPIIT": "INDEX", "PPIJO": "INDEX", "PPIJP": "INDEX", "PPIKR": "INDEX", "PPIKZ": "INDEX", "PPILT": "INDEX", "PPILU": "INDEX", "PPILV": "INDEX", "PPIMA": "INDEX", "PPIME": "INDEX", "PPIMK": "INDEX", "PPIMT": "INDEX", "PPIMX": "INDEX", "PPIMY": "INDEX", "PPINL": "INDEX", "PPINO": "INDEX", "PPIPA": "INDEX", "PPIPE": "INDEX", "PPIPH": "INDEX", "PPIPK": "INDEX", "PPIPL": "INDEX", "PPIPT": "INDEX", "PPIQA": "INDEX", "PPIRO": "INDEX", "PPIRS": "INDEX", "PPIRU": "INDEX", "PPISA": "INDEX", "PPISE": "INDEX", "PPISG": "INDEX", "PPISI": "INDEX", "PPISK": "INDEX", "PPISV": "INDEX", "PPITH": "INDEX", "PPITN": "INDEX", "PPITR": "INDEX", "PPITW": "INDEX", "PPIUA": "INDEX", "PPIUK": "INDEX", "PPIUS": "INDEX", "PPIUY": "INDEX", "PPIVN": "INDEX", "PPIZA": "INDEX", "PPLAT": "USD", "PPOIL": "USD", "PPORK": "USD", "PPOTASH": "USD", "PPOULT": "USD", "PPROPANE": "USD", "PRCAE": "DOMESTIC", "PRCAR": "DOMESTIC", "PRCAT": "DOMESTIC", "PRCAU": "DOMESTIC", "PRCAZ": "DOMESTIC", "PRCBD": "DOMESTIC", "PRCBE": "DOMESTIC", "PRCBG": "DOMESTIC", "PRCBR": "DOMESTIC", "PRCBW": "DOMESTIC", "PRCBY": "DOMESTIC", "PRCCA": "DOMESTIC", "PRCCH": "DOMESTIC", "PRCCL": "DOMESTIC", "PRCCN": "DOMESTIC", "PRCCO": "DOMESTIC", "PRCCY": "DOMESTIC", "PRCCZ": "DOMESTIC", "PRCDE": "DOMESTIC", "PRCDK": "DOMESTIC", "PRCEA": "DOMESTIC", "PRCEE": "DOMESTIC", "PRCES": "DOMESTIC", "PRCEU": "DOMESTIC", "PRCFI": "DOMESTIC", "PRCFR": "DOMESTIC", "PRCGR": "DOMESTIC", "PRCHK": "DOMESTIC", "PRCHR": "DOMESTIC", "PRCHU": "DOMESTIC", "PRCID": "DOMESTIC", "PRCIE": "DOMESTIC", "PRCIL": "DOMESTIC", "PRCIN": "DOMESTIC", "PRCIR": "DOMESTIC", "PRCIT": "DOMESTIC", "PRCJP": "DOMESTIC", "PRCKR": "DOMESTIC", "PRCKZ": "DOMESTIC", "PRCLT": "DOMESTIC", "PRCLU": "DOMESTIC", "PRCLV": "DOMESTIC", "PRCMA": "DOMESTIC", "PRCME": "DOMESTIC", "PRCMK": "DOMESTIC", "PRCMN": "DOMESTIC", "PRCMT": "DOMESTIC", "PRCMX": "DOMESTIC", "PRCMY": "DOMESTIC", "PRCNG": "DOMESTIC", "PRCNL": "DOMESTIC", "PRCNO": "DOMESTIC", "PRCNZ": "DOMESTIC", "PRCPE": "DOMESTIC", "PRCPH": "DOMESTIC", "PRCPK": "DOMESTIC", "PRCPL": "DOMESTIC", "PRCPT": "DOMESTIC", "PRCQA": "DOMESTIC", "PRCRO": "DOMESTIC", "PRCRS": "DOMESTIC", "PRCRU": "DOMESTIC", "PRCSA": "DOMESTIC", "PRCSE": "DOMESTIC", "PRCSG": "DOMESTIC", "PRCSI": "DOMESTIC", "PRCSK": "DOMESTIC", "PRCTH": "DOMESTIC", "PRCTR": "DOMESTIC", "PRCTW": "DOMESTIC", "PRCUA": "DOMESTIC", "PRCUK": "DOMESTIC", "PRCUS": "DOMESTIC", "PRCVN": "DOMESTIC", "PRCZA": "DOMESTIC", "PREODOM": "USD", "PRICENPQ": "USD", "PRIDEBTBG": "PERCENT", "PRIDEBTCY": "PERCENT", "PRIDEBTEE": "PERCENT", "PRIDEBTHR": "PERCENT", "PRIDEBTLT": "PERCENT", "PRIDEBTLV": "PERCENT", "PRIDEBTMT": "PERCENT", "PRIDEBTRO": "PERCENT", "PRIDEBTSI": "PERCENT", "PRIDEBTSK": "PERCENT", "PROIL": "USD", "PRUBB": "USD", "PSALM": "USD", "PSAWMAL": "USD", "PSAWORE": "USD", "PSHRI": "USD", "PSILLUMP": "USD", "PSILVER": "USD", "PSMEA": "USD", "PSOIL": "USD", "PSORG": "USD", "PSOYB": "USD", "PSUGAISA": "USD", "PSUGAUSA": "USD", "PSUNO": "USD", "PTEA": "USD", "PTEAINDIA": "USD", "PTEAMOM": "USD", "PTEASL": "USD", "PTIN": "USD", "PTOMATO": "USD", "PUCAE": "DOMESTIC", "PUCAL": "DOMESTIC", "PUCAR": "DOMESTIC", "PUCAT": "DOMESTIC", "PUCAU": "DOMESTIC", "PUCAZ": "DOMESTIC", "PUCBD": "DOMESTIC", "PUCBE": "DOMESTIC", "PUCBG": "DOMESTIC", "PUCBR": "DOMESTIC", "PUCBW": "DOMESTIC", "PUCBY": "DOMESTIC", "PUCCA": "DOMESTIC", "PUCCH": "DOMESTIC", "PUCCL": "DOMESTIC", "PUCCM": "DOMESTIC", "PUCCN": "DOMESTIC", "PUCCO": "DOMESTIC", "PUCCY": "DOMESTIC", "PUCCZ": "DOMESTIC", "PUCDE": "DOMESTIC", "PUCDK": "DOMESTIC", "PUCEA": "DOMESTIC", "PUCEE": "DOMESTIC", "PUCES": "DOMESTIC", "PUCEU": "DOMESTIC", "PUCFI": "DOMESTIC", "PUCFR": "DOMESTIC", "PUCGR": "DOMESTIC", "PUCHK": "DOMESTIC", "PUCHR": "DOMESTIC", "PUCHU": "DOMESTIC", "PUCID": "DOMESTIC", "PUCIE": "DOMESTIC", "PUCIL": "DOMESTIC", "PUCIN": "DOMESTIC", "PUCIR": "DOMESTIC", "PUCIT": "DOMESTIC", "PUCJP": "DOMESTIC", "PUCKR": "DOMESTIC", "PUCKZ": "DOMESTIC", "PUCLT": "DOMESTIC", "PUCLU": "DOMESTIC", "PUCLV": "DOMESTIC", "PUCMA": "DOMESTIC", "PUCME": "DOMESTIC", "PUCMK": "DOMESTIC", "PUCMT": "DOMESTIC", "PUCMX": "DOMESTIC", "PUCMY": "DOMESTIC", "PUCNG": "DOMESTIC", "PUCNL": "DOMESTIC", "PUCNO": "DOMESTIC", "PUCNZ": "DOMESTIC", "PUCPE": "DOMESTIC", "PUCPH": "DOMESTIC", "PUCPK": "DOMESTIC", "PUCPL": "DOMESTIC", "PUCPT": "DOMESTIC", "PUCQA": "DOMESTIC", "PUCRO": "DOMESTIC", "PUCRS": "DOMESTIC", "PUCRU": "DOMESTIC", "PUCSA": "DOMESTIC", "PUCSE": "DOMESTIC", "PUCSG": "DOMESTIC", "PUCSI": "DOMESTIC", "PUCSK": "DOMESTIC", "PUCTH": "DOMESTIC", "PUCTR": "DOMESTIC", "PUCTW": "DOMESTIC", "PUCUA": "DOMESTIC", "PUCUK": "DOMESTIC", "PUCUS": "DOMESTIC", "PUCVN": "DOMESTIC", "PUCZA": "DOMESTIC", "PURAN": "USD", "PUREA": "USD", "PVANPENT": "USD", "PWHEAMT": "USD", "PWOOLC": "USD", "PWOOLF": "USD", "PZINC": "USD", "RCIAE": "DOMESTIC", "RCIAR": "DOMESTIC", "RCIAU": "DOMESTIC", "RCIAZ": "DOMESTIC", "RCIBW": "DOMESTIC", "RCIBY": "DOMESTIC", "RCICN": "DOMESTIC", "RCIHK": "DOMESTIC", "RCIIN": "DOMESTIC", "RCIIR": "DOMESTIC", "RCIKH": "DOMESTIC", "RCIKR": "DOMESTIC", "RCIMN": "DOMESTIC", "RCIMO": "DOMESTIC", "RCIMY": "DOMESTIC", "RCING": "DOMESTIC", "RCINO": null, "RCINZ": "DOMESTIC", "RCIPA": "DOMESTIC", "RCIPH": "DOMESTIC", "RCIPK": "DOMESTIC", "RCISA": "DOMESTIC", "RCISG": "DOMESTIC", "RCITH": "DOMESTIC", "RCITW": "DOMESTIC", "RCIUA": "DOMESTIC", "RCIUK": "DOMESTIC", "RCIUS": "DOMESTIC", "RCIVN": "DOMESTIC", "RCIZA": "DOMESTIC", "RCONAE": "DOMESTIC", "RCONAL": "DOMESTIC", "RCONAT": "DOMESTIC", "RCONBA": "DOMESTIC", "RCONBD": "DOMESTIC", "RCONBE": "DOMESTIC", "RCONBG": "DOMESTIC", "RCONBY": "DOMESTIC", "RCONCA": "DOMESTIC", "RCONCH": "DOMESTIC", "RCONCL": "USD", "RCONCY": "DOMESTIC", "RCONCZ": "DOMESTIC", "RCONDE": "DOMESTIC", "RCONDK": "DOMESTIC", "RCONEA": "DOMESTIC", "RCONEE": "DOMESTIC", "RCONES": "DOMESTIC", "RCONEU": "DOMESTIC", "RCONFI": "DOMESTIC", "RCONFR": "DOMESTIC", "RCONGR": "DOMESTIC", "RCONHR": "DOMESTIC", "RCONHU": "DOMESTIC", "RCONIE": "DOMESTIC", "RCONIT": "DOMESTIC", "RCONKR": "DOMESTIC", "RCONLT": "DOMESTIC", "RCONLU": "DOMESTIC", "RCONLV": "DOMESTIC", "RCONMK": "DOMESTIC", "RCONMT": "DOMESTIC", "RCONMX": "DOMESTIC", "RCONNL": "DOMESTIC", "RCONNO": "DOMESTIC", "RCONPL": "DOMESTIC", "RCONPT": "DOMESTIC", "RCONRO": "DOMESTIC", "RCONRS": "DOMESTIC", "RCONSE": "DOMESTIC", "RCONSI": "DOMESTIC", "RCONSK": "DOMESTIC", "RCONTH": "DOMESTIC", "RCONUA": "DOMESTIC", "RCONUK": "DOMESTIC", "REERAE": "INDEX", "REERAR": "INDEX", "REERAT": "INDEX", "REERAU": "INDEX", "REERAZ": "INDEX", "REERBE": "INDEX", "REERBG": "INDEX", "REERBR": "INDEX", "REERCA": "INDEX", "REERCH": "INDEX", "REERCL": "INDEX", "REERCN": "INDEX", "REERCO": "INDEX", "REERCY": "INDEX", "REERCZ": "INDEX", "REERDE": "INDEX", "REERDK": "INDEX", "REERDZ": "INDEX", "REEREA": "INDEX", "REEREE": "INDEX", "REERES": "INDEX", "REERFI": "INDEX", "REERFR": "INDEX", "REERGR": "INDEX", "REERHK": "INDEX", "REERHR": "INDEX", "REERHU": "INDEX", "REERID": "INDEX", "REERIE": "INDEX", "REERIL": "INDEX", "REERIN": "INDEX", "REERIS": "INDEX", "REERIT": "INDEX", "REERJP": "INDEX", "REERKR": "INDEX", "REERLT": "INDEX", "REERLU": "INDEX", "REERLV": "INDEX", "REERMT": "INDEX", "REERMX": "INDEX", "REERMY": "INDEX", "REERNL": "INDEX", "REERNO": "INDEX", "REERNZ": "INDEX", "REERPE": "INDEX", "REERPH": "INDEX", "REERPL": "INDEX", "REERPT": "INDEX", "REERPY": "INDEX", "REERRO": "INDEX", "REERRS": "INDEX", "REERRU": "INDEX", "REERSA": "INDEX", "REERSE": "INDEX", "REERSG": "INDEX", "REERSI": "INDEX", "REERSK": "INDEX", "REERTH": "INDEX", "REERTR": "INDEX", "REERTW": "INDEX", "REERUK": "INDEX", "REERUS": "INDEX", "REERZA": "INDEX", "RETAAL": "INDEX", "RETAAR": "DOMESTIC", "RETAAT": "INDEX", "RETAAU": "DOMESTIC", "RETAAZ": "INDEX", "RETABA": "INDEX", "RETABE": "INDEX", "RETABG": "INDEX", "RETABR": "INDEX", "RETACA": "DOMESTIC", "RETACH": "INDEX", "RETACN": "DOMESTIC", "RETACO": "INDEX", "RETACY": "INDEX", "RETACZ": "INDEX", "RETADE": "INDEX", "RETADK": "INDEX", "RETAEE": "INDEX", "RETAES": "INDEX", "RETAEU": "INDEX", "RETAFI": "INDEX", "RETAFR": "INDEX", "RETAGR": "INDEX", "RETAHK": "INDEX", "RETAHR": "INDEX", "RETAHU": "INDEX", "RETAID": "INDEX", "RETAIE": "INDEX", "RETAIL": "INDEX", "RETAIR": "DOMESTIC", "RETAIT": "INDEX", "RETAJP": "INDEX", "RETAKR": "INDEX", "RETALT": "INDEX", "RETALU": "INDEX", "RETALV": "INDEX", "RETAME": "INDEX", "RETAMK": "INDEX", "RETAMT": "INDEX", "RETAMX": "INDEX", "RETAMY": "INDEX", "RETANL": "INDEX", "RETANO": "INDEX", "RETANZ": "DOMESTIC", "RETAPL": "INDEX", "RETAPT": "INDEX", "RETARO": "INDEX", "RETARS": "INDEX", "RETARU": "INDEX", "RETASE": "INDEX", "RETASG": "INDEX", "RETASI": "INDEX", "RETASK": "INDEX", "RETATH": "INDEX", "RETATR": "INDEX", "RETATW": "INDEX", "RETAUK": "INDEX", "RETAUS": "INDEX", "RETAVN": "INDEX", "RETAZA": "INDEX", "REXPAE": "DOMESTIC", "REXPAL": "DOMESTIC", "REXPAR": "DOMESTIC", "REXPAT": "DOMESTIC", "REXPAU": "DOMESTIC", "REXPAZ": "DOMESTIC", "REXPBA": "DOMESTIC", "REXPBD": "DOMESTIC", "REXPBE": "DOMESTIC", "REXPBG": "DOMESTIC", "REXPBR": "DOMESTIC", "REXPBW": "DOMESTIC", "REXPBY": "DOMESTIC", "REXPCA": "DOMESTIC", "REXPCH": "DOMESTIC", "REXPCL": "DOMESTIC", "REXPCN": "DOMESTIC", "REXPCO": "DOMESTIC", "REXPCY": "DOMESTIC", "REXPCZ": "DOMESTIC", "REXPDE": "DOMESTIC", "REXPDK": "DOMESTIC", "REXPEA": "DOMESTIC", "REXPEE": "DOMESTIC", "REXPES": "DOMESTIC", "REXPEU": "DOMESTIC", "REXPFI": "DOMESTIC", "REXPFR": "DOMESTIC", "REXPGR": "DOMESTIC", "REXPHK": "DOMESTIC", "REXPHN": "DOMESTIC", "REXPHR": "DOMESTIC", "REXPHU": "DOMESTIC", "REXPID": "DOMESTIC", "REXPIE": "DOMESTIC", "REXPIL": "DOMESTIC", "REXPIN": "DOMESTIC", "REXPIR": "DOMESTIC", "REXPIT": "DOMESTIC", "REXPJP": "DOMESTIC", "REXPKH": "DOMESTIC", "REXPKR": "DOMESTIC", "REXPLT": "DOMESTIC", "REXPLU": "DOMESTIC", "REXPLV": "DOMESTIC", "REXPMA": "DOMESTIC", "REXPMK": "DOMESTIC", "REXPMN": "DOMESTIC", "REXPMO": "DOMESTIC", "REXPMT": "DOMESTIC", "REXPMX": "DOMESTIC", "REXPMY": "DOMESTIC", "REXPNG": "DOMESTIC", "REXPNL": "DOMESTIC", "REXPNO": "DOMESTIC", "REXPNZ": "DOMESTIC", "REXPPA": "DOMESTIC", "REXPPE": "DOMESTIC", "REXPPH": "DOMESTIC", "REXPPK": "DOMESTIC", "REXPPL": "DOMESTIC", "REXPPT": "DOMESTIC", "REXPRO": "DOMESTIC", "REXPRS": "DOMESTIC", "REXPRU": "DOMESTIC", "REXPRW": "DOMESTIC", "REXPSA": "DOMESTIC", "REXPSE": "DOMESTIC", "REXPSG": "DOMESTIC", "REXPSI": "DOMESTIC", "REXPSK": "DOMESTIC", "REXPTH": "DOMESTIC", "REXPTR": "DOMESTIC", "REXPTW": "DOMESTIC", "REXPUA": "DOMESTIC", "REXPUK": "DOMESTIC", "REXPUS": "DOMESTIC", "REXPUZ": "DOMESTIC", "REXPZA": "DOMESTIC", "RGCFAT": "DOMESTIC", "RGCFBA": "DOMESTIC", "RGCFBE": "DOMESTIC", "RGCFBG": "DOMESTIC", "RGCFBY": "DOMESTIC", "RGCFCH": "DOMESTIC", "RGCFCO": "DOMESTIC", "RGCFCY": "DOMESTIC", "RGCFCZ": "DOMESTIC", "RGCFDE": "DOMESTIC", "RGCFDK": "DOMESTIC", "RGCFEA": "DOMESTIC", "RGCFEE": "DOMESTIC", "RGCFES": "DOMESTIC", "RGCFEU": "DOMESTIC", "RGCFFI": "DOMESTIC", "RGCFFR": "DOMESTIC", "RGCFGR": "DOMESTIC", "RGCFHR": "DOMESTIC", "RGCFHU": "DOMESTIC", "RGCFIE": "DOMESTIC", "RGCFIT": "DOMESTIC", "RGCFKR": "DOMESTIC", "RGCFLT": "DOMESTIC", "RGCFLU": "DOMESTIC", "RGCFLV": "DOMESTIC", "RGCFMK": "DOMESTIC", "RGCFMT": "DOMESTIC", "RGCFNL": "DOMESTIC", "RGCFNO": "DOMESTIC", "RGCFNZ": "DOMESTIC", "RGCFPE": "DOMESTIC", "RGCFPK": "DOMESTIC", "RGCFPL": "DOMESTIC", "RGCFPT": "DOMESTIC", "RGCFRO": "DOMESTIC", "RGCFRS": "DOMESTIC", "RGCFRU": "DOMESTIC", "RGCFRW": "DOMESTIC", "RGCFSE": "DOMESTIC", "RGCFSI": "DOMESTIC", "RGCFSK": "DOMESTIC", "RGCFUA": "DOMESTIC", "RGCFUK": "DOMESTIC", "RGDPAE": "DOMESTIC", "RGDPAL": "DOMESTIC", "RGDPAR": "DOMESTIC", "RGDPAT": "DOMESTIC", "RGDPAU": "DOMESTIC", "RGDPAZ": "DOMESTIC", "RGDPBA": "DOMESTIC", "RGDPBD": "DOMESTIC", "RGDPBE": "DOMESTIC", "RGDPBG": "DOMESTIC", "RGDPBR": "DOMESTIC", "RGDPBW": "DOMESTIC", "RGDPBY": "DOMESTIC", "RGDPCA": "DOMESTIC", "RGDPCH": "DOMESTIC", "RGDPCL": "DOMESTIC", "RGDPCM": "DOMESTIC", "RGDPCN": "DOMESTIC", "RGDPCO": "DOMESTIC", "RGDPCR": "DOMESTIC", "RGDPCY": "DOMESTIC", "RGDPCZ": "DOMESTIC", "RGDPDE": "DOMESTIC", "RGDPDK": "DOMESTIC", "RGDPEA": "DOMESTIC", "RGDPEE": "DOMESTIC", "RGDPEG": "DOMESTIC", "RGDPES": "DOMESTIC", "RGDPEU": "DOMESTIC", "RGDPFI": "DOMESTIC", "RGDPFR": "DOMESTIC", "RGDPGR": "DOMESTIC", "RGDPHK": "DOMESTIC", "RGDPHN": "DOMESTIC", "RGDPHR": "DOMESTIC", "RGDPHU": "DOMESTIC", "RGDPID": "DOMESTIC", "RGDPIE": "DOMESTIC", "RGDPIL": "DOMESTIC", "RGDPIN": "DOMESTIC", "RGDPIR": "DOMESTIC", "RGDPIT": "DOMESTIC", "RGDPJO": "DOMESTIC", "RGDPJP": "DOMESTIC", "RGDPKH": "DOMESTIC", "RGDPKR": "DOMESTIC", "RGDPKZ": "DOMESTIC", "RGDPLA": "DOMESTIC", "RGDPLT": "DOMESTIC", "RGDPLU": "DOMESTIC", "RGDPLV": "DOMESTIC", "RGDPMA": "DOMESTIC", "RGDPMK": "DOMESTIC", "RGDPMN": "DOMESTIC", "RGDPMO": "DOMESTIC", "RGDPMT": "DOMESTIC", "RGDPMX": "DOMESTIC", "RGDPMY": "DOMESTIC", "RGDPNG": "DOMESTIC", "RGDPNL": "DOMESTIC", "RGDPNO": "DOMESTIC", "RGDPNZ": "DOMESTIC", "RGDPPA": "DOMESTIC", "RGDPPCAL": "VOLUME", "RGDPPCAT": "VOLUME", "RGDPPCAU": "DOMESTIC", "RGDPPCBE": "VOLUME", "RGDPPCBG": "VOLUME", "RGDPPCBR": "INDEX", "RGDPPCCH": "VOLUME", "RGDPPCCY": "VOLUME", "RGDPPCCZ": "VOLUME", "RGDPPCDE": "VOLUME", "RGDPPCDK": "VOLUME", "RGDPPCEA": "VOLUME", "RGDPPCEE": "VOLUME", "RGDPPCES": "VOLUME", "RGDPPCEU": "VOLUME", "RGDPPCFI": "VOLUME", "RGDPPCFR": "VOLUME", "RGDPPCGR": "VOLUME", "RGDPPCHR": "VOLUME", "RGDPPCHU": "VOLUME", "RGDPPCIE": "VOLUME", "RGDPPCIS": "VOLUME", "RGDPPCIT": "VOLUME", "RGDPPCLT": "VOLUME", "RGDPPCLU": "VOLUME", "RGDPPCLV": "VOLUME", "RGDPPCME": "VOLUME", "RGDPPCMK": "VOLUME", "RGDPPCMT": "VOLUME", "RGDPPCNL": "VOLUME", "RGDPPCNO": "VOLUME", "RGDPPCNZ": "DOMESTIC", "RGDPPCPL": "VOLUME", "RGDPPCPT": "VOLUME", "RGDPPCRO": "VOLUME", "RGDPPCRS": "VOLUME", "RGDPPCSE": "VOLUME", "RGDPPCSI": "VOLUME", "RGDPPCSK": "VOLUME", "RGDPPCTR": "VOLUME", "RGDPPCUS": "DOMESTIC", "RGDPPE": "DOMESTIC", "RGDPPH": "DOMESTIC", "RGDPPK": "DOMESTIC", "RGDPPL": "DOMESTIC", "RGDPPT": "DOMESTIC", "RGDPQA": "DOMESTIC", "RGDPRO": "DOMESTIC", "RGDPRS": "DOMESTIC", "RGDPRU": "DOMESTIC", "RGDPRW": "DOMESTIC", "RGDPSA": "DOMESTIC", "RGDPSE": "DOMESTIC", "RGDPSG": "DOMESTIC", "RGDPSI": "DOMESTIC", "RGDPSK": "DOMESTIC", "RGDPTH": "DOMESTIC", "RGDPTN": "DOMESTIC", "RGDPTR": "DOMESTIC", "RGDPTW": "DOMESTIC", "RGDPUA": "DOMESTIC", "RGDPUK": "DOMESTIC", "RGDPUS": "DOMESTIC", "RGDPUY": "DOMESTIC", "RGDPUZ": "DOMESTIC", "RGDPVN": "DOMESTIC", "RGDPZA": "DOMESTIC", "RGFCFAE": "DOMESTIC", "RGFCFAL": "DOMESTIC", "RGFCFAR": "DOMESTIC", "RGFCFAT": "DOMESTIC", "RGFCFAU": "DOMESTIC", "RGFCFAZ": "DOMESTIC", "RGFCFBA": "DOMESTIC", "RGFCFBD": "DOMESTIC", "RGFCFBE": "DOMESTIC", "RGFCFBG": "DOMESTIC", "RGFCFBR": "DOMESTIC", "RGFCFBW": "DOMESTIC", "RGFCFBY": "DOMESTIC", "RGFCFCA": "DOMESTIC", "RGFCFCH": "DOMESTIC", "RGFCFCL": "DOMESTIC", "RGFCFCN": "INDEX", "RGFCFCO": "DOMESTIC", "RGFCFCR": "DOMESTIC", "RGFCFCY": "DOMESTIC", "RGFCFCZ": "DOMESTIC", "RGFCFDE": "DOMESTIC", "RGFCFDK": "DOMESTIC", "RGFCFEA": "DOMESTIC", "RGFCFEE": "DOMESTIC", "RGFCFES": "DOMESTIC", "RGFCFEU": "DOMESTIC", "RGFCFFI": "DOMESTIC", "RGFCFFR": "DOMESTIC", "RGFCFGR": "DOMESTIC", "RGFCFHK": "DOMESTIC", "RGFCFHN": "DOMESTIC", "RGFCFHR": "DOMESTIC", "RGFCFHU": "DOMESTIC", "RGFCFID": "DOMESTIC", "RGFCFIE": "DOMESTIC", "RGFCFIL": "DOMESTIC", "RGFCFIN": "DOMESTIC", "RGFCFIR": "DOMESTIC", "RGFCFIT": "DOMESTIC", "RGFCFJP": "DOMESTIC", "RGFCFKH": "DOMESTIC", "RGFCFKR": "DOMESTIC", "RGFCFLT": "DOMESTIC", "RGFCFLU": "DOMESTIC", "RGFCFLV": "DOMESTIC", "RGFCFMA": "DOMESTIC", "RGFCFMN": "DOMESTIC", "RGFCFMO": "DOMESTIC", "RGFCFMT": "DOMESTIC", "RGFCFMX": "DOMESTIC", "RGFCFMY": "DOMESTIC", "RGFCFNG": "DOMESTIC", "RGFCFNL": "DOMESTIC", "RGFCFNO": "DOMESTIC", "RGFCFNZ": "DOMESTIC", "RGFCFPH": "DOMESTIC", "RGFCFPK": "DOMESTIC", "RGFCFPL": "DOMESTIC", "RGFCFPT": "DOMESTIC", "RGFCFRO": "DOMESTIC", "RGFCFRS": "DOMESTIC", "RGFCFRU": "DOMESTIC", "RGFCFSA": "DOMESTIC", "RGFCFSE": "DOMESTIC", "RGFCFSG": "DOMESTIC", "RGFCFSI": "DOMESTIC", "RGFCFSK": "DOMESTIC", "RGFCFTH": "DOMESTIC", "RGFCFTR": "DOMESTIC", "RGFCFTW": "DOMESTIC", "RGFCFUA": "DOMESTIC", "RGFCFUK": "DOMESTIC", "RGFCFUS": "DOMESTIC", "RGFCFVN": "DOMESTIC", "RGFCFZA": "DOMESTIC", "RIMPAE": "DOMESTIC", "RIMPAL": "DOMESTIC", "RIMPAR": "DOMESTIC", "RIMPAT": "DOMESTIC", "RIMPAU": "DOMESTIC", "RIMPAZ": "DOMESTIC", "RIMPBA": "DOMESTIC", "RIMPBD": "DOMESTIC", "RIMPBE": "DOMESTIC", "RIMPBG": "DOMESTIC", "RIMPBR": "DOMESTIC", "RIMPBW": "DOMESTIC", "RIMPBY": "DOMESTIC", "RIMPCA": "DOMESTIC", "RIMPCH": "DOMESTIC", "RIMPCL": "DOMESTIC", "RIMPCN": "DOMESTIC", "RIMPCO": "DOMESTIC", "RIMPCY": "DOMESTIC", "RIMPCZ": "DOMESTIC", "RIMPDE": "DOMESTIC", "RIMPDK": "DOMESTIC", "RIMPEA": "DOMESTIC", "RIMPEE": "DOMESTIC", "RIMPES": "DOMESTIC", "RIMPEU": "DOMESTIC", "RIMPFI": "DOMESTIC", "RIMPFR": "DOMESTIC", "RIMPGR": "DOMESTIC", "RIMPHK": "DOMESTIC", "RIMPHN": "DOMESTIC", "RIMPHR": "DOMESTIC", "RIMPHU": "DOMESTIC", "RIMPID": "DOMESTIC", "RIMPIE": "DOMESTIC", "RIMPIL": "DOMESTIC", "RIMPIN": "DOMESTIC", "RIMPIR": "DOMESTIC", "RIMPIT": "DOMESTIC", "RIMPJP": "DOMESTIC", "RIMPKH": "DOMESTIC", "RIMPKR": "DOMESTIC", "RIMPLT": "DOMESTIC", "RIMPLU": "DOMESTIC", "RIMPLV": "DOMESTIC", "RIMPMA": "DOMESTIC", "RIMPMK": "DOMESTIC", "RIMPMN": "DOMESTIC", "RIMPMO": "DOMESTIC", "RIMPMT": "DOMESTIC", "RIMPMX": "DOMESTIC", "RIMPMY": "DOMESTIC", "RIMPNG": "DOMESTIC", "RIMPNL": "DOMESTIC", "RIMPNO": "DOMESTIC", "RIMPNZ": "DOMESTIC", "RIMPPA": "DOMESTIC", "RIMPPE": "DOMESTIC", "RIMPPH": "DOMESTIC", "RIMPPK": "DOMESTIC", "RIMPPL": "DOMESTIC", "RIMPPT": "DOMESTIC", "RIMPRO": "DOMESTIC", "RIMPRS": "DOMESTIC", "RIMPRU": "DOMESTIC", "RIMPRW": "DOMESTIC", "RIMPSA": "DOMESTIC", "RIMPSE": "DOMESTIC", "RIMPSG": "DOMESTIC", "RIMPSI": "DOMESTIC", "RIMPSK": "DOMESTIC", "RIMPTH": "DOMESTIC", "RIMPTR": "DOMESTIC", "RIMPTW": "DOMESTIC", "RIMPUA": "DOMESTIC", "RIMPUK": "DOMESTIC", "RIMPUS": "DOMESTIC", "RIMPUZ": "DOMESTIC", "RIMPZA": "DOMESTIC", "RPRCAE": "DOMESTIC", "RPRCAR": "DOMESTIC", "RPRCAT": "DOMESTIC", "RPRCAU": "DOMESTIC", "RPRCAZ": "DOMESTIC", "RPRCBA": "DOMESTIC", "RPRCBD": "DOMESTIC", "RPRCBE": "DOMESTIC", "RPRCBG": "DOMESTIC", "RPRCBR": "DOMESTIC", "RPRCBY": "DOMESTIC", "RPRCCA": "DOMESTIC", "RPRCCH": "DOMESTIC", "RPRCCL": "DOMESTIC", "RPRCCN": "DOMESTIC", "RPRCCO": "DOMESTIC", "RPRCCR": "DOMESTIC", "RPRCCY": "DOMESTIC", "RPRCCZ": "DOMESTIC", "RPRCDE": "DOMESTIC", "RPRCDK": "DOMESTIC", "RPRCEA": "DOMESTIC", "RPRCEE": "DOMESTIC", "RPRCES": "DOMESTIC", "RPRCEU": "DOMESTIC", "RPRCFI": "DOMESTIC", "RPRCFR": "DOMESTIC", "RPRCGR": "DOMESTIC", "RPRCHK": "DOMESTIC", "RPRCHN": "DOMESTIC", "RPRCHR": "DOMESTIC", "RPRCHU": "DOMESTIC", "RPRCID": "DOMESTIC", "RPRCIE": "DOMESTIC", "RPRCIL": "DOMESTIC", "RPRCIN": "DOMESTIC", "RPRCIR": "DOMESTIC", "RPRCIT": "DOMESTIC", "RPRCJP": "DOMESTIC", "RPRCKH": "DOMESTIC", "RPRCKR": "DOMESTIC", "RPRCLT": "DOMESTIC", "RPRCLU": "DOMESTIC", "RPRCLV": "DOMESTIC", "RPRCMA": "DOMESTIC", "RPRCMK": "DOMESTIC", "RPRCMO": "DOMESTIC", "RPRCMT": "DOMESTIC", "RPRCMX": "DOMESTIC", "RPRCMY": "DOMESTIC", "RPRCNG": "DOMESTIC", "RPRCNL": "DOMESTIC", "RPRCNO": "DOMESTIC", "RPRCNZ": "DOMESTIC", "RPRCPE": "DOMESTIC", "RPRCPH": "DOMESTIC", "RPRCPK": "DOMESTIC", "RPRCPL": "DOMESTIC", "RPRCPT": "DOMESTIC", "RPRCRO": "DOMESTIC", "RPRCRS": "DOMESTIC", "RPRCRU": "DOMESTIC", "RPRCRW": "DOMESTIC", "RPRCSA": "DOMESTIC", "RPRCSE": "DOMESTIC", "RPRCSG": "DOMESTIC", "RPRCSI": "DOMESTIC", "RPRCSK": "DOMESTIC", "RPRCTH": "DOMESTIC", "RPRCTR": "DOMESTIC", "RPRCTW": "DOMESTIC", "RPRCUA": "DOMESTIC", "RPRCUK": "DOMESTIC", "RPRCUS": "DOMESTIC", "RPRCUZ": "DOMESTIC", "RPRCVN": "DOMESTIC", "RPRCZA": "DOMESTIC", "RPUCAE": "DOMESTIC", "RPUCAL": "DOMESTIC", "RPUCAR": "DOMESTIC", "RPUCAT": "DOMESTIC", "RPUCAU": "DOMESTIC", "RPUCAZ": "DOMESTIC", "RPUCBA": "DOMESTIC", "RPUCBD": "DOMESTIC", "RPUCBE": "DOMESTIC", "RPUCBG": "DOMESTIC", "RPUCBR": "DOMESTIC", "RPUCBW": "DOMESTIC", "RPUCBY": "DOMESTIC", "RPUCCA": "DOMESTIC", "RPUCCH": "DOMESTIC", "RPUCCL": "DOMESTIC", "RPUCCN": "DOMESTIC", "RPUCCO": "DOMESTIC", "RPUCCR": "DOMESTIC", "RPUCCY": "DOMESTIC", "RPUCCZ": "DOMESTIC", "RPUCDE": "DOMESTIC", "RPUCDK": "DOMESTIC", "RPUCEA": "DOMESTIC", "RPUCEE": "DOMESTIC", "RPUCES": "DOMESTIC", "RPUCEU": "DOMESTIC", "RPUCFI": "DOMESTIC", "RPUCFR": "DOMESTIC", "RPUCGR": "DOMESTIC", "RPUCHK": "DOMESTIC", "RPUCHN": "DOMESTIC", "RPUCHR": "DOMESTIC", "RPUCHU": "DOMESTIC", "RPUCID": "DOMESTIC", "RPUCIE": "DOMESTIC", "RPUCIL": "DOMESTIC", "RPUCIN": "DOMESTIC", "RPUCIR": "DOMESTIC", "RPUCIT": "DOMESTIC", "RPUCJP": "DOMESTIC", "RPUCKH": "DOMESTIC", "RPUCKR": "DOMESTIC", "RPUCLT": "DOMESTIC", "RPUCLU": "DOMESTIC", "RPUCLV": "DOMESTIC", "RPUCMA": "DOMESTIC", "RPUCMK": "DOMESTIC", "RPUCMN": "DOMESTIC", "RPUCMO": "DOMESTIC", "RPUCMT": "DOMESTIC", "RPUCMX": "DOMESTIC", "RPUCMY": "DOMESTIC", "RPUCNG": "DOMESTIC", "RPUCNL": "DOMESTIC", "RPUCNO": "DOMESTIC", "RPUCNZ": "DOMESTIC", "RPUCPA": "DOMESTIC", "RPUCPE": "DOMESTIC", "RPUCPH": "DOMESTIC", "RPUCPK": "DOMESTIC", "RPUCPL": "DOMESTIC", "RPUCPT": "DOMESTIC", "RPUCRO": "DOMESTIC", "RPUCRS": "DOMESTIC", "RPUCRU": "DOMESTIC", "RPUCRW": "DOMESTIC", "RPUCSA": "DOMESTIC", "RPUCSE": "DOMESTIC", "RPUCSG": "DOMESTIC", "RPUCSI": "DOMESTIC", "RPUCSK": "DOMESTIC", "RPUCTH": "DOMESTIC", "RPUCTR": "DOMESTIC", "RPUCTW": "DOMESTIC", "RPUCUA": "DOMESTIC", "RPUCUK": "DOMESTIC", "RPUCUS": "DOMESTIC", "RPUCUZ": "DOMESTIC", "RPUCVN": "DOMESTIC", "RPUCZA": "DOMESTIC", "SEIAE": "INDEX", "SEIAT": "INDEX", "SEIAU": "INDEX", "SEIBD": "INDEX", "SEIBE": "INDEX", "SEIBR": "INDEX", "SEICA": "DOMESTIC", "SEICH": "INDEX", "SEICN": "INDEX", "SEIDE": "INDEX", "SEIDK": "INDEX", "SEIES": "INDEX", "SEIFI": "INDEX", "SEIFR": "INDEX", "SEIID": "INDEX", "SEIIE": "INDEX", "SEIIN": "INDEX", "SEIIT": "INDEX", "SEIJP": "INDEX", "SEIKR": "INDEX", "SEILA": "INDEX", "SEILU": "INDEX", "SEIMX": "INDEX", "SEINL": "INDEX", "SEINO": "INDEX", "SEIOM": "INDEX", "SEIPA": "INDEX", "SEIPK": "INDEX", "SEIPL": "INDEX", "SEIRS": "INDEX", "SEIRU": "INDEX", "SEISE": "INDEX", "SEITR": "INDEX", "SEIUS": "INDEX", "SEIUZ": "INDEX", "SEIZA": "INDEX", "SENTAL": "INDEX", "SENTAT": "INDEX", "SENTAU": "INDEX", "SENTBE": "INDEX", "SENTBG": "INDEX", "SENTBR": "INDEX", "SENTCY": "INDEX", "SENTCZ": "INDEX", "SENTDE": "INDEX", "SENTDK": "INDEX", "SENTEE": "INDEX", "SENTES": "INDEX", "SENTEU": "INDEX", "SENTFI": "INDEX", "SENTFR": "INDEX", "SENTGR": "INDEX", "SENTHR": "INDEX", "SENTHU": "INDEX", "SENTIE": "INDEX", "SENTIT": "INDEX", "SENTJP": "INDEX", "SENTKR": "INDEX", "SENTLT": "INDEX", "SENTLU": "INDEX", "SENTLV": "INDEX", "SENTME": "INDEX", "SENTMK": "INDEX", "SENTMT": "INDEX", "SENTNL": "INDEX", "SENTPL": "INDEX", "SENTPT": "INDEX", "SENTRO": "INDEX", "SENTRS": "INDEX", "SENTSE": "INDEX", "SENTSI": "INDEX", "SENTSK": "INDEX", "SENTTR": "INDEX", "SENTUK": "INDEX", "SENTUS": "INDEX", "TBBE": "DOMESTIC", "TBBG": "DOMESTIC", "TBBR": "USD", "TBCA": "DOMESTIC", "TBCZ": "DOMESTIC", "TBDE": "DOMESTIC", "TBDK": "DOMESTIC", "TBEE": "DOMESTIC", "TBEG": "USD", "TBFI": "DOMESTIC", "TBFR": "DOMESTIC", "TBGR": "DOMESTIC", "TBHR": "DOMESTIC", "TBHU": "DOMESTIC", "TBIN": "USD", "TBIT": "DOMESTIC", "TBLT": "DOMESTIC", "TBLU": "DOMESTIC", "TBLV": "DOMESTIC", "TBMK": "DOMESTIC", "TBMT": "DOMESTIC", "TBMX": "DOMESTIC", "TBNL": "DOMESTIC", "TBPL": "DOMESTIC", "TBPT": "DOMESTIC", "TBRO": "DOMESTIC", "TBRS": "DOMESTIC", "TBSE": "DOMESTIC", "TBSI": "DOMESTIC", "TBSK": "DOMESTIC", "TBTH": "USD", "TBUS": "DOMESTIC", "TBVN": "DOMESTIC", "URATEAE": "PERCENT", "URATEAR": "PERCENT", "URATEAT": "PERCENT", "URATEAU": "PERCENT", "URATEAZ": "OTHER NUMBER", "URATEBD": "PERCENT", "URATEBE": "PERCENT", "URATEBG": "PERCENT", "URATEBR": "PERCENT", "URATEBY": "PERCENT", "URATECA": "PERCENT", "URATECH": "PERCENT", "URATECL": "PERCENT", "URATECN": "PERCENT", "URATECO": "PERCENT", "URATECR": "PERCENT", "URATECY": "PERCENT", "URATECZ": "PERCENT", "URATEDE": "PERCENT", "URATEDK": "PERCENT", "URATEDO": "PERCENT", "URATEEA": "PERCENT", "URATEEE": "PERCENT", "URATEEG": "PERCENT", "URATEES": "PERCENT", "URATEEU": "PERCENT", "URATEFI": "PERCENT", "URATEFR": "PERCENT", "URATEGR": "PERCENT", "URATEHK": "PERCENT", "URATEHR": "PERCENT", "URATEHU": "PERCENT", "URATEID": "PERCENT", "URATEIE": "PERCENT", "URATEIL": "PERCENT", "URATEIS": "PERCENT", "URATEIT": "PERCENT", "URATEJO": "PERCENT", "URATEJP": "PERCENT", "URATEKH": "PERCENT", "URATEKR": "PERCENT", "URATEKZ": "PERCENT", "URATELK": "PERCENT", "URATELT": "PERCENT", "URATELU": "PERCENT", "URATELV": "PERCENT", "URATEMA": "PERCENT", "URATEMO": "PERCENT", "URATEMT": "PERCENT", "URATEMX": "PERCENT", "URATEMY": "PERCENT", "URATENL": "PERCENT", "URATENO": "PERCENT", "URATENZ": "PERCENT", "URATEPE": "PERCENT", "URATEPH": "PERCENT", "URATEPK": "PERSONS", "URATEPL": "PERCENT", "URATEPT": "PERCENT", "URATEQA": "PERCENT", "URATERO": "PERCENT", "URATERS": "PERSONS", "URATERU": "PERCENT", "URATESA": "PERCENT", "URATESE": "PERCENT", "URATESG": "PERCENT", "URATESI": "PERCENT", "URATESK": "PERCENT", "URATETH": "PERCENT", "URATETN": "PERCENT", "URATETR": "PERCENT", "URATETW": "PERCENT", "URATEUA": "PERCENT", "URATEUK": "PERCENT", "URATEUS": "PERCENT", "URATEUY": "PERCENT", "URATEVN": "PERCENT", "URATEZA": "PERCENT", "UTILAL": "INDEX", "UTILAR": "OTHER NUMBER", "UTILAT": "PERCENT", "UTILBE": "PERCENT", "UTILBG": "PERCENT", "UTILCA": "INDEX", "UTILCY": "PERCENT", "UTILCZ": "PERCENT", "UTILDE": "PERCENT", "UTILDK": "PERCENT", "UTILEE": "PERCENT", "UTILES": "INDEX", "UTILEU": "INDEX", "UTILFI": "PERCENT", "UTILFR": "PERCENT", "UTILGR": "PERCENT", "UTILHR": "PERCENT", "UTILHU": "PERCENT", "UTILIE": "PERCENT", "UTILIT": "PERCENT", "UTILLT": "PERCENT", "UTILLU": "PERCENT", "UTILLV": "PERCENT", "UTILME": "INDEX", "UTILMK": "INDEX", "UTILMT": "INDEX", "UTILNL": "PERCENT", "UTILPL": "PERCENT", "UTILPT": "PERCENT", "UTILRO": "PERCENT", "UTILRS": "PERCENT", "UTILSE": "PERCENT", "UTILSI": "PERCENT", "UTILSK": "PERCENT", "UTILTR": "INDEX", "UTILUK": "PERCENT", "UTILUS": "PERCENT", "WAGEMANCL": "INDEX", "WAGEMANMX": null, "WAGEMANUS": "DOMESTIC", "WAGEUY": "DOMESTIC", "WAGEVN": "DOMESTIC", "Y10YDAR": "PERCENT", "Y10YDAT": "PERCENT", "Y10YDAU": "PERCENT", "Y10YDBD": "PERCENT", "Y10YDBE": "PERCENT", "Y10YDBG": "PERCENT", "Y10YDCA": "PERCENT", "Y10YDCN": "PERCENT", "Y10YDCY": "PERCENT", "Y10YDCZ": "PERCENT", "Y10YDDE": "PERCENT", "Y10YDDK": "PERCENT", "Y10YDEA": "PERCENT", "Y10YDEE": "PERCENT", "Y10YDES": "PERCENT", "Y10YDEU": "PERCENT", "Y10YDFI": "PERCENT", "Y10YDFR": "PERCENT", "Y10YDGR": "PERCENT", "Y10YDHK": "PERCENT", "Y10YDHR": "PERCENT", "Y10YDHU": "PERCENT", "Y10YDID": "PERCENT", "Y10YDIE": "PERCENT", "Y10YDIN": "PERCENT", "Y10YDIT": "PERCENT", "Y10YDJP": "PERCENT", "Y10YDKR": "PERCENT", "Y10YDLT": "PERCENT", "Y10YDLU": "PERCENT", "Y10YDLV": "PERCENT", "Y10YDMT": "PERCENT", "Y10YDMX": "PERCENT", "Y10YDMY": "PERCENT", "Y10YDNL": "PERCENT", "Y10YDNZ": "PERCENT", "Y10YDPH": "PERCENT", "Y10YDPL": "PERCENT", "Y10YDPT": "PERCENT", "Y10YDQA": "PERCENT", "Y10YDRO": "PERCENT", "Y10YDRU": "PERCENT", "Y10YDSA": "PERCENT", "Y10YDSE": "PERCENT", "Y10YDSG": "PERCENT", "Y10YDSI": "PERCENT", "Y10YDSK": "PERCENT", "Y10YDTH": "PERCENT", "Y10YDTR": "PERCENT", "Y10YDTW": "PERCENT", "Y10YDUA": "PERCENT", "Y10YDUK": "PERCENT", "Y10YDUS": "PERCENT", "Y10YDVN": "PERCENT", "Y10YDZA": "PERCENT"}