"""OpenBB EIA Provider Module Constants."""

# pylint: disable=too-many-lines

from typing import Literal

WpsrCategoryType = Literal[
    "balance_sheet",
    "inputs_and_production",
    "refiner_blender_net_production",
    "crude_petroleum_stocks",
    "gasoline_fuel_stocks",
    "total_gasoline_by_sub_padd",
    "distillate_fuel_oil_stocks",
    "imports",
    "imports_by_country",
    "weekly_estimates",
    "spot_prices_crude_gas_heating",
    "spot_prices_diesel_jet_fuel_propane",
    "retail_prices",
]
WpsrCategoryChoices = [
    "balance_sheet",
    "inputs_and_production",
    "refiner_blender_net_production",
    "crude_petroleum_stocks",
    "gasoline_fuel_stocks",
    "total_gasoline_by_sub_padd",
    "distillate_fuel_oil_stocks",
    "imports",
    "imports_by_country",
    "weekly_estimates",
    "spot_prices_crude_gas_heating",
    "spot_prices_diesel_jet_fuel_propane",
    "retail_prices",
]
WpsrTableType = Literal[
    "all",
    "conventional_gas",
    "crude",
    "crude_production",
    "crude_production_avg",
    "diesel",
    "ethanol_plant_production",
    "ethanol_plant_production_avg",
    "exports",
    "exports_avg",
    "heating_oil",
    "imports",
    "imports_avg",
    "imports_by_country",
    "imports_by_country_avg",
    "inputs_and_utilization",
    "inputs_and_utilization_avg",
    "jet_fuel",
    "monthly",
    "net_imports_inc_spr_avg",
    "net_imports_incl_spr",
    "net_production",
    "net_production_avg",
    "net_production_by_product",
    "net_production_by_production_avg",
    "product_by_region",
    "product_by_region_avg",
    "product_supplied",
    "product_supplied_avg",
    "propane",
    "rbob",
    "refiner_blender_net_production",
    "refiner_blender_net_production_avg",
    "stocks",
    "supply",
    "supply_avg",
    "ulta_low_sulfur_distillate_reclassification",
    "ulta_low_sulfur_distillate_reclassification_avg",
    "weekly",
]
WpsrTableChoices = [
    "all",
    "conventional_gas",
    "crude",
    "crude_production",
    "crude_production_avg",
    "diesel",
    "ethanol_plant_production",
    "ethanol_plant_production_avg",
    "exports",
    "exports_avg",
    "heating_oil",
    "imports",
    "imports_avg",
    "imports_by_country",
    "imports_by_country_avg",
    "inputs_and_utilization",
    "inputs_and_utilization_avg",
    "jet_fuel",
    "monthly",
    "net_imports_inc_spr_avg",
    "net_imports_incl_spr",
    "net_production",
    "net_production_avg",
    "net_production_by_product",
    "net_production_by_production_avg",
    "product_by_region",
    "product_by_region_avg",
    "product_supplied",
    "product_supplied_avg",
    "propane",
    "rbob",
    "refiner_blender_net_production",
    "refiner_blender_net_production_avg",
    "stocks",
    "supply",
    "supply_avg",
    "ulta_low_sulfur_distillate_reclassification",
    "ulta_low_sulfur_distillate_reclassification_avg",
    "weekly",
]
WpsrFileMap = {
    "balance_sheet": "https://ir.eia.gov/wpsr/psw01.xls",
    "inputs_and_production": "https://ir.eia.gov/wpsr/psw02.xls",
    "refiner_blender_net_production": "https://ir.eia.gov/wpsr/psw03.xls",
    "crude_petroleum_stocks": "https://ir.eia.gov/wpsr/psw04.xls",
    "gasoline_fuel_stocks": "https://ir.eia.gov/wpsr/psw05.xls",
    "total_gasoline_by_sub_padd": "https://ir.eia.gov/wpsr/psw05a.xls",
    "distillate_fuel_oil_stocks": "https://ir.eia.gov/wpsr/psw06.xls",
    "imports": "https://ir.eia.gov/wpsr/psw07.xls",
    "imports_by_country": "https://ir.eia.gov/wpsr/psw08.xls",
    "weekly_estimates": "https://ir.eia.gov/wpsr/psw09.xls",
    "spot_prices_crude_gas_heating": "https://ir.eia.gov/wpsr/psw11.xls",
    "spot_prices_diesel_jet_fuel_propane": "https://ir.eia.gov/wpsr/psw12.xls",
    "retail_prices": "https://ir.eia.gov/wpsr/psw14.xls",
}
WpsrTableMap = {
    "balance_sheet": {
        "stocks": "Data 1",
        "supply": "Data 2",
        "supply_avg": "Data 3",
    },
    "inputs_and_production": {
        "product_by_region": "Data 1",
        "product_by_region_avg": "Data 2",
    },
    "refiner_blender_net_production": {
        "net_production": "Data 1",
        "net_production_avg": "Data 2",
    },
    "crude_petroleum_stocks": {
        "stocks": "Data 1",
    },
    "gasoline_fuel_stocks": {
        "stocks": "Data 1",
    },
    "total_gasoline_by_sub_padd": {
        "stocks": "Data 1",
    },
    "distillate_fuel_oil_stocks": {
        "stocks": "Data 1",
    },
    "imports": {
        "imports": "Data 1",
        "imports_avg": "Data 2",
    },
    "imports_by_country": {
        "imports_by_country": "Data 1",
        "imports_by_country_avg": "Data 2",
    },
    "weekly_estimates": {
        "crude_production": "Data 1",
        "inputs_and_utilization": "Data 2",
        "refiner_blender_net_production": "Data 3",
        "net_production_by_product": "Data 4",
        "ethanol_plant_production": "Data 5",
        "stocks": "Data 6",
        "imports": "Data 7",
        "exports": "Data 8",
        "net_imports_incl_spr": "Data 9",
        "product_supplied": "Data 10",
        "ulta_low_sulfur_distillate_reclassification": "Data 11",
        "crude_production_avg": "Data 12",
        "inputs_utilization_avg": "Data 13",
        "refiner_blender_net_production_avg": "Data 14",
        "net_production_by_production_avg": "Data 15",
        "ethanol_plant_production_avg": "Data 16",
        "imports_avg": "Data 17",
        "exports_avg": "Data 18",
        "net_imports_inc_spr_avg": "Data 19",
        "product_supplied_avg": "Data 20",
        "ulta_low_sulfur_distillate_reclassification_avg": "Data 21",
    },
    "spot_prices_crude_gas_heating": {
        "crude": "Data 1",
        "conventional_gas": "Data 2",
        "rbob": "Data 3",
        "heating_oil": "Data 4",
    },
    "spot_prices_diesel_jet_fuel_propane": {
        "diesel": "Data 1",
        "jet_fuel": "Data 2",
        "propane": "Data 3",
    },
    "retail_prices": {
        "weekly": "Data 1",
        "monthly": "Data 2",
    },
}
SteoTableMap = {
    "01": [
        "COPRPUS",
        "NGPRPUS",
        "CLPRPUS_TON",
        "PATCPUSX",
        "NGTCPUS",
        "CLTCPUS_TON",
        "ESTXPUS",
        "RETCBUS",
        "TETCFUEL",
        "WTIPUUS",
        "NGHHUUS",
        "CLEUDUS",
        "GDPQXUS",
        "GDPQXUS_PCT",
        "GDPDIUS",
        "GDPDIUS_PCT",
        "YD87OUS",
        "YD87OUS_PCT",
        "ZOMNIUS",
        "ZOMNIUS_PCT",
        "ZWHDPUS",
        "ZWCDPUS",
    ],
    "02": [
        "WTIPUUS",
        "BREPUUS",
        "RAIMUUS",
        "RACPUUS",
        "MGWHUUS_$",
        "DSWHUUS_$",
        "D2WHUUS_$",
        "JKTCUUS_$",
        "RFTCUUS_$",
        "PRMBUUS_$",
        "MGRARUS_$",
        "MGEIAUS_$",
        "DSRTUUS_$",
        "D2RCAUS_$",
        "PRRCAUS",
        "NGHHMCF",
        "NGHHUUS",
        "NGICUUS",
        "NGCCUUS",
        "NGRCUUS",
        "CLEUDUS",
        "NGEUDUS",
        "RFEUDUS",
        "DKEUDUS",
        "ESICUUS",
        "ESCMUUS",
        "ESRCUUS",
    ],
    "03a": [
        "papr_world",
        "copr_world",
        "world_nc",
        "papr_world",
        "papr_opec",
        "copr_opec",
        "opec_nc",
        "papr_nonopec",
        "copr_nonopec",
        "nonopec_nc",
        "patc_world",
        "patc_oecd",
        "patc_ca",
        "patc_oecd_europe",
        "patc_ja",
        "patc_us",
        "patc_ust",
        "patc_other_oecd",
        "patc_non_oecd",
        "patc_ch",
        "patc_fsu",
        "patc_nonoecd_europe",
        "patc_other_asia",
        "patc_other_nonoecd",
        "t3_stchange_world",
        "t3_stchange_us",
        "t3_stchange_ooecd",
        "t3_stchange_noecd",
        "pasc_oecd_t3",
        "pasc_us",
        "pasc_ooecd_t3",
    ],
    "03b": [
        "papr_nonopec",
        "t3b_papr_r01",
        "papr_CA",
        "papr_MX",
        "papr_US",
        "t3b_papr_r02",
        "papr_AR",
        "papr_BR",
        "papr_CO",
        "papr_GY",
        "t3b_papr_r03",
        "papr_NO",
        "papr_UK",
        "t3b_papr_r04",
        "papr_AJ",
        "papr_KZ",
        "papr_RS",
        "t3b_papr_r05",
        "papr_MU",
        "papr_QA",
        "t3b_papr_r06",
        "papr_AO",
        "papr_EG",
        "t3b_papr_r07",
        "papr_CH",
        "papr_IN",
        "papr_ID",
        "papr_MY",
        "padi_nonOPEC",
    ],
    "03c": [
        "papr_world",
        "papr_opecplus",
        "papr_us",
        "papr_nonopecplus_xus",
        "papr_opec",
        "papr_ag",
        "papr_cf",
        "papr_ek",
        "papr_gb",
        "papr_IR",
        "papr_iz",
        "papr_ku",
        "papr_ly",
        "papr_ni",
        "papr_sa",
        "papr_tc",
        "papr_ve",
        "papr_opecplus",
        "papr_opecplus_opec",
        "papr_opecplus_other",
        "papr_aj",
        "papr_ba",
        "papr_bx",
        "papr_kz",
        "papr_my",
        "papr_mx",
        "papr_mu",
        "papr_rs",
        "papr_od",
        "papr_su",
    ],
    "03d": [
        "copr_world",
        "copr_opecplus",
        "coprpus",
        "copr_nonopecplus_xus",
        "copr_opec",
        "copr_ag",
        "copr_cf",
        "copr_ek",
        "copr_gb",
        "copr_IR",
        "copr_iz",
        "copr_ku",
        "copr_ly",
        "copr_ni",
        "copr_sa",
        "copr_tc",
        "copr_ve",
        "copr_opecplus",
        "copr_opecplus_opec",
        "copr_opecplus_other",
        "copr_aj",
        "copr_ba",
        "copr_bx",
        "copr_kz",
        "copr_my",
        "copr_mx",
        "copr_mu",
        "copr_rs",
        "copr_od",
        "copr_su",
        "copc_opec",
        "copc_opec_r05",
        "copc_opec_rot",
        "cops_opec",
        "cops_opec_r05",
        "cops_opec_rot",
        "padi_OPEC",
    ],
    "03e": [
        "patc_world",
        "patc_oecd",
        "patc_non_oecd",
        "patc_world",
        "patc_r01",
        "patc_ca",
        "patc_mx",
        "patc_us",
        "patc_r02",
        "patc_br",
        "patc_r03",
        "patc_r04",
        "patc_rs",
        "patc_r05",
        "patc_r06",
        "patc_r07",
        "patc_ch",
        "patc_in",
        "patc_ja",
    ],
    "04a": [
        "COPRPUS",
        "PAPRPAK",
        "PAPRPGLF",
        "PAPR48NGOM",
        "COPRAP",
        "COPRBK",
        "COPREF",
        "COPRHA",
        "COPRPM",
        "COPRR48",
        "PASUPPLY",
        "CORIPUS",
        "COPRPUS",
        "COTRPUS",
        "CONIPUS",
        "COSQ_DRAW",
        "COSX_DRAW",
        "COUNPUS",
        "PAGLPUS",
        "NLPRPUS",
        "PARNPUS",
        "EOPRPUS",
        "PAFPPUS",
        "PATRPUS",
        "PANIPUS",
        "NLNIPUS",
        "UONIPUS",
        "OHNIPUS",
        "MBNIPUS",
        "MGNIPUS",
        "JFNIPUS",
        "DFNIPUS",
        "RFNIPUS",
        "PSNIPUS",
        "PROD_DRAW",
        "PATCPUSX",
        "NLTCPUS",
        "OHTCPUS",
        "MGTCPUSX",
        "EOTCPUS",
        "JFTCPUS",
        "DFTCPUS",
        "RFTCPUS",
        "PSTCPUS",
        "PAIMPORT",
        "PASXPUS",
        "COSXPUS",
        "NLPSPUS",
        "UOPSPUS",
        "OHPSPUS",
        "MGTSPUS",
        "MGPSPUS",
        "MBPSPUS",
        "JFPSPUS",
        "DFPSPUS",
        "RFPSPUS",
        "PSPSPUS",
        "COSQPUS",
    ],
    "04b": [
        "HGPRPUS",
        "NLPRPUS",
        "ETFPPUS",
        "PRFPPUS",
        "C4FPPUS",
        "PPFPPUS",
        "NLROPUS",
        "ETROPUS",
        "C3ROPUS",
        "P3ROPUS",
        "C4ROPUS",
        "PPPRPUS",
        "NLTCPUS",
        "ETTCPUS",
        "C3TCPUS",
        "P3TCPUS",
        "C4TCPUS",
        "NLNIPUS",
        "ETNIPUS",
        "PRNIPUS",
        "C4NIPUS",
        "PPNIPUS",
        "NLPSPUS",
        "ETPSPUS",
        "C3PSPUS",
        "P3PSPUS",
        "C4PSPUS",
        "PPPSPUS",
        "PARIPUS",
        "CORIPUS",
        "NLRIPUS",
        "OHRIPUS",
        "UORIPUS",
        "MBRIPUS",
        "PAGLPUS",
        "PAROPUS",
        "NLROPUS",
        "MGROPUS",
        "JFROPUS",
        "DFROPUS",
        "RFROPUS",
        "PSROPUS",
        "CODIPUS",
        "ORCAPUS",
        "ORUTCUS",
    ],
    "04c": [
        "MGWHUUS_$",
        "MGEIAUS_$",
        "MGRARUS_$",
        "MGRARP1_$",
        "MGRARP2_$",
        "MGRARP3_$",
        "MGRARP4_$",
        "MGRARP5_$",
        "MGTSPUS",
        "MGTSPP1",
        "MGTSPP2",
        "MGTSPP3",
        "MGTSPP4",
        "MGTSPP5",
    ],
    "04d": [
        "BFSUPPLY",
        "EOPRPUS",
        "BDPRPUS",
        "RDPRPUS",
        "OBPRPUS",
        "EONIPUS",
        "BDNIPUS",
        "RDNIPUS",
        "OBNIPUS",
        "BFPSPUS_DRAW",
        "DASUPPLY",
        "DFROPUS",
        "BDPRPUS",
        "RDPRPUS",
        "DFNIPUS",
        "BDNIPUS",
        "RDNIPUS",
        "DAPSPUS_DRAW",
        "BFTCPUS",
        "EOTCPUS",
        "BDTCPUS",
        "BDTCPUS_PS",
        "BDRIPUS",
        "RDTCPUS",
        "RDTCPUS_PS",
        "RDRIPUS",
        "OBTCPUS",
        "MGTCPUSX",
        "MGTCPUSX_P",
        "EOTCPUS",
        "DATCPUS",
        "DFTCPUS",
        "DFTCPUS_P",
        "BDRIPUS",
        "RDRIPUS",
        "BDTCPUS_PS",
        "RDTCPUS_PS",
        "BFPSPUS",
        "EOPSPUS",
        "BDPSPUS",
        "RDPSPUS",
        "OBPSPUS",
        "DAPSPUS",
        "DFPSPUS",
        "BDPSPUS",
        "RDPSPUS",
    ],
    "05a": [
        "NGMPPUS",
        "NGMPPAK",
        "NGMPPGLF",
        "NGMP48NGOM",
        "NGMPAP",
        "NGMPBK",
        "NGMPEF",
        "NGMPHA",
        "NGMPPM",
        "NGMPR48",
        "NGSUPP",
        "BALIT",
        "NGPSUPP",
        "NGPRPUS",
        "NGNWPUS",
        "NGSFPUS",
        "NGNIPUS",
        "NGIMPUS_LNG",
        "NGEXPUS_LNG",
        "NGIMPUS_PIPE",
        "NGEXPUS_PIPE",
        "NGTCPUS",
        "NGRCPUS",
        "NGCCPUS",
        "NGINX",
        "NGEPCON",
        "NGLPPUS",
        "NGACPUS",
        "NGVHPUS",
        "NGWGPUS",
        "NGWG_EAST",
        "NGWG_MW",
        "NGWG_SC",
        "NGWG_MTN",
        "NGWG_PAC",
        "NGWG_AK",
    ],
    "05b": [
        "NGHHMCF",
        "NGRCUUS",
        "NGRCU_NEC",
        "NGRCU_MAC",
        "NGRCU_ENC",
        "NGRCU_WNC",
        "NGRCU_SAC",
        "NGRCU_ESC",
        "NGRCU_WSC",
        "NGRCU_MTN",
        "NGRCU_PAC",
        "NGCCUUS",
        "NGCCU_NEC",
        "NGCCU_MAC",
        "NGCCU_ENC",
        "NGCCU_WNC",
        "NGCCU_SAC",
        "NGCCU_ESC",
        "NGCCU_WSC",
        "NGCCU_MTN",
        "NGCCU_PAC",
        "NGICUUS",
        "NGICU_NEC",
        "NGICU_MAC",
        "NGICU_ENC",
        "NGICU_WNC",
        "NGICU_SAC",
        "NGICU_ESC",
        "NGICU_WSC",
        "NGICU_MTN",
        "NGICU_PAC",
    ],
    "06": [
        "CLTSPUS_TON",
        "CLST_DRAW_TON",
        "CLWCPUS_TON",
        "CLNSPUS_TON",
        "CLPRPUS_TON",
        "CLPRPAR_TON",
        "CLPRPIR_TON",
        "CLPRPWR_TON",
        "CLNIPUS_TON",
        "CLIMPUS_TON",
        "CLEXPUS_TON",
        "CLEXPMC_TON",
        "CLEXPSC_TON",
        "CLSD_DRAW_TON",
        "CLTCPUS_TON",
        "CLKCPUS_TON",
        "CLEPCON_TON",
        "CLZCPUS_TON",
        "CLHCPUS_TON",
        "CLYCPUS_TON",
        "CLAJPUS_TON",
        "CLPS_TOT",
        "CLSDPUS",
        "CLSTPUS",
        "CLPS_EP",
        "CLSOPUS",
        "CLSKPUS",
        "CLSHPUS",
        "CLMRHUS",
        "RSPRPUS",
        "CLEUDUS",
    ],
    "07a": [
        "ELSUTWH",
        "TSEOTWH",
        "EPEOTWH",
        "INEOTWH",
        "CMEOTWH",
        "ELNITWH",
        "SODTP_US",
        "SODRP_US",
        "SODCP_US",
        "SODIP_US",
        "TDLOTWH",
        "ELCOTWH",
        "ELTCTWH",
        "ELRCP_US",
        "ELCCP_US",
        "ELICP_US",
        "ELACP_US",
        "ELDUTWH",
        "EXRCH_US",
        "CLPS_EP",
        "RFPS_EP",
        "DKPS_EP",
        "CLEUDUS",
        "NGEUDUS",
        "RFEUDUS",
        "DKEUDUS",
        "ESRCUUS",
        "ESCMUUS",
        "ESICUUS",
        "ELWHU_TX",
        "ELWHU_CA",
        "ELWHU_NE",
        "ELWHU_NY",
        "ELWHU_PJ",
        "ELWHU_MW",
        "ELWHU_SP",
        "ELWHU_SE",
        "ELWHU_FL",
        "ELWHU_NW",
        "ELWHU_SW",
    ],
    "07b": [
        "ELTCP_US",
        "ELTCP_NEC",
        "ELTCP_MAC",
        "ELTCP_ENC",
        "ELTCP_WNC",
        "ELTCP_SAC",
        "ELTCP_ESC",
        "ELTCP_WSC",
        "ELTCP_MTN",
        "ELTCP_PAC",
        "ELTCP_HAK",
        "ELRCP_US",
        "ELRCP_NEC",
        "ELRCP_MAC",
        "ELRCP_ENC",
        "ELRCP_WNC",
        "ELRCP_SAC",
        "ELRCP_ESC",
        "ELRCP_WSC",
        "ELRCP_MTN",
        "ELRCP_PAC",
        "ELRCP_HAK",
        "ELCCP_US",
        "ELCCP_NEC",
        "ELCCP_MAC",
        "ELCCP_ENC",
        "ELCCP_WNC",
        "ELCCP_SAC",
        "ELCCP_ESC",
        "ELCCP_WSC",
        "ELCCP_MTN",
        "ELCCP_PAC",
        "ELCCP_HAK",
        "ELICP_US",
        "ELICP_NEC",
        "ELICP_MAC",
        "ELICP_ENC",
        "ELICP_WNC",
        "ELICP_SAC",
        "ELICP_ESC",
        "ELICP_WSC",
        "ELICP_MTN",
        "ELICP_PAC",
        "ELICP_HAK",
    ],
    "07c": [
        "ESTCU_US",
        "ESTCU_NEC",
        "ESTCU_MAC",
        "ESTCU_ENC",
        "ESTCU_WNC",
        "ESTCU_SAC",
        "ESTCU_ESC",
        "ESTCU_WSC",
        "ESTCU_MTN",
        "ESTCU_PAC",
        "ESRCU_US",
        "ESRCU_NEC",
        "ESRCU_MAC",
        "ESRCU_ENC",
        "ESRCU_WNC",
        "ESRCU_SAC",
        "ESRCU_ESC",
        "ESRCU_WSC",
        "ESRCU_MTN",
        "ESRCU_PAC",
        "ESCMU_US",
        "ESCMU_NEC",
        "ESCMU_MAC",
        "ESCMU_ENC",
        "ESCMU_WNC",
        "ESCMU_SAC",
        "ESCMU_ESC",
        "ESCMU_WSC",
        "ESCMU_MTN",
        "ESCMU_PAC",
        "ESICU_US",
        "ESICU_NEC",
        "ESICU_MAC",
        "ESICU_ENC",
        "ESICU_WNC",
        "ESICU_SAC",
        "ESICU_ESC",
        "ESICU_WSC",
        "ESICU_MTN",
        "ESICU_PAC",
    ],
    "07d1": [
        "TOEPGEN_US",
        "NGEPGEN_US",
        "CLEPGEN_US",
        "NUEPGEN_US",
        "RTEPGEN_US",
        "HVEPGEN_US",
        "WNEPGEN_US",
        "SOEPGEN_US",
        "GEEPGEN_US",
        "OWEPGEN_US",
        "WWEPGEN_US",
        "HPEPGEN_US",
        "PAEPGEN_US",
        "OGEPGEN_US",
        "OBEPGEN_US",
        "TOEPGEN_NE",
        "NGEPGEN_NE",
        "CLEPGEN_NE",
        "NUEPGEN_NE",
        "HVEPGEN_NE",
        "RNEPGEN_NE",
        "XXEPGEN_NE",
        "ELLOAD_NE",
        "TOEPGEN_NY",
        "NGEPGEN_NY",
        "CLEPGEN_NY",
        "NUEPGEN_NY",
        "HVEPGEN_NY",
        "RNEPGEN_NY",
        "XXEPGEN_NY",
        "ELLOAD_NY",
        "TOEPGEN_PJ",
        "NGEPGEN_PJ",
        "CLEPGEN_PJ",
        "NUEPGEN_PJ",
        "HVEPGEN_PJ",
        "RNEPGEN_PJ",
        "XXEPGEN_PJ",
        "ELLOAD_PJ",
        "TOEPGEN_SE",
        "NGEPGEN_SE",
        "CLEPGEN_SE",
        "NUEPGEN_SE",
        "HVEPGEN_SE",
        "RNEPGEN_SE",
        "XXEPGEN_SE",
        "ELLOAD_SE",
        "TOEPGEN_FL",
        "NGEPGEN_FL",
        "CLEPGEN_FL",
        "NUEPGEN_FL",
        "HVEPGEN_FL",
        "RNEPGEN_FL",
        "XXEPGEN_FL",
        "ELLOAD_FL",
    ],
    "07d2": [
        "TOEPGEN_MW",
        "NGEPGEN_MW",
        "CLEPGEN_MW",
        "NUEPGEN_MW",
        "HVEPGEN_MW",
        "RNEPGEN_MW",
        "XXEPGEN_MW",
        "ELLOAD_MW",
        "TOEPGEN_SP",
        "NGEPGEN_SP",
        "CLEPGEN_SP",
        "NUEPGEN_SP",
        "HVEPGEN_SP",
        "RNEPGEN_SP",
        "XXEPGEN_SP",
        "ELLOAD_SP",
        "TOEPGEN_TX",
        "NGEPGEN_TX",
        "CLEPGEN_TX",
        "NUEPGEN_TX",
        "HVEPGEN_TX",
        "RNEPGEN_TX",
        "XXEPGEN_TX",
        "ELLOAD_TX",
        "TOEPGEN_NW",
        "NGEPGEN_NW",
        "CLEPGEN_NW",
        "NUEPGEN_NW",
        "HVEPGEN_NW",
        "RNEPGEN_NW",
        "XXEPGEN_NW",
        "ELLOAD_NW",
        "TOEPGEN_SW",
        "NGEPGEN_SW",
        "CLEPGEN_SW",
        "NUEPGEN_SW",
        "HVEPGEN_SW",
        "RNEPGEN_SW",
        "XXEPGEN_SW",
        "ELLOAD_SW",
        "TOEPGEN_CA",
        "NGEPGEN_CA",
        "CLEPGEN_CA",
        "NUEPGEN_CA",
        "HVEPGEN_CA",
        "RNEPGEN_CA",
        "XXEPGEN_CA",
        "ELLOAD_CA",
    ],
    "07e": [
        "NGEPCGW_US",
        "CLEPCGW_US",
        "PAEPCGW_US",
        "OGEPCGW_US",
        "WNEPCGW_US",
        "SPEPCGWX_US",
        "STEPCGW_US",
        "GEEPCGW_US",
        "OWEPCGW_US",
        "WWEPCGW_US",
        "HVEPCGW_US",
        "HPEPCGW_US",
        "NUEPCGW_US",
        "BAEPCGW_US",
        "OTEPCGW_US",
        "NGCHCGW_US",
        "CLCHCGW_US",
        "PACHCGW_US",
        "OGCHCGW_US",
        "WWCHCGW_US",
        "OWCHCGW_US",
        "SOCHCGW_US",
        "WNCHCGW_US",
        "GECHCGW_US",
        "HVCHCGW_US",
        "BACHCGW_US",
        "OTCHCGW_US",
        "SODTG_US",
        "SODRG_US",
        "SODCG_US",
        "SODIG_US",
    ],
    "08": [
        "RETCBUS",
        "BTTCBUS",
        "BFLCBUS",
        "EOTCBUS",
        "GETCBUS",
        "HVTCBUS",
        "SOTCBUS",
        "OWTCBUS",
        "WWTCBUS",
        "WNTCBUS",
        "REECBUS",
        "GEECBUS",
        "HVECBUS",
        "SOECBUS",
        "OWEPCONB",
        "WWEPCONB",
        "WNECBUS",
        "REICBUS",
        "BFLCBUS",
        "GEICBUS",
        "HVICBUS",
        "SOICBUS",
        "OWICBUS",
        "WWICBUS",
        "RECCBUS",
        "GECCBUS",
        "SOCCBUS",
        "OWCCBUS",
        "WWCCBUS",
        "RERCBUS",
        "GERCBUS",
        "SORCBUS",
        "WWRCBUS",
        "BFACBUS",
        "BTTCBUS",
        "EOACBUS",
    ],
    "09a": [
        "GDPQXUS",
        "CONSRUS",
        "I87RXUS",
        "KRDRXUS",
        "GOVXRUS",
        "TREXRUS",
        "TRIMRUS",
        "YD87OUS",
        "EMNFPUS",
        "XRUNR",
        "HSTCXUS",
        "ZOTOIUS",
        "ZOMNIUS",
        "ZO311IUS",
        "ZO322IUS",
        "ZO324IUS",
        "ZO325IUS",
        "ZO327IUS",
        "ZO331IUS",
        "QSIC_CL",
        "QSIC_DF",
        "QSIC_EL",
        "QSIC_NG",
        "CICPIUS",
        "WPCPIUS",
        "WP57IUS",
        "GDPDIUS",
        "MVVMPUS",
        "RSPRPUS",
        "TETCCO2",
        "PATCCO2",
        "NGTCCO2",
        "CXTCCO2",
    ],
    "09b": [
        "CGSP_NEC",
        "CGSP_MAC",
        "CGSP_ENC",
        "CGSP_WNC",
        "CGSP_SAC",
        "CGSP_ESC",
        "CGSP_WSC",
        "CGSP_MTN",
        "CGSP_PAC",
        "IPMFG_NEC",
        "IPMFG_MAC",
        "IPMFG_ENC",
        "IPMFG_WNC",
        "IPMFG_SAC",
        "IPMFG_ESC",
        "IPMFG_WSC",
        "IPMFG_MTN",
        "IPMFG_PAC",
        "CYRPIC_NEC",
        "CYRPIC_MAC",
        "CYRPIC_ENC",
        "CYRPIC_WNC",
        "CYRPIC_SAC",
        "CYRPIC_ESC",
        "CYRPIC_WSC",
        "CYRPIC_MTN",
        "CYRPIC_PAC",
        "QHALLC_NEC",
        "QHALLC_MAC",
        "QHALLC_ENC",
        "QHALLC_WNC",
        "QHALLC_SAC",
        "QHALLC_ESC",
        "QHALLC_WSC",
        "QHALLC_MTN",
        "QHALLC_PAC",
        "EE_NEC",
        "EE_MAC",
        "EE_ENC",
        "EE_WNC",
        "EE_SAC",
        "EE_ESC",
        "EE_WSC",
        "EE_MTN",
        "EE_PAC",
    ],
    "09c": [
        "ZWHDPUS",
        "ZWHD_NEC",
        "ZWHD_MAC",
        "ZWHD_ENC",
        "ZWHD_WNC",
        "ZWHD_SAC",
        "ZWHD_ESC",
        "ZWHD_WSC",
        "ZWHD_MTN",
        "ZWHD_PAC",
        "ZWHD_US_10YR",
        "ZWHD_NEC_10YR",
        "ZWHD_MAC_10YR",
        "ZWHD_ENC_10YR",
        "ZWHD_WNC_10YR",
        "ZWHD_SAC_10YR",
        "ZWHD_ESC_10YR",
        "ZWHD_WSC_10YR",
        "ZWHD_MTN_10YR",
        "ZWHD_PAC_10YR",
        "ZWCDPUS",
        "ZWCD_NEC",
        "ZWCD_MAC",
        "ZWCD_ENC",
        "ZWCD_WNC",
        "ZWCD_SAC",
        "ZWCD_ESC",
        "ZWCD_WSC",
        "ZWCD_MTN",
        "ZWCD_PAC",
        "ZWCD_US_10YR",
        "ZWCD_NEC_10YR",
        "ZWCD_MAC_10YR",
        "ZWCD_ENC_10YR",
        "ZWCD_WNC_10YR",
        "ZWCD_SAC_10YR",
        "ZWCD_ESC_10YR",
        "ZWCD_WSC_10YR",
        "ZWCD_MTN_10YR",
        "ZWCD_PAC_10YR",
    ],
    "10a": [
        "RIGSAP",
        "RIGSBK",
        "RIGSEF",
        "RIGSHA",
        "RIGSPM",
        "RIGSR48",
        "NWDAP",
        "NWDBK",
        "NWDEF",
        "NWDHA",
        "NWDPM",
        "NWDR48",
        "NWRAP",
        "NWRBK",
        "NWREF",
        "NWRHA",
        "NWRPM",
        "NWRR48",
        "NWCAP",
        "NWCBK",
        "NWCEF",
        "NWCHA",
        "NWCPM",
        "NWCR48",
        "DUCSAP",
        "DUCSBK",
        "DUCSEF",
        "DUCSHA",
        "DUCSPM",
        "DUCSR48",
        "CONWAP",
        "CONWBK",
        "CONWEF",
        "CONWHA",
        "CONWPM",
        "CONWR48",
        "CONWRAP",
        "CONWRBK",
        "CONWREF",
        "CONWRHA",
        "CONWRPM",
        "CONWRR48",
        "COEOPAP",
        "COEOPBK",
        "COEOPEF",
        "COEOPHA",
        "COEOPPM",
        "COEOPR48",
        "NGNWAP",
        "NGNWBK",
        "NGNWEF",
        "NGNWHA",
        "NGNWPM",
        "NGNWR48",
        "NGNWRAP",
        "NGNWRBK",
        "NGNWREF",
        "NGNWRHA",
        "NGNWRPM",
        "NGNWRR48",
        "NGEOPAP",
        "NGEOPBK",
        "NGEOPEF",
        "NGEOPHA",
        "NGEOPPM",
        "NGEOPR48",
    ],
    "10b": [
        "TOPRL48",
        "TOPRAC",
        "TOPRBK",
        "TOPREF",
        "TOPRMP",
        "TOPRNI",
        "TOPRPM",
        "TOPRWF",
        "TOPRR48",
        "SNGPRL48",
        "SNGPRBK",
        "SNGPRBN",
        "SNGPREF",
        "SNGPRFY",
        "SNGPRHA",
        "SNGPRMC",
        "SNGPRMP",
        "SNGPRNI",
        "SNGPRPM",
        "SNGPRUA",
        "SNGPRWF",
        "SNGPRR48",
    ],
}
SteoTableNames = {
    "01": "US Energy Markets Summary",
    "02": "Nominal Energy Prices",
    "03a": "World Petroleum and Other Liquid Fuels Production, Consumption, and Inventories",
    "03b": "Non-OPEC Petroleum and Other Liquid Fuels Production",
    "03c": "World Petroleum and Other Liquid Fuels Production",
    "03d": "World Crude Oil Production",
    "03e": "World Petroleum and Other Liquid Fuels Consumption",
    "04a": "US Petroleum and Other Liquid Fuels Supply, Consumption, and Inventories",
    "04b": "US Hydrocarbon Gas Liquids (HGL) and Petroleum Refinery Balances",
    "04c": "US Regional Motor Gasoline Prices and Inventories",
    "04d": "US Biofuel Supply, Consumption, and Inventories",
    "05a": "US Natural Gas Supply, Consumption, and Inventories",
    "05b": "US Regional Natural Gas Prices",
    "06": "US Coal Supply, Consumption, and Inventories",
    "07a": "US Electricity Industry Overview",
    "07b": "US Regional Electricity Retail Sales",
    "07c": "US Regional Electricity Prices",
    "07d1": "US Regional Electricity Generation, Electric Power Sector",
    "07d2": "US Regional Electricity Generation, Electric Power Sector, continued",
    "07e": "US Electricity Generating Capacity",
    "08": "US Renewable Energy Consumption",
    "09a": "US Macroeconomic Indicators and CO2 Emissions",
    "09b": "US Regional Macroeconomic Data",
    "09c": "US Regional Weather Data",
    "10a": "Drilling Productivity Metrics",
    "10b": "Crude Oil and Natural Gas Production from Shale and Tight Formations",
}
SteoTableType = Literal[
    "01",
    "02",
    "03a",
    "03b",
    "03c",
    "03d",
    "03e",
    "04a",
    "04b",
    "04c",
    "04d",
    "05a",
    "05b",
    "06",
    "07a",
    "07b",
    "07c",
    "07d1",
    "07d2",
    "07e",
    "08",
    "09a",
    "09b",
    "09c",
    "10a",
    "10b",
]
