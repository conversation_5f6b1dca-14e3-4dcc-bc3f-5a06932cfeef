#!/usr/bin/env python3
"""
Explore OpenBB Platform API structure
"""

import openbb

def main():
    print("🔍 Exploring OpenBB Platform API")
    print("=" * 50)
    
    try:
        print("\n📋 OpenBB Module Attributes:")
        print("-" * 30)
        
        # Get all attributes of the openbb module
        attributes = [attr for attr in dir(openbb) if not attr.startswith('_')]
        
        for attr in attributes:
            try:
                obj = getattr(openbb, attr)
                obj_type = type(obj).__name__
                print(f"  {attr} ({obj_type})")
                
                # If it's a module or has sub-attributes, explore a bit
                if hasattr(obj, '__dict__') and not callable(obj):
                    sub_attrs = [sub for sub in dir(obj) if not sub.startswith('_')][:5]  # First 5
                    if sub_attrs:
                        print(f"    └─ Sub-attributes: {', '.join(sub_attrs)}")
                        
            except Exception as e:
                print(f"  {attr} (Error: {e})")
        
        print(f"\n📖 OpenBB Help Information:")
        print("-" * 30)
        
        # Try to get help information
        try:
            help_info = help(openbb)
            print("✓ Help information available (use help(openbb) in interactive mode)")
        except Exception as e:
            print(f"⚠ Could not get help: {e}")
        
        print(f"\n🔧 OpenBB Version and Info:")
        print("-" * 30)
        
        # Try different ways to get version
        version_attrs = ['__version__', 'version', 'VERSION']
        for attr in version_attrs:
            if hasattr(openbb, attr):
                try:
                    version = getattr(openbb, attr)
                    print(f"✓ {attr}: {version}")
                except Exception as e:
                    print(f"⚠ Could not get {attr}: {e}")
        
        # Check if there's a specific way to initialize or access the platform
        print(f"\n🚀 Trying to Initialize OpenBB:")
        print("-" * 30)
        
        try:
            # Try common initialization patterns
            if hasattr(openbb, 'obb'):
                print("✓ Found 'obb' attribute")
                obb = openbb.obb
                print(f"  Type: {type(obb)}")
                
                # Explore obb attributes
                obb_attrs = [attr for attr in dir(obb) if not attr.startswith('_')][:10]
                print(f"  Attributes: {', '.join(obb_attrs)}")
                
        except Exception as e:
            print(f"⚠ Error exploring obb: {e}")
            
        print(f"\n✅ OpenBB exploration completed!")
        
    except Exception as e:
        print(f"❌ An error occurred during exploration: {e}")

if __name__ == "__main__":
    main()
