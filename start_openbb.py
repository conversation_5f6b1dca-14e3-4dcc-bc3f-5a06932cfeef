#!/usr/bin/env python3
"""
OpenBB Platform Startup Script
This script provides an interactive way to start using OpenBB Platform
"""

from openbb import obb
import sys

def print_banner():
    """Print OpenBB banner"""
    print("""
    ╔═══════════════════════════════════════════════════════════════╗
    ║                                                               ║
    ║                    🚀 OpenBB Platform                        ║
    ║                                                               ║
    ║              Open Source Investment Research                  ║
    ║                                                               ║
    ╚═══════════════════════════════════════════════════════════════╝
    """)

def show_available_modules():
    """Show available OpenBB modules"""
    print("\n📋 Available Modules:")
    print("-" * 50)
    
    modules = [attr for attr in dir(obb) if not attr.startswith('_')]
    for i, module in enumerate(modules, 1):
        print(f"  {i:2d}. {module}")
    
    return modules

def show_module_functions(module_name):
    """Show functions available in a specific module"""
    try:
        module = getattr(obb, module_name)
        functions = [attr for attr in dir(module) if not attr.startswith('_')]
        
        print(f"\n🔧 Functions in '{module_name}' module:")
        print("-" * 50)
        
        for i, func in enumerate(functions, 1):
            print(f"  {i:2d}. {func}")
            
        return functions
    except Exception as e:
        print(f"❌ Error accessing module '{module_name}': {e}")
        return []

def interactive_mode():
    """Run interactive mode"""
    print("\n🎯 Interactive Mode")
    print("-" * 50)
    print("Type 'help' for commands, 'quit' to exit")
    
    while True:
        try:
            command = input("\nOpenBB> ").strip().lower()
            
            if command in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
            elif command in ['help', 'h']:
                print("""
Available commands:
  modules     - Show available modules
  explore     - Explore a specific module
  example     - Run a simple example
  help        - Show this help
  quit        - Exit the program
                """)
            elif command == 'modules':
                show_available_modules()
            elif command == 'explore':
                modules = show_available_modules()
                try:
                    choice = input("\nEnter module name or number: ").strip()
                    if choice.isdigit():
                        idx = int(choice) - 1
                        if 0 <= idx < len(modules):
                            module_name = modules[idx]
                        else:
                            print("❌ Invalid module number")
                            continue
                    else:
                        module_name = choice
                    
                    if module_name in modules:
                        show_module_functions(module_name)
                    else:
                        print(f"❌ Module '{module_name}' not found")
                except ValueError:
                    print("❌ Invalid input")
            elif command == 'example':
                run_simple_example()
            else:
                print(f"❌ Unknown command: {command}")
                print("Type 'help' for available commands")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

def run_simple_example():
    """Run a simple example"""
    print("\n📊 Running Simple Example...")
    print("-" * 50)
    
    try:
        # Try to get some basic information
        print("1. Checking available providers...")
        
        # Show equity module capabilities
        print("2. Exploring equity module...")
        equity_functions = [attr for attr in dir(obb.equity) if not attr.startswith('_')]
        print(f"   Available equity functions: {len(equity_functions)}")
        print(f"   Examples: {', '.join(equity_functions[:5])}")
        
        # Show crypto module capabilities
        print("3. Exploring crypto module...")
        crypto_functions = [attr for attr in dir(obb.crypto) if not attr.startswith('_')]
        print(f"   Available crypto functions: {len(crypto_functions)}")
        print(f"   Examples: {', '.join(crypto_functions)}")
        
        print("\n✅ Example completed!")
        print("💡 To fetch real data, you may need to configure API keys for data providers.")
        
    except Exception as e:
        print(f"❌ Error running example: {e}")

def main():
    """Main function"""
    print_banner()
    
    print("🔍 Initializing OpenBB Platform...")
    
    try:
        # Test basic functionality
        modules = [attr for attr in dir(obb) if not attr.startswith('_')]
        print(f"✅ OpenBB Platform loaded successfully!")
        print(f"📦 Available modules: {len(modules)}")
        
        # Show options
        print("\n🎯 What would you like to do?")
        print("  1. Show available modules")
        print("  2. Run interactive mode")
        print("  3. Run simple example")
        print("  4. Exit")
        
        while True:
            try:
                choice = input("\nEnter your choice (1-4): ").strip()
                
                if choice == '1':
                    show_available_modules()
                elif choice == '2':
                    interactive_mode()
                    break
                elif choice == '3':
                    run_simple_example()
                elif choice == '4':
                    print("👋 Goodbye!")
                    break
                else:
                    print("❌ Invalid choice. Please enter 1-4.")
                    
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
                
    except Exception as e:
        print(f"❌ Error initializing OpenBB: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
