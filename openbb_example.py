#!/usr/bin/env python3
"""
OpenBB Platform Example Usage
This script demonstrates basic usage of the OpenBB Platform
"""

import openbb
import pandas as pd
from datetime import datetime, timedelta

def main():
    print("🚀 OpenBB Platform Example")
    print("=" * 50)
    
    try:
        # Example 1: Get stock price data
        print("\n📈 Example 1: Getting Stock Price Data")
        print("-" * 40)
        
        # Get Apple stock data for the last 30 days
        symbol = "AAPL"
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)
        
        print(f"Fetching {symbol} stock data from {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
        
        try:
            # Using OpenBB to get stock data
            stock_data = openbb.equity.price.historical(
                symbol=symbol,
                start_date=start_date.strftime('%Y-%m-%d'),
                end_date=end_date.strftime('%Y-%m-%d')
            )
            
            if stock_data is not None and not stock_data.empty:
                print(f"✓ Successfully retrieved {len(stock_data)} data points")
                print(f"Latest close price: ${stock_data['close'].iloc[-1]:.2f}")
                print(f"30-day high: ${stock_data['high'].max():.2f}")
                print(f"30-day low: ${stock_data['low'].min():.2f}")
            else:
                print("⚠ No data retrieved")
                
        except Exception as e:
            print(f"⚠ Could not fetch stock data: {e}")
            print("This might be due to API limitations or network issues")
        
        # Example 2: Get company information
        print(f"\n🏢 Example 2: Company Information for {symbol}")
        print("-" * 40)
        
        try:
            # Get company profile
            company_info = openbb.equity.profile(symbol=symbol)
            if company_info is not None:
                print(f"✓ Company information retrieved")
                # Display some basic info if available
                if hasattr(company_info, 'to_dict'):
                    info_dict = company_info.to_dict()
                    for key, value in list(info_dict.items())[:5]:  # Show first 5 items
                        print(f"  {key}: {value}")
            else:
                print("⚠ No company information available")
                
        except Exception as e:
            print(f"⚠ Could not fetch company info: {e}")
        
        # Example 3: Available providers
        print(f"\n🔌 Example 3: Available Data Providers")
        print("-" * 40)
        
        try:
            # List available providers
            providers = openbb.coverage.providers
            if providers:
                print("✓ Available data providers:")
                for provider in providers:
                    print(f"  - {provider}")
            else:
                print("⚠ No providers information available")
                
        except Exception as e:
            print(f"⚠ Could not fetch providers: {e}")
            
        print(f"\n✅ OpenBB Platform example completed!")
        print("\n💡 Tips:")
        print("  - You can explore more functions using: help(openbb)")
        print("  - Check available endpoints: openbb.coverage.commands")
        print("  - Visit https://docs.openbb.co for comprehensive documentation")
        
    except Exception as e:
        print(f"❌ An error occurred: {e}")

if __name__ == "__main__":
    main()
