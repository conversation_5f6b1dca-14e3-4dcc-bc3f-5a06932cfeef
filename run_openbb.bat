@echo off
echo ========================================
echo        OpenBB Platform Launcher
echo ========================================
echo.

REM 激活虚拟环境
echo Activating OpenBB virtual environment...
call openbb_env\Scripts\activate.bat

echo.
echo OpenBB Platform is ready!
echo.
echo Available commands:
echo   python test_openbb.py           - Test installation
echo   python explore_openbb.py        - Explore API structure  
echo   python openbb_correct_example.py - Run examples
echo   python start_openbb.py          - Interactive mode
echo.

REM 保持命令行窗口打开
cmd /k
