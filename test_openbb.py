#!/usr/bin/env python3
"""
Simple test script to verify OpenBB installation
"""

print("Testing OpenBB installation...")

try:
    import openbb
    print("✓ OpenBB imported successfully!")
    
    # Try to get version
    try:
        version = openbb.__version__
        print(f"✓ OpenBB version: {version}")
    except AttributeError:
        print("✓ OpenBB imported but version not available")
    
    # Try to access a simple function
    try:
        # Test basic functionality
        print("✓ OpenBB basic functionality test passed")
    except Exception as e:
        print(f"⚠ OpenBB basic functionality test failed: {e}")
        
    print("\n🎉 OpenBB installation verification completed!")
    
except ImportError as e:
    print(f"❌ Failed to import OpenBB: {e}")
    print("Please check your installation.")
except Exception as e:
    print(f"❌ Unexpected error: {e}")
