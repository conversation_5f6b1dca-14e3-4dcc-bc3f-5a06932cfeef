{"ACPOPAT": "ACPOP", "ACPOPBE": "ACPOP", "ACPOPBG": "ACPOP", "ACPOPBR": "ACPOP", "ACPOPCA": "ACPOP", "ACPOPCH": "ACPOP", "ACPOPCY": "ACPOP", "ACPOPCZ": "ACPOP", "ACPOPDE": "ACPOP", "ACPOPDK": "ACPOP", "ACPOPEE": "ACPOP", "ACPOPES": "ACPOP", "ACPOPFI": "ACPOP", "ACPOPFR": "ACPOP", "ACPOPGR": "ACPOP", "ACPOPHR": "ACPOP", "ACPOPHU": "ACPOP", "ACPOPIE": "ACPOP", "ACPOPIS": "ACPOP", "ACPOPIT": "ACPOP", "ACPOPJP": "ACPOP", "ACPOPLT": "ACPOP", "ACPOPLU": "ACPOP", "ACPOPLV": "ACPOP", "ACPOPME": "ACPOP", "ACPOPMK": "ACPOP", "ACPOPMT": "ACPOP", "ACPOPMX": "ACPOP", "ACPOPNL": "ACPOP", "ACPOPNO": "ACPOP", "ACPOPPH": "ACPOP", "ACPOPPK": "ACPOP", "ACPOPPL": "ACPOP", "ACPOPPT": "ACPOP", "ACPOPQA": "ACPOP", "ACPOPRO": "ACPOP", "ACPOPRS": "ACPOP", "ACPOPSE": "ACPOP", "ACPOPSI": "ACPOP", "ACPOPSK": "ACPOP", "ACPOPTR": "ACPOP", "ACPOPTW": "ACPOP", "ACPOPUS": "ACPOP", "ACPOPVN": "ACPOP", "CAAR": "CA", "CAAT": "CA", "CAAU": "CA", "CAAZ": "CA", "CABD": "CA", "CABE": "CA", "CABG": "CA", "CABR": "CA", "CABT": "CA", "CABY": "CA", "CACA": "CA", "CACH": "CA", "CACL": "CA", "CACN": "CA", "CACO": "CA", "CACR": "CA", "CACY": "CA", "CACZ": "CA", "CADE": "CA", "CADK": "CA", "CAEE": "CA", "CAEG": "CA", "CAES": "CA", "CAEU": "CA", "CAFI": "CA", "CAFR": "CA", "CAGR": "CA", "CAHK": "CA", "CAHR": "CA", "CAHU": "CA", "CAID": "CA", "CAIE": "CA", "CAIL": "CA", "CAIN": "CA", "CAIT": "CA", "CAJO": "CA", "CAJP": "CA", "CAKH": "CA", "CAKR": "CA", "CAKZ": "CA", "CALA": "CA", "CALT": "CA", "CALU": "CA", "CALV": "CA", "CAMA": "CA", "CAMT": "CA", "CAMX": "CA", "CAMY": "CA", "CANL": "CA", "CANO": "CA", "CANP": "CA", "CANZ": "CA", "CAPA": "CA", "CAPH": "CA", "CAPK": "CA", "CAPL": "CA", "CAPT": "CA", "CARAT": "CAR", "CARBE": "CAR", "CARBG": "CAR", "CARCH": "CAR", "CARCZ": "CAR", "CARDE": "CAR", "CARDK": "CAR", "CAREE": "CAR", "CARES": "CAR", "CAREU": "CAR", "CARFI": "CAR", "CARFR": "CAR", "CARGR": "CAR", "CARHR": "CAR", "CARHU": "CAR", "CARIE": "CAR", "CARIS": "CAR", "CARIT": "CAR", "CARLT": "CAR", "CARLU": "CAR", "CARLV": "CAR", "CARNL": "CAR", "CARNO": "CAR", "CARO": "CA", "CARPL": "CAR", "CARPT": "CAR", "CARRO": "CAR", "CARSE": "CAR", "CARSI": "CAR", "CARSK": "CAR", "CARU": "CA", "CARUK": "CAR", "CARZA": "CAR", "CASA": "CA", "CASE": "CA", "CASG": "CA", "CASI": "CA", "CASK": "CA", "CASV": "CA", "CATH": "CA", "CATN": "CA", "CATR": "CA", "CATW": "CA", "CAUA": "CA", "CAUK": "CA", "CAUS": "CA", "CAUY": "CA", "CAVN": "CA", "CAZA": "CA", "CBALAL": "CBAL", "CBALAT": "CBAL", "CBALAZ": "CBAL", "CBALBE": "CBAL", "CBALBG": "CBAL", "CBALCH": "CBAL", "CBALCY": "CBAL", "CBALCZ": "CBAL", "CBALDE": "CBAL", "CBALDK": "CBAL", "CBALEA": "CBAL", "CBALEE": "CBAL", "CBALES": "CBAL", "CBALEU": "CBAL", "CBALFI": "CBAL", "CBALFR": "CBAL", "CBALGR": "CBAL", "CBALHR": "CBAL", "CBALHU": "CBAL", "CBALIE": "CBAL", "CBALIR": "CBAL", "CBALIT": "CBAL", "CBALLT": "CBAL", "CBALLU": "CBAL", "CBALLV": "CBAL", "CBALME": "CBAL", "CBALMT": "CBAL", "CBALNL": "CBAL", "CBALNO": "CBAL", "CBALPH": "CBAL", "CBALPL": "CBAL", "CBALPT": "CBAL", "CBALRO": "CBAL", "CBALRS": "CBAL", "CBALSE": "CBAL", "CBALSI": "CBAL", "CBALSK": "CBAL", "CBALTR": "CBAL", "CBALUK": "CBAL", "CBALUS": "CBAL", "CIAE": "CI", "CIAR": "CI", "CIAT": "CI", "CIAU": "CI", "CIAZ": "CI", "CIBE": "CI", "CIBG": "CI", "CIBR": "CI", "CIBW": "CI", "CIBY": "CI", "CICH": "CI", "CICN": "CI", "CICY": "CI", "CICZ": "CI", "CIDE": "CI", "CIDK": "CI", "CIEE": "CI", "CIES": "CI", "CIFI": "CI", "CIFR": "CI", "CIGR": "CI", "CIHK": "CI", "CIHR": "CI", "CIHU": "CI", "CIID": "CI", "CIIE": "CI", "CIIL": "CI", "CIIN": "CI", "CIIR": "CI", "CIIT": "CI", "CIKR": "CI", "CIKZ": "CI", "CILT": "CI", "CILU": "CI", "CILV": "CI", "CIMA": "CI", "CIME": "CI", "CIMN": "CI", "CIMT": "CI", "CIMX": "CI", "CIMY": "CI", "CING": "CI", "CINL": "CI", "CINO": "CI", "CINZ": "CI", "CIPH": "CI", "CIPK": "CI", "CIPL": "CI", "CIPT": "CI", "CIRO": "CI", "CIRS": "CI", "CIRU": "CI", "CISA": "CI", "CISE": "CI", "CISG": "CI", "CISI": "CI", "CISK": "CI", "CITH": "CI", "CITR": "CI", "CITW": "CI", "CIUA": "CI", "CIUK": "CI", "CIUS": "CI", "CIVN": "CI", "CIZA": "CI", "CKABE": "CKA", "CKABG": "CKA", "CKACZ": "CKA", "CKADE": "CKA", "CKADK": "CKA", "CKAEE": "CKA", "CKAES": "CKA", "CKAFI": "CKA", "CKAFR": "CKA", "CKAGR": "CKA", "CKAHR": "CKA", "CKAHU": "CKA", "CKAIT": "CKA", "CKALT": "CKA", "CKALU": "CKA", "CKALV": "CKA", "CKAMT": "CKA", "CKANL": "CKA", "CKAPL": "CKA", "CKAPT": "CKA", "CKARO": "CKA", "CKASE": "CKA", "CKASI": "CKA", "CKASK": "CKA", "CLAIMSUS": "CLAIMS", "CLIAT": "CLI", "CLIAU": "CLI", "CLIBE": "CLI", "CLIBR": "CLI", "CLICA": "CLI", "CLICH": "CLI", "CLICL": "CLI", "CLICN": "CLI", "CLICZ": "CLI", "CLIDE": "CLI", "CLIDK": "CLI", "CLIEE": "CLI", "CLIES": "CLI", "CLIFI": "CLI", "CLIFR": "CLI", "CLIGR": "CLI", "CLIHU": "CLI", "CLIID": "CLI", "CLIIE": "CLI", "CLIIL": "CLI", "CLIIN": "CLI", "CLIIS": "CLI", "CLIIT": "CLI", "CLIJP": "CLI", "CLIKR": "CLI", "CLILU": "CLI", "CLIMX": "CLI", "CLINL": "CLI", "CLINO": "CLI", "CLINZ": "CLI", "CLIPL": "CLI", "CLIPT": "CLI", "CLIRU": "CLI", "CLISE": "CLI", "CLISI": "CLI", "CLISK": "CLI", "CLITR": "CLI", "CLIUK": "CLI", "CLIUS": "CLI", "CLIZA": "CLI", "CONAE": "CON", "CONAL": "CON", "CONAT": "CON", "CONBD": "CON", "CONBE": "CON", "CONBG": "CON", "CONBY": "CON", "CONCA": "CON", "CONCH": "CON", "CONCL": "CON", "CONCN": "CON", "CONCY": "CON", "CONCZ": "CON", "CONDE": "CON", "CONDK": "CON", "CONEA": "CON", "CONEE": "CON", "CONES": "CON", "CONEU": "CON", "CONFAL": "CONF", "CONFAT": "CONF", "CONFAU": "CONF", "CONFBE": "CONF", "CONFBG": "CONF", "CONFBR": "CONF", "CONFCY": "CONF", "CONFCZ": "CONF", "CONFDE": "CONF", "CONFDK": "CONF", "CONFEE": "CONF", "CONFES": "CONF", "CONFEU": "CONF", "CONFFI": "CONF", "CONFFR": "CONF", "CONFGR": "CONF", "CONFHR": "CONF", "CONFHU": "CONF", "CONFI": "CON", "CONFID": "CONF", "CONFIE": "CONF", "CONFIL": "CONF", "CONFIT": "CONF", "CONFJP": "CONF", "CONFKR": "CONF", "CONFLT": "CONF", "CONFLU": "CONF", "CONFLV": "CONF", "CONFME": "CONF", "CONFMK": "CONF", "CONFMT": "CONF", "CONFMX": "CONF", "CONFNL": "CONF", "CONFPL": "CONF", "CONFPT": "CONF", "CONFR": "CON", "CONFRO": "CONF", "CONFRS": "CONF", "CONFSE": "CONF", "CONFSI": "CONF", "CONFSK": "CONF", "CONFTR": "CONF", "CONFUK": "CONF", "CONFUS": "CONF", "CONGR": "CON", "CONHR": "CON", "CONHU": "CON", "CONIE": "CON", "CONIT": "CON", "CONKR": "CON", "CONLT": "CON", "CONLU": "CON", "CONLV": "CON", "CONMK": "CON", "CONMT": "CON", "CONMX": "CON", "CONNL": "CON", "CONNO": "CON", "CONPL": "CON", "CONPT": "CON", "CONQA": "CON", "CONRO": "CON", "CONRS": "CON", "CONRU": "CON", "CONSE": "CON", "CONSI": "CON", "CONSK": "CON", "CONUA": "CON", "CONUK": "CON", "CONZA": "CON", "COREAR": "CORE", "COREAT": "CORE", "COREAU": "CORE", "COREBD": "CORE", "COREBE": "CORE", "COREBG": "CORE", "COREBR": "CORE", "CORECA": "CORE", "CORECH": "CORE", "CORECY": "CORE", "CORECZ": "CORE", "COREDE": "CORE", "COREDK": "CORE", "COREEA": "CORE", "COREEE": "CORE", "COREES": "CORE", "COREEU": "CORE", "COREFI": "CORE", "COREFR": "CORE", "COREGR": "CORE", "COREHR": "CORE", "COREHU": "CORE", "COREIE": "CORE", "COREIN": "CORE", "COREIS": "CORE", "COREIT": "CORE", "COREJP": "CORE", "COREKR": "CORE", "CORELT": "CORE", "CORELU": "CORE", "CORELV": "CORE", "COREMK": "CORE", "COREMT": "CORE", "COREMX": "CORE", "CORENL": "CORE", "CORENO": "CORE", "CORENP": "CORE", "COREPL": "CORE", "COREPT": "CORE", "CORERO": "CORE", "CORERS": "CORE", "CORERU": "CORE", "CORESE": "CORE", "CORESG": "CORE", "CORESI": "CORE", "CORESK": "CORE", "CORETH": "CORE", "CORETR": "CORE", "CORETW": "CORE", "COREUK": "CORE", "COREUS": "CORE", "CPAT": "CP", "CPBE": "CP", "CPBG": "CP", "CPCZ": "CP", "CPDE": "CP", "CPDK": "CP", "CPES": "CP", "CPEU": "CP", "CPFI": "CP", "CPFR": "CP", "CPHR": "CP", "CPHU": "CP", "CPIAE": "CPI", "CPIAF": "CPI", "CPIAG": "CPI", "CPIAL": "CPI", "CPIAM": "CPI", "CPIAR": "CPI", "CPIAT": "CPI", "CPIAU": "CPI", "CPIAW": "CPI", "CPIAZ": "CPI", "CPIBA": "CPI", "CPIBB": "CPI", "CPIBD": "CPI", "CPIBE": "CPI", "CPIBF": "CPI", "CPIBG": "CPI", "CPIBH": "CPI", "CPIBI": "CPI", "CPIBJ": "CPI", "CPIBN": "CPI", "CPIBO": "CPI", "CPIBR": "CPI", "CPIBS": "CPI", "CPIBT": "CPI", "CPIBW": "CPI", "CPIBY": "CPI", "CPIBZ": "CPI", "CPICA": "CPI", "CPICF": "CPI", "CPICG": "CPI", "CPICH": "CPI", "CPICI": "CPI", "CPICL": "CPI", "CPICM": "CPI", "CPICN": "CPI", "CPICO": "CPI", "CPICR": "CPI", "CPICV": "CPI", "CPICW": "CPI", "CPICY": "CPI", "CPICZ": "CPI", "CPIDE": "CPI", "CPIDJ": "CPI", "CPIDK": "CPI", "CPIDM": "CPI", "CPIDO": "CPI", "CPIEA": "CPI", "CPIEC": "CPI", "CPIEE": "CPI", "CPIEG": "CPI", "CPIES": "CPI", "CPIET": "CPI", "CPIEU": "CPI", "CPIFI": "CPI", "CPIFJ": "CPI", "CPIFR": "CPI", "CPIGA": "CPI", "CPIGD": "CPI", "CPIGE": "CPI", "CPIGN": "CPI", "CPIGQ": "CPI", "CPIGR": "CPI", "CPIGT": "CPI", "CPIGW": "CPI", "CPIGY": "CPI", "CPIHK": "CPI", "CPIHN": "CPI", "CPIHR": "CPI", "CPIHT": "CPI", "CPIHU": "CPI", "CPIID": "CPI", "CPIIE": "CPI", "CPIIL": "CPI", "CPIIN": "CPI", "CPIIQ": "CPI", "CPIIR": "CPI", "CPIIS": "CPI", "CPIIT": "CPI", "CPIJM": "CPI", "CPIJO": "CPI", "CPIJP": "CPI", "CPIKG": "CPI", "CPIKH": "CPI", "CPIKI": "CPI", "CPIKN": "CPI", "CPIKR": "CPI", "CPIKW": "CPI", "CPIKZ": "CPI", "CPILA": "CPI", "CPILB": "CPI", "CPILC": "CPI", "CPILK": "CPI", "CPILR": "CPI", "CPILS": "CPI", "CPILT": "CPI", "CPILU": "CPI", "CPILV": "CPI", "CPIMA": "CPI", "CPIMD": "CPI", "CPIME": "CPI", "CPIMG": "CPI", "CPIMK": "CPI", "CPIML": "CPI", "CPIMM": "CPI", "CPIMN": "CPI", "CPIMO": "CPI", "CPIMR": "CPI", "CPIMS": "CPI", "CPIMT": "CPI", "CPIMU": "CPI", "CPIMV": "CPI", "CPIMW": "CPI", "CPIMX": "CPI", "CPIMY": "CPI", "CPIMZ": "CPI", "CPINE": "CPI", "CPING": "CPI", "CPINI": "CPI", "CPINL": "CPI", "CPINO": "CPI", "CPINP": "CPI", "CPINZ": "CPI", "CPIOM": "CPI", "CPIPA": "CPI", "CPIPE": "CPI", "CPIPH": "CPI", "CPIPK": "CPI", "CPIPL": "CPI", "CPIPS": "CPI", "CPIPT": "CPI", "CPIQA": "CPI", "CPIRO": "CPI", "CPIRS": "CPI", "CPIRU": "CPI", "CPISA": "CPI", "CPISB": "CPI", "CPISC": "CPI", "CPISD": "CPI", "CPISE": "CPI", "CPISG": "CPI", "CPISI": "CPI", "CPISK": "CPI", "CPISR": "CPI", "CPISS": "CPI", "CPIST": "CPI", "CPISV": "CPI", "CPISZ": "CPI", "CPIT": "CP", "CPITD": "CPI", "CPITG": "CPI", "CPITH": "CPI", "CPITJ": "CPI", "CPITL": "CPI", "CPITN": "CPI", "CPITO": "CPI", "CPITR": "CPI", "CPITT": "CPI", "CPITW": "CPI", "CPIUA": "CPI", "CPIUG": "CPI", "CPIUK": "CPI", "CPIUS": "CPI", "CPIUY": "CPI", "CPIUZ": "CPI", "CPIVC": "CPI", "CPIVE": "CPI", "CPIVN": "CPI", "CPIWS": "CPI", "CPIXK": "CPI", "CPIZA": "CPI", "CPIZM": "CPI", "CPIZW": "CPI", "CPLU": "CP", "CPMK": "CP", "CPMX": "CP", "CPNL": "CP", "CPNO": "CP", "CPPL": "CP", "CPPT": "CP", "CPRO": "CP", "CPSE": "CP", "CPSG": "CP", "CPSI": "CP", "CPSK": "CP", "CPUK": "CP", "CREDAR": "CRED", "CREDAT": "CRED", "CREDAU": "CRED", "CREDBE": "CRED", "CREDBR": "CRED", "CREDCA": "CRED", "CREDCH": "CRED", "CREDCL": "CRED", "CREDCN": "CRED", "CREDCO": "CRED", "CREDCZ": "CRED", "CREDDE": "CRED", "CREDDK": "CRED", "CREDEA": "CREDEA", "CREDES": "CRED", "CREDFI": "CRED", "CREDFR": "CRED", "CREDGR": "CRED", "CREDHK": "CRED", "CREDHU": "CRED", "CREDID": "CRED", "CREDIE": "CRED", "CREDIL": "CRED", "CREDIN": "CRED", "CREDIT": "CRED", "CREDJP": "CRED", "CREDKR": "CRED", "CREDLU": "CRED", "CREDMO": "CRED", "CREDMX": "CRED", "CREDMY": "CRED", "CREDNL": "CRED", "CREDNO": "CRED", "CREDNZ": "CRED", "CREDPL": "CRED", "CREDPT": "CRED", "CREDRU": "CRED", "CREDSA": "CRED", "CREDSE": "CRED", "CREDSG": "CRED", "CREDTH": "CRED", "CREDTN": "CRED", "CREDTR": "CRED", "CREDUK": "CRED", "CREDUS": "CRED", "CREDZA": "CRED", "DWPEBE": "DWPE", "DWPECY": "DWPE", "DWPEDE": "DWPE", "DWPEES": "DWPE", "DWPEEU": "DWPE", "DWPEFI": "DWPE", "DWPEFR": "DWPE", "DWPEGR": "DWPE", "DWPEHU": "DWPE", "DWPEMK": "DWPE", "DWPENL": "DWPE", "DWPENO": "DWPE", "DWPEPT": "DWPE", "DWPERO": "DWPE", "DWPESE": "DWPE", "DWPESI": "DWPE", "ELEAT": "ELE", "ELEBE": "ELE", "ELEBG": "ELE", "ELECH": "ELE", "ELECN": "ELE", "ELECY": "ELE", "ELECZ": "ELE", "ELEDE": "ELE", "ELEDK": "ELE", "ELEEE": "ELE", "ELEES": "ELE", "ELEEU": "ELE", "ELEFI": "ELE", "ELEFR": "ELE", "ELEGR": "ELE", "ELEHR": "ELE", "ELEHU": "ELE", "ELEIE": "ELE", "ELEIT": "ELE", "ELELT": "ELE", "ELELU": "ELE", "ELELV": "ELE", "ELEMK": "ELE", "ELEMT": "ELE", "ELENL": "ELE", "ELENO": "ELE", "ELEPL": "ELE", "ELEPT": "ELE", "ELERO": "ELE", "ELESE": "ELE", "ELESI": "ELE", "ELESK": "ELE", "ELETR": "ELE", "ELEUK": "ELE", "ELEUS": "ELE", "ELEZA": "ELE", "EMPAR": "EMP", "EMPAT": "EMP", "EMPAU": "EMP", "EMPAZ": "EMP", "EMPBD": "EMP", "EMPBE": "EMP", "EMPBG": "EMP", "EMPBR": "EMP", "EMPBY": "EMP", "EMPCA": "EMP", "EMPCH": "EMP", "EMPCL": "EMP", "EMPCN": "EMP", "EMPCO": "EMP", "EMPCR": "EMP", "EMPCY": "EMP", "EMPCZ": "EMP", "EMPDE": "EMP", "EMPDK": "EMP", "EMPDO": "EMP", "EMPEE": "EMP", "EMPES": "EMP", "EMPFI": "EMP", "EMPFR": "EMP", "EMPGR": "EMP", "EMPHR": "EMP", "EMPHU": "EMP", "EMPID": "EMP", "EMPIE": "EMP", "EMPIS": "EMP", "EMPIT": "EMP", "EMPJO": "EMP", "EMPJP": "EMP", "EMPKR": "EMP", "EMPKZ": "EMP", "EMPLK": "EMP", "EMPLT": "EMP", "EMPLU": "EMP", "EMPLV": "EMP", "EMPME": "EMP", "EMPMK": "EMP", "EMPMO": "EMP", "EMPMT": "EMP", "EMPMX": "EMP", "EMPMY": "EMP", "EMPNL": "EMP", "EMPNO": "EMP", "EMPNZ": "EMP", "EMPPA": "EMP", "EMPPE": "EMP", "EMPPH": "EMP", "EMPPK": "EMP", "EMPPL": "EMP", "EMPPT": "EMP", "EMPQA": "EMP", "EMPRO": "EMP", "EMPRS": "EMP", "EMPRU": "EMP", "EMPSE": "EMP", "EMPSG": "EMP", "EMPSI": "EMP", "EMPSK": "EMP", "EMPTH": "EMP", "EMPTN": "EMP", "EMPTR": "EMP", "EMPTW": "EMP", "EMPUA": "EMP", "EMPUS": "EMP", "EMPUY": "EMP", "EMPVN": "EMP", "EMPZA": "EMP", "EMRATIOAT": "EMRATIO", "EMRATIOBE": "EMRATIO", "EMRATIOBG": "EMRATIO", "EMRATIOCH": "EMRATIO", "EMRATIOCY": "EMRATIO", "EMRATIOCZ": "EMRATIO", "EMRATIODE": "EMRATIO", "EMRATIODK": "EMRATIO", "EMRATIOEE": "EMRATIO", "EMRATIOES": "EMRATIO", "EMRATIOFI": "EMRATIO", "EMRATIOFR": "EMRATIO", "EMRATIOGR": "EMRATIO", "EMRATIOHR": "EMRATIO", "EMRATIOHU": "EMRATIO", "EMRATIOIE": "EMRATIO", "EMRATIOIS": "EMRATIO", "EMRATIOIT": "EMRATIO", "EMRATIOJP": "EMRATIO", "EMRATIOLT": "EMRATIO", "EMRATIOLU": "EMRATIO", "EMRATIOLV": "EMRATIO", "EMRATIOME": "EMRATIO", "EMRATIOMK": "EMRATIO", "EMRATIOMT": "EMRATIO", "EMRATIONL": "EMRATIO", "EMRATIONO": "EMRATIO", "EMRATIOPL": "EMRATIO", "EMRATIOPT": "EMRATIO", "EMRATIORO": "EMRATIO", "EMRATIORS": "EMRATIO", "EMRATIOSE": "EMRATIO", "EMRATIOSI": "EMRATIO", "EMRATIOSK": "EMRATIO", "EMRATIOTR": "EMRATIO", "EMRATIOTW": "EMRATIO", "EMRATIOUS": "EMRATIO", "EQYCAPCN": "EQYCAP", "EXPAE": "EXP", "EXPAL": "EXP", "EXPAR": "EXP", "EXPAT": "EXP", "EXPAU": "EXP", "EXPAZ": "EXP", "EXPBD": "EXP", "EXPBE": "EXP", "EXPBG": "EXP", "EXPBR": "EXP", "EXPBW": "EXP", "EXPBY": "EXP", "EXPCA": "EXP", "EXPCH": "EXP", "EXPCL": "EXP", "EXPCM": "EXP", "EXPCN": "EXP", "EXPCO": "EXP", "EXPCY": "EXP", "EXPCZ": "EXP", "EXPDE": "EXP", "EXPDK": "EXP", "EXPEA": "EXP", "EXPEE": "EXP", "EXPES": "EXP", "EXPEU": "EXP", "EXPFI": "EXP", "EXPFR": "EXP", "EXPGR": "EXP", "EXPHK": "EXP", "EXPHR": "EXP", "EXPHU": "EXP", "EXPID": "EXP", "EXPIE": "EXP", "EXPIL": "EXP", "EXPIN": "EXP", "EXPIR": "EXP", "EXPIT": "EXP", "EXPJO": "EXP", "EXPJP": "EXP", "EXPKR": "EXP", "EXPKZ": "EXP", "EXPLT": "EXP", "EXPLU": "EXP", "EXPLV": "EXP", "EXPMA": "EXP", "EXPME": "EXP", "EXPMK": "EXP", "EXPMN": "EXP", "EXPMONAR": "EXPMON", "EXPMONAU": "EXPMON", "EXPMONBD": "EXPMON", "EXPMONBE": "EXPMON", "EXPMONBG": "EXPMON", "EXPMONBR": "EXPMON", "EXPMONCA": "EXPMON", "EXPMONCL": "EXPMON", "EXPMONCN": "EXPMON", "EXPMONCO": "EXPMON", "EXPMONCR": "EXPMON", "EXPMONCZ": "EXPMON", "EXPMONDE": "EXPMON", "EXPMONDK": "EXPMON", "EXPMONEE": "EXPMON", "EXPMONEG": "EXPMON", "EXPMONES": "EXPMON", "EXPMONEU": "EXPMON", "EXPMONFI": "EXPMON", "EXPMONFR": "EXPMON", "EXPMONGR": "EXPMON", "EXPMONHK": "EXPMON", "EXPMONHR": "EXPMON", "EXPMONHU": "EXPMON", "EXPMONID": "EXPMON", "EXPMONIN": "EXPMON", "EXPMONIT": "EXPMON", "EXPMONJP": "EXPMON", "EXPMONKH": "EXPMON", "EXPMONKR": "EXPMON", "EXPMONKZ": "EXPMON", "EXPMONLT": "EXPMON", "EXPMONLU": "EXPMON", "EXPMONLV": "EXPMON", "EXPMONMK": "EXPMON", "EXPMONMT": "EXPMON", "EXPMONMX": "EXPMON", "EXPMONMY": "EXPMON", "EXPMONNL": "EXPMON", "EXPMONNP": "EXPMON", "EXPMONPH": "EXPMON", "EXPMONPK": "EXPMON", "EXPMONPL": "EXPMON", "EXPMONPT": "EXPMON", "EXPMONQA": "EXPMON", "EXPMONRO": "EXPMON", "EXPMONRS": "EXPMON", "EXPMONRU": "EXPMON", "EXPMONSA": "EXPMON", "EXPMONSE": "EXPMON", "EXPMONSG": "EXPMON", "EXPMONSI": "EXPMON", "EXPMONSK": "EXPMON", "EXPMONTH": "EXPMON", "EXPMONTR": "EXPMON", "EXPMONTW": "EXPMON", "EXPMONUA": "EXPMON", "EXPMONUK": "EXPMON", "EXPMONUS": "EXPMON", "EXPMONUY": "EXPMON", "EXPMONVN": "EXPMON", "EXPMONZA": "EXPMON", "EXPMT": "EXP", "EXPMX": "EXP", "EXPMY": "EXP", "EXPNG": "EXP", "EXPNL": "EXP", "EXPNO": "EXP", "EXPNZ": "EXP", "EXPPE": "EXP", "EXPPH": "EXP", "EXPPK": "EXP", "EXPPL": "EXP", "EXPPT": "EXP", "EXPQA": "EXP", "EXPRO": "EXP", "EXPRS": "EXP", "EXPRU": "EXP", "EXPRW": "EXP", "EXPSA": "EXP", "EXPSE": "EXP", "EXPSG": "EXP", "EXPSI": "EXP", "EXPSK": "EXP", "EXPSV": "EXP", "EXPTH": "EXP", "EXPTR": "EXP", "EXPTW": "EXP", "EXPUA": "EXP", "EXPUK": "EXP", "EXPUS": "EXP", "EXPZA": "EXP", "GASDEMAL": "GASDEM", "GASDEMAT": "GASDEM", "GASDEMAU": "GASDEM", "GASDEMAZ": "GASDEM", "GASDEMBE": "GASDEM", "GASDEMBG": "GASDEM", "GASDEMBH": "GASDEM", "GASDEMBN": "GASDEM", "GASDEMBO": "GASDEM", "GASDEMBY": "GASDEM", "GASDEMCA": "GASDEM", "GASDEMCH": "GASDEM", "GASDEMCL": "GASDEM", "GASDEMCZ": "GASDEM", "GASDEMDE": "GASDEM", "GASDEMDK": "GASDEM", "GASDEMDZ": "GASDEM", "GASDEMEC": "GASDEM", "GASDEMEE": "GASDEM", "GASDEMEG": "GASDEM", "GASDEMES": "GASDEM", "GASDEMFI": "GASDEM", "GASDEMFR": "GASDEM", "GASDEMGE": "GASDEM", "GASDEMGQ": "GASDEM", "GASDEMGR": "GASDEM", "GASDEMHK": "GASDEM", "GASDEMHR": "GASDEM", "GASDEMHU": "GASDEM", "GASDEMID": "GASDEM", "GASDEMIE": "GASDEM", "GASDEMIN": "GASDEM", "GASDEMIT": "GASDEM", "GASDEMJP": "GASDEM", "GASDEMKR": "GASDEM", "GASDEMKW": "GASDEM", "GASDEMLT": "GASDEM", "GASDEMLU": "GASDEM", "GASDEMLV": "GASDEM", "GASDEMLY": "GASDEM", "GASDEMMA": "GASDEM", "GASDEMMD": "GASDEM", "GASDEMMK": "GASDEM", "GASDEMMT": "GASDEM", "GASDEMMX": "GASDEM", "GASDEMNG": "GASDEM", "GASDEMNL": "GASDEM", "GASDEMNO": "GASDEM", "GASDEMNZ": "GASDEM", "GASDEMPE": "GASDEM", "GASDEMPH": "GASDEM", "GASDEMPL": "GASDEM", "GASDEMPT": "GASDEM", "GASDEMQA": "GASDEM", "GASDEMRO": "GASDEM", "GASDEMSE": "GASDEM", "GASDEMSI": "GASDEM", "GASDEMSK": "GASDEM", "GASDEMTN": "GASDEM", "GASDEMTR": "GASDEM", "GASDEMTT": "GASDEM", "GASDEMTW": "GASDEM", "GASDEMUA": "GASDEM", "GASDEMUK": "GASDEM", "GASDEMUS": "GASDEM", "GASDEMVE": "GASDEM", "GASDEMZA": "GASDEM", "GASODEMAE": "GASODEM", "GASODEMAL": "GASODEM", "GASODEMAM": "GASODEM", "GASODEMAO": "GASODEM", "GASODEMAR": "GASODEM", "GASODEMAT": "GASODEM", "GASODEMAU": "GASODEM", "GASODEMAZ": "GASODEM", "GASODEMBB": "GASODEM", "GASODEMBD": "GASODEM", "GASODEMBE": "GASODEM", "GASODEMBG": "GASODEM", "GASODEMBH": "GASODEM", "GASODEMBM": "GASODEM", "GASODEMBN": "GASODEM", "GASODEMBO": "GASODEM", "GASODEMBR": "GASODEM", "GASODEMBY": "GASODEM", "GASODEMBZ": "GASODEM", "GASODEMCA": "GASODEM", "GASODEMCH": "GASODEM", "GASODEMCL": "GASODEM", "GASODEMCN": "GASODEM", "GASODEMCO": "GASODEM", "GASODEMCR": "GASODEM", "GASODEMCU": "GASODEM", "GASODEMCY": "GASODEM", "GASODEMCZ": "GASODEM", "GASODEMDE": "GASODEM", "GASODEMDK": "GASODEM", "GASODEMDO": "GASODEM", "GASODEMDZ": "GASODEM", "GASODEMEC": "GASODEM", "GASODEMEE": "GASODEM", "GASODEMEG": "GASODEM", "GASODEMES": "GASODEM", "GASODEMFI": "GASODEM", "GASODEMFR": "GASODEM", "GASODEMGA": "GASODEM", "GASODEMGD": "GASODEM", "GASODEMGE": "GASODEM", "GASODEMGM": "GASODEM", "GASODEMGQ": "GASODEM", "GASODEMGR": "GASODEM", "GASODEMGT": "GASODEM", "GASODEMGY": "GASODEM", "GASODEMHK": "GASODEM", "GASODEMHN": "GASODEM", "GASODEMHR": "GASODEM", "GASODEMHT": "GASODEM", "GASODEMHU": "GASODEM", "GASODEMID": "GASODEM", "GASODEMIE": "GASODEM", "GASODEMIN": "GASODEM", "GASODEMIQ": "GASODEM", "GASODEMIR": "GASODEM", "GASODEMIS": "GASODEM", "GASODEMIT": "GASODEM", "GASODEMJM": "GASODEM", "GASODEMJP": "GASODEM", "GASODEMKR": "GASODEM", "GASODEMKW": "GASODEM", "GASODEMKZ": "GASODEM", "GASODEMLT": "GASODEM", "GASODEMLU": "GASODEM", "GASODEMLV": "GASODEM", "GASODEMLY": "GASODEM", "GASODEMMA": "GASODEM", "GASODEMMD": "GASODEM", "GASODEMMK": "GASODEM", "GASODEMMM": "GASODEM", "GASODEMMT": "GASODEM", "GASODEMMU": "GASODEM", "GASODEMMX": "GASODEM", "GASODEMMY": "GASODEM", "GASODEMNE": "GASODEM", "GASODEMNG": "GASODEM", "GASODEMNI": "GASODEM", "GASODEMNL": "GASODEM", "GASODEMNO": "GASODEM", "GASODEMNP": "GASODEM", "GASODEMNZ": "GASODEM", "GASODEMOM": "GASODEM", "GASODEMPA": "GASODEM", "GASODEMPE": "GASODEM", "GASODEMPG": "GASODEM", "GASODEMPH": "GASODEM", "GASODEMPL": "GASODEM", "GASODEMPT": "GASODEM", "GASODEMPY": "GASODEM", "GASODEMQA": "GASODEM", "GASODEMRO": "GASODEM", "GASODEMRU": "GASODEM", "GASODEMSA": "GASODEM", "GASODEMSD": "GASODEM", "GASODEMSE": "GASODEM", "GASODEMSG": "GASODEM", "GASODEMSI": "GASODEM", "GASODEMSK": "GASODEM", "GASODEMSR": "GASODEM", "GASODEMSV": "GASODEM", "GASODEMSY": "GASODEM", "GASODEMSZ": "GASODEM", "GASODEMTH": "GASODEM", "GASODEMTJ": "GASODEM", "GASODEMTN": "GASODEM", "GASODEMTR": "GASODEM", "GASODEMTT": "GASODEM", "GASODEMTW": "GASODEM", "GASODEMUA": "GASODEM", "GASODEMUK": "GASODEM", "GASODEMUS": "GASODEM", "GASODEMUY": "GASODEM", "GASODEMVE": "GASODEM", "GASODEMVN": "GASODEM", "GASODEMYE": "GASODEM", "GASODEMZA": "GASODEM", "GASOPRODAE": "GASOPROD", "GASOPRODAL": "GASOPROD", "GASOPRODAM": "GASOPROD", "GASOPRODAO": "GASOPROD", "GASOPRODAR": "GASOPROD", "GASOPRODAT": "GASOPROD", "GASOPRODAU": "GASOPROD", "GASOPRODAZ": "GASOPROD", "GASOPRODBB": "GASOPROD", "GASOPRODBD": "GASOPROD", "GASOPRODBE": "GASOPROD", "GASOPRODBG": "GASOPROD", "GASOPRODBH": "GASOPROD", "GASOPRODBM": "GASOPROD", "GASOPRODBN": "GASOPROD", "GASOPRODBO": "GASOPROD", "GASOPRODBR": "GASOPROD", "GASOPRODBY": "GASOPROD", "GASOPRODBZ": "GASOPROD", "GASOPRODCA": "GASOPROD", "GASOPRODCH": "GASOPROD", "GASOPRODCL": "GASOPROD", "GASOPRODCN": "GASOPROD", "GASOPRODCO": "GASOPROD", "GASOPRODCR": "GASOPROD", "GASOPRODCU": "GASOPROD", "GASOPRODCY": "GASOPROD", "GASOPRODCZ": "GASOPROD", "GASOPRODDE": "GASOPROD", "GASOPRODDK": "GASOPROD", "GASOPRODDO": "GASOPROD", "GASOPRODDZ": "GASOPROD", "GASOPRODEC": "GASOPROD", "GASOPRODEE": "GASOPROD", "GASOPRODEG": "GASOPROD", "GASOPRODES": "GASOPROD", "GASOPRODFI": "GASOPROD", "GASOPRODFR": "GASOPROD", "GASOPRODGA": "GASOPROD", "GASOPRODGD": "GASOPROD", "GASOPRODGE": "GASOPROD", "GASOPRODGM": "GASOPROD", "GASOPRODGQ": "GASOPROD", "GASOPRODGR": "GASOPROD", "GASOPRODGT": "GASOPROD", "GASOPRODGY": "GASOPROD", "GASOPRODHK": "GASOPROD", "GASOPRODHN": "GASOPROD", "GASOPRODHR": "GASOPROD", "GASOPRODHT": "GASOPROD", "GASOPRODHU": "GASOPROD", "GASOPRODID": "GASOPROD", "GASOPRODIE": "GASOPROD", "GASOPRODIN": "GASOPROD", "GASOPRODIQ": "GASOPROD", "GASOPRODIR": "GASOPROD", "GASOPRODIS": "GASOPROD", "GASOPRODIT": "GASOPROD", "GASOPRODJM": "GASOPROD", "GASOPRODJP": "GASOPROD", "GASOPRODKR": "GASOPROD", "GASOPRODKW": "GASOPROD", "GASOPRODKZ": "GASOPROD", "GASOPRODLT": "GASOPROD", "GASOPRODLU": "GASOPROD", "GASOPRODLV": "GASOPROD", "GASOPRODLY": "GASOPROD", "GASOPRODMA": "GASOPROD", "GASOPRODMD": "GASOPROD", "GASOPRODMK": "GASOPROD", "GASOPRODMM": "GASOPROD", "GASOPRODMT": "GASOPROD", "GASOPRODMU": "GASOPROD", "GASOPRODMX": "GASOPROD", "GASOPRODMY": "GASOPROD", "GASOPRODNE": "GASOPROD", "GASOPRODNG": "GASOPROD", "GASOPRODNI": "GASOPROD", "GASOPRODNL": "GASOPROD", "GASOPRODNO": "GASOPROD", "GASOPRODNP": "GASOPROD", "GASOPRODNZ": "GASOPROD", "GASOPRODOM": "GASOPROD", "GASOPRODPA": "GASOPROD", "GASOPRODPE": "GASOPROD", "GASOPRODPG": "GASOPROD", "GASOPRODPH": "GASOPROD", "GASOPRODPL": "GASOPROD", "GASOPRODPT": "GASOPROD", "GASOPRODPY": "GASOPROD", "GASOPRODQA": "GASOPROD", "GASOPRODRO": "GASOPROD", "GASOPRODRU": "GASOPROD", "GASOPRODSA": "GASOPROD", "GASOPRODSD": "GASOPROD", "GASOPRODSE": "GASOPROD", "GASOPRODSG": "GASOPROD", "GASOPRODSI": "GASOPROD", "GASOPRODSK": "GASOPROD", "GASOPRODSR": "GASOPROD", "GASOPRODSV": "GASOPROD", "GASOPRODSY": "GASOPROD", "GASOPRODSZ": "GASOPROD", "GASOPRODTH": "GASOPROD", "GASOPRODTJ": "GASOPROD", "GASOPRODTN": "GASOPROD", "GASOPRODTR": "GASOPROD", "GASOPRODTT": "GASOPROD", "GASOPRODTW": "GASOPROD", "GASOPRODUA": "GASOPROD", "GASOPRODUK": "GASOPROD", "GASOPRODUS": "GASOPROD", "GASOPRODUY": "GASOPROD", "GASOPRODVE": "GASOPROD", "GASOPRODVN": "GASOPROD", "GASOPRODYE": "GASOPROD", "GASOPRODZA": "GASOPROD", "GASPRODAL": "GASPROD", "GASPRODAT": "GASPROD", "GASPRODAU": "GASPROD", "GASPRODAZ": "GASPROD", "GASPRODBE": "GASPROD", "GASPRODBG": "GASPROD", "GASPRODBH": "GASPROD", "GASPRODBN": "GASPROD", "GASPRODBO": "GASPROD", "GASPRODBY": "GASPROD", "GASPRODCA": "GASPROD", "GASPRODCH": "GASPROD", "GASPRODCL": "GASPROD", "GASPRODCN": "GASPROD", "GASPRODCZ": "GASPROD", "GASPRODDE": "GASPROD", "GASPRODDK": "GASPROD", "GASPRODDZ": "GASPROD", "GASPRODEC": "GASPROD", "GASPRODEE": "GASPROD", "GASPRODEG": "GASPROD", "GASPRODES": "GASPROD", "GASPRODFI": "GASPROD", "GASPRODFR": "GASPROD", "GASPRODGA": "GASPROD", "GASPRODGE": "GASPROD", "GASPRODGQ": "GASPROD", "GASPRODGR": "GASPROD", "GASPRODHK": "GASPROD", "GASPRODHR": "GASPROD", "GASPRODHU": "GASPROD", "GASPRODID": "GASPROD", "GASPRODIE": "GASPROD", "GASPRODIN": "GASPROD", "GASPRODIQ": "GASPROD", "GASPRODIT": "GASPROD", "GASPRODJP": "GASPROD", "GASPRODKR": "GASPROD", "GASPRODKW": "GASPROD", "GASPRODKZ": "GASPROD", "GASPRODLT": "GASPROD", "GASPRODLU": "GASPROD", "GASPRODLV": "GASPROD", "GASPRODLY": "GASPROD", "GASPRODMA": "GASPROD", "GASPRODMD": "GASPROD", "GASPRODMK": "GASPROD", "GASPRODMT": "GASPROD", "GASPRODMX": "GASPROD", "GASPRODMY": "GASPROD", "GASPRODNG": "GASPROD", "GASPRODNL": "GASPROD", "GASPRODNO": "GASPROD", "GASPRODNZ": "GASPROD", "GASPRODPE": "GASPROD", "GASPRODPG": "GASPROD", "GASPRODPH": "GASPROD", "GASPRODPL": "GASPROD", "GASPRODPT": "GASPROD", "GASPRODQA": "GASPROD", "GASPRODRO": "GASPROD", "GASPRODRU": "GASPROD", "GASPRODSE": "GASPROD", "GASPRODSG": "GASPROD", "GASPRODSI": "GASPROD", "GASPRODSK": "GASPROD", "GASPRODTH": "GASPROD", "GASPRODTN": "GASPROD", "GASPRODTR": "GASPROD", "GASPRODTT": "GASPROD", "GASPRODTW": "GASPROD", "GASPRODUA": "GASPROD", "GASPRODUK": "GASPROD", "GASPRODUS": "GASPROD", "GASPRODVE": "GASPROD", "GASPRODVN": "GASPROD", "GASPRODZA": "GASPROD", "GBALAR": "GBAL", "GBALAT": "GBAL", "GBALAU": "GBAL", "GBALAZ": "GBAL", "GBALBD": "GBAL", "GBALBE": "GBAL", "GBALBG": "GBAL", "GBALBR": "GBAL", "GBALCA": "GBAL", "GBALCH": "GBAL", "GBALCN": "GBAL", "GBALCO": "GBAL", "GBALCR": "GBAL", "GBALCY": "GBAL", "GBALCZ": "GBAL", "GBALDE": "GBAL", "GBALDK": "GBAL", "GBALEE": "GBAL", "GBALES": "GBAL", "GBALEU": "GBAL", "GBALFI": "GBAL", "GBALFR": "GBAL", "GBALGR": "GBAL", "GBALHK": "GBAL", "GBALHR": "GBAL", "GBALHU": "GBAL", "GBALID": "GBAL", "GBALIE": "GBAL", "GBALIL": "GBAL", "GBALIN": "GBAL", "GBALIS": "GBAL", "GBALIT": "GBAL", "GBALJP": "GBAL", "GBALKH": "GBAL", "GBALKR": "GBAL", "GBALKZ": "GBAL", "GBALLT": "GBAL", "GBALLU": "GBAL", "GBALLV": "GBAL", "GBALMO": "GBAL", "GBALMT": "GBAL", "GBALMX": "GBAL", "GBALMY": "GBAL", "GBALNL": "GBAL", "GBALNO": "GBAL", "GBALPE": "GBAL", "GBALPH": "GBAL", "GBALPL": "GBAL", "GBALPT": "GBAL", "GBALRO": "GBAL", "GBALRU": "GBAL", "GBALSA": "GBAL", "GBALSE": "GBAL", "GBALSG": "GBAL", "GBALSI": "GBAL", "GBALSK": "GBAL", "GBALTH": "GBAL", "GBALTR": "GBAL", "GBALTW": "GBAL", "GBALUA": "GBAL", "GBALUK": "GBAL", "GBALUS": "GBAL", "GBALZA": "GBAL", "GCFAT": "GCF", "GCFBE": "GCF", "GCFBG": "GCF", "GCFBR": "GCF", "GCFBY": "GCF", "GCFCH": "GCF", "GCFCN": "GCF", "GCFCY": "GCF", "GCFCZ": "GCF", "GCFDE": "GCF", "GCFDK": "GCF", "GCFEA": "GCF", "GCFEE": "GCF", "GCFES": "GCF", "GCFEU": "GCF", "GCFFI": "GCF", "GCFFR": "GCF", "GCFGR": "GCF", "GCFHR": "GCF", "GCFHU": "GCF", "GCFIE": "GCF", "GCFIT": "GCF", "GCFKR": "GCF", "GCFLT": "GCF", "GCFLU": "GCF", "GCFLV": "GCF", "GCFME": "GCF", "GCFMK": "GCF", "GCFMT": "GCF", "GCFNL": "GCF", "GCFNO": "GCF", "GCFNZ": "GCF", "GCFPE": "GCF", "GCFPL": "GCF", "GCFPT": "GCF", "GCFQA": "GCF", "GCFRO": "GCF", "GCFRS": "GCF", "GCFRU": "GCF", "GCFSE": "GCF", "GCFSI": "GCF", "GCFSK": "GCF", "GCFTH": "GCF", "GCFUA": "GCF", "GCFUK": "GCF", "GDEBTAR": "GDEBT", "GDEBTAT": "GDEBT", "GDEBTAU": "GDEBT", "GDEBTBE": "GDEBT", "GDEBTBG": "GDEBT", "GDEBTBR": "GDEBT", "GDEBTCA": "GDEBT", "GDEBTCL": "GDEBT", "GDEBTCN": "GDEBT", "GDEBTCO": "GDEBT", "GDEBTCR": "GDEBT", "GDEBTCY": "GDEBT", "GDEBTCZ": "GDEBT", "GDEBTDE": "GDEBT", "GDEBTDK": "GDEBT", "GDEBTEE": "GDEBT", "GDEBTES": "GDEBT", "GDEBTEU": "GDEBT", "GDEBTFI": "GDEBT", "GDEBTFR": "GDEBT", "GDEBTGR": "GDEBT", "GDEBTHK": "GDEBT", "GDEBTHR": "GDEBT", "GDEBTHU": "GDEBT", "GDEBTID": "GDEBT", "GDEBTIE": "GDEBT", "GDEBTIL": "GDEBT", "GDEBTIN": "GDEBT", "GDEBTIT": "GDEBT", "GDEBTJO": "GDEBT", "GDEBTJP": "GDEBT", "GDEBTKR": "GDEBT", "GDEBTKZ": "GDEBT", "GDEBTLT": "GDEBT", "GDEBTLU": "GDEBT", "GDEBTLV": "GDEBT", "GDEBTMT": "GDEBT", "GDEBTMX": "GDEBT", "GDEBTMY": "GDEBT", "GDEBTNAT": "GDEBTN", "GDEBTNBE": "GDEBTN", "GDEBTNBG": "GDEBTN", "GDEBTNCY": "GDEBTN", "GDEBTNCZ": "GDEBTN", "GDEBTNDE": "GDEBTN", "GDEBTNDK": "GDEBTN", "GDEBTNEE": "GDEBTN", "GDEBTNES": "GDEBTN", "GDEBTNEU": "GDEBTN", "GDEBTNFI": "GDEBTN", "GDEBTNFR": "GDEBTN", "GDEBTNGR": "GDEBTN", "GDEBTNHR": "GDEBTN", "GDEBTNHU": "GDEBTN", "GDEBTNIE": "GDEBTN", "GDEBTNIT": "GDEBTN", "GDEBTNL": "GDEBT", "GDEBTNLT": "GDEBTN", "GDEBTNLU": "GDEBTN", "GDEBTNLV": "GDEBTN", "GDEBTNMT": "GDEBTN", "GDEBTNNL": "GDEBTN", "GDEBTNNO": "GDEBTN", "GDEBTNO": "GDEBT", "GDEBTNPL": "GDEBTN", "GDEBTNPT": "GDEBTN", "GDEBTNRO": "GDEBTN", "GDEBTNSE": "GDEBTN", "GDEBTNSI": "GDEBTN", "GDEBTNSK": "GDEBTN", "GDEBTNUK": "GDEBTN", "GDEBTPH": "GDEBT", "GDEBTPK": "GDEBT", "GDEBTPL": "GDEBT", "GDEBTPT": "GDEBT", "GDEBTQA": "GDEBT", "GDEBTRO": "GDEBT", "GDEBTRU": "GDEBT", "GDEBTSA": "GDEBT", "GDEBTSE": "GDEBT", "GDEBTSG": "GDEBT", "GDEBTSI": "GDEBT", "GDEBTSK": "GDEBT", "GDEBTSV": "GDEBT", "GDEBTTH": "GDEBT", "GDEBTTR": "GDEBT", "GDEBTTW": "GDEBT", "GDEBTUK": "GDEBT", "GDEBTUS": "GDEBT", "GDEBTZA": "GDEBT", "GDPAE": "GDP", "GDPAL": "GDP", "GDPAR": "GDP", "GDPAT": "GDP", "GDPAU": "GDP", "GDPAZ": "GDP", "GDPBD": "GDP", "GDPBE": "GDP", "GDPBG": "GDP", "GDPBR": "GDP", "GDPBW": "GDP", "GDPBY": "GDP", "GDPCA": "GDP", "GDPCH": "GDP", "GDPCL": "GDP", "GDPCM": "GDP", "GDPCN": "GDP", "GDPCO": "GDP", "GDPCR": "GDP", "GDPCY": "GDP", "GDPCZ": "GDP", "GDPDE": "GDP", "GDPDEFLK": "GDPDEF", "GDPDK": "GDP", "GDPEA": "GDP", "GDPEE": "GDP", "GDPEG": "GDP", "GDPES": "GDP", "GDPEU": "GDP", "GDPFI": "GDP", "GDPFR": "GDP", "GDPGR": "GDP", "GDPHK": "GDP", "GDPHN": "GDP", "GDPHR": "GDP", "GDPHU": "GDP", "GDPID": "GDP", "GDPIE": "GDP", "GDPIL": "GDP", "GDPIN": "GDP", "GDPIR": "GDP", "GDPIT": "GDP", "GDPJO": "GDP", "GDPJP": "GDP", "GDPKH": "GDP", "GDPKR": "GDP", "GDPKZ": "GDP", "GDPLA": "GDP", "GDPLT": "GDP", "GDPLU": "GDP", "GDPLV": "GDP", "GDPMA": "GDP", "GDPME": "GDP", "GDPMK": "GDP", "GDPMO": "GDP", "GDPMT": "GDP", "GDPMX": "GDP", "GDPMY": "GDP", "GDPNG": "GDP", "GDPNL": "GDP", "GDPNO": "GDP", "GDPNZ": "GDP", "GDPPA": "GDP", "GDPPCAL": "GDPPC", "GDPPCAT": "GDPPC", "GDPPCAU": "GDPPC", "GDPPCBE": "GDPPC", "GDPPCBG": "GDPPC", "GDPPCBR": "GDPPC", "GDPPCCH": "GDPPC", "GDPPCCN": "GDPPC", "GDPPCCY": "GDPPC", "GDPPCCZ": "GDPPC", "GDPPCDE": "GDPPC", "GDPPCDK": "GDPPC", "GDPPCEA": "GDPPC", "GDPPCEE": "GDPPC", "GDPPCES": "GDPPC", "GDPPCEU": "GDPPC", "GDPPCFI": "GDPPC", "GDPPCFR": "GDPPC", "GDPPCGR": "GDPPC", "GDPPCHR": "GDPPC", "GDPPCHU": "GDPPC", "GDPPCIE": "GDPPC", "GDPPCIS": "GDPPC", "GDPPCIT": "GDPPC", "GDPPCKH": "GDPPC", "GDPPCKZ": "GDPPC", "GDPPCLI": "GDPPC", "GDPPCLT": "GDPPC", "GDPPCLU": "GDPPC", "GDPPCLV": "GDPPC", "GDPPCME": "GDPPC", "GDPPCMK": "GDPPC", "GDPPCMT": "GDPPC", "GDPPCNL": "GDPPC", "GDPPCNO": "GDPPC", "GDPPCNZ": "GDPPC", "GDPPCPL": "GDPPC", "GDPPCPT": "GDPPC", "GDPPCRO": "GDPPC", "GDPPCRS": "GDPPC", "GDPPCSE": "GDPPC", "GDPPCSG": "GDPPC", "GDPPCSI": "GDPPC", "GDPPCSK": "GDPPC", "GDPPCTR": "GDPPC", "GDPPCTW": "GDPPC", "GDPPCUS": "GDPPC", "GDPPE": "GDP", "GDPPH": "GDP", "GDPPK": "GDP", "GDPPL": "GDP", "GDPPT": "GDP", "GDPQA": "GDP", "GDPRO": "GDP", "GDPRS": "GDP", "GDPRU": "GDP", "GDPRW": "GDP", "GDPSA": "GDP", "GDPSE": "GDP", "GDPSG": "GDP", "GDPSI": "GDP", "GDPSK": "GDP", "GDPSV": "GDP", "GDPTH": "GDP", "GDPTN": "GDP", "GDPTR": "GDP", "GDPTW": "GDP", "GDPUA": "GDP", "GDPUK": "GDP", "GDPUS": "GDP", "GDPUZ": "GDP", "GDPVN": "GDP", "GDPZA": "GDP", "GFCFAE": "GFCF", "GFCFAL": "GFCF", "GFCFAR": "GFCF", "GFCFAT": "GFCF", "GFCFAU": "GFCF", "GFCFAZ": "GFCF", "GFCFBD": "GFCF", "GFCFBE": "GFCF", "GFCFBG": "GFCF", "GFCFBR": "GFCF", "GFCFBW": "GFCF", "GFCFBY": "GFCF", "GFCFCA": "GFCF", "GFCFCH": "GFCF", "GFCFCL": "GFCF", "GFCFCM": "GFCF", "GFCFCN": "GFCF", "GFCFCO": "GFCF", "GFCFCY": "GFCF", "GFCFCZ": "GFCF", "GFCFDE": "GFCF", "GFCFDK": "GFCF", "GFCFEA": "GFCF", "GFCFEE": "GFCF", "GFCFES": "GFCF", "GFCFEU": "GFCF", "GFCFFI": "GFCF", "GFCFFR": "GFCF", "GFCFGR": "GFCF", "GFCFHK": "GFCF", "GFCFHR": "GFCF", "GFCFHU": "GFCF", "GFCFID": "GFCF", "GFCFIE": "GFCF", "GFCFIL": "GFCF", "GFCFIN": "GFCF", "GFCFIR": "GFCF", "GFCFIT": "GFCF", "GFCFJP": "GFCF", "GFCFKR": "GFCF", "GFCFKZ": "GFCF", "GFCFLT": "GFCF", "GFCFLU": "GFCF", "GFCFLV": "GFCF", "GFCFMA": "GFCF", "GFCFME": "GFCF", "GFCFMN": "GFCF", "GFCFMT": "GFCF", "GFCFMX": "GFCF", "GFCFMY": "GFCF", "GFCFNG": "GFCF", "GFCFNL": "GFCF", "GFCFNO": "GFCF", "GFCFNZ": "GFCF", "GFCFPH": "GFCF", "GFCFPK": "GFCF", "GFCFPL": "GFCF", "GFCFPT": "GFCF", "GFCFRO": "GFCF", "GFCFRS": "GFCF", "GFCFRU": "GFCF", "GFCFSA": "GFCF", "GFCFSE": "GFCF", "GFCFSG": "GFCF", "GFCFSI": "GFCF", "GFCFSK": "GFCF", "GFCFTH": "GFCF", "GFCFTR": "GFCF", "GFCFTW": "GFCF", "GFCFUA": "GFCF", "GFCFUK": "GFCF", "GFCFUS": "GFCF", "GFCFUZ": "GFCF", "GFCFVN": "GFCF", "GFCFZA": "GFCF", "GREVAR": "GREV", "GREVAT": "GREV", "GREVAU": "GREV", "GREVAZ": "GREV", "GREVBD": "GREV", "GREVBE": "GREV", "GREVBG": "GREV", "GREVBR": "GREV", "GREVCA": "GREV", "GREVCH": "GREV", "GREVCN": "GREV", "GREVCO": "GREV", "GREVCR": "GREV", "GREVCY": "GREV", "GREVCZ": "GREV", "GREVDE": "GREV", "GREVDK": "GREV", "GREVEE": "GREV", "GREVES": "GREV", "GREVEU": "GREV", "GREVFI": "GREV", "GREVFR": "GREV", "GREVHK": "GREV", "GREVHR": "GREV", "GREVHU": "GREV", "GREVID": "GREV", "GREVIE": "GREV", "GREVIL": "GREV", "GREVIN": "GREV", "GREVJP": "GREV", "GREVKH": "GREV", "GREVKR": "GREV", "GREVKZ": "GREV", "GREVLT": "GREV", "GREVLU": "GREV", "GREVLV": "GREV", "GREVMA": "GREV", "GREVMO": "GREV", "GREVMT": "GREV", "GREVMX": "GREV", "GREVMY": "GREV", "GREVNL": "GREV", "GREVPE": "GREV", "GREVPH": "GREV", "GREVPL": "GREV", "GREVPT": "GREV", "GREVRO": "GREV", "GREVRU": "GREV", "GREVSA": "GREV", "GREVSE": "GREV", "GREVSG": "GREV", "GREVSI": "GREV", "GREVSK": "GREV", "GREVSV": "GREV", "GREVTH": "GREV", "GREVTR": "GREV", "GREVTW": "GREV", "GREVUA": "GREV", "GREVUK": "GREV", "GREVUS": "GREV", "GREVUY": "GREV", "GREVVN": "GREV", "GREVZA": "GREV", "GSPEAR": "GSPE", "GSPEAT": "GSPE", "GSPEAU": "GSPE", "GSPEAZ": "GSPE", "GSPEBD": "GSPE", "GSPEBE": "GSPE", "GSPEBG": "GSPE", "GSPEBR": "GSPE", "GSPECA": "GSPE", "GSPECH": "GSPE", "GSPECN": "GSPE", "GSPECO": "GSPE", "GSPECR": "GSPE", "GSPECY": "GSPE", "GSPECZ": "GSPE", "GSPEDE": "GSPE", "GSPEDK": "GSPE", "GSPEEE": "GSPE", "GSPEES": "GSPE", "GSPEEU": "GSPE", "GSPEFI": "GSPE", "GSPEFR": "GSPE", "GSPEHK": "GSPE", "GSPEHR": "GSPE", "GSPEHU": "GSPE", "GSPEID": "GSPE", "GSPEIE": "GSPE", "GSPEIL": "GSPE", "GSPEIN": "GSPE", "GSPEJP": "GSPE", "GSPEKH": "GSPE", "GSPEKR": "GSPE", "GSPEKZ": "GSPE", "GSPELT": "GSPE", "GSPELU": "GSPE", "GSPELV": "GSPE", "GSPEMO": "GSPE", "GSPEMT": "GSPE", "GSPEMX": "GSPE", "GSPEMY": "GSPE", "GSPENL": "GSPE", "GSPEPE": "GSPE", "GSPEPH": "GSPE", "GSPEPL": "GSPE", "GSPEPT": "GSPE", "GSPERO": "GSPE", "GSPERU": "GSPE", "GSPESA": "GSPE", "GSPESE": "GSPE", "GSPESG": "GSPE", "GSPESI": "GSPE", "GSPESK": "GSPE", "GSPETH": "GSPE", "GSPETR": "GSPE", "GSPETW": "GSPE", "GSPEUA": "GSPE", "GSPEUK": "GSPE", "GSPEUS": "GSPE", "GSPEUY": "GSPE", "GSPEVN": "GSPE", "GSPEZA": "GSPE", "HHDIRAT": "HHDIR", "HHDIRBE": "HHDIR", "HHDIRCH": "HHDIR", "HHDIRCY": "HHDIR", "HHDIRCZ": "HHDIR", "HHDIRDE": "HHDIR", "HHDIRDK": "HHDIR", "HHDIREE": "HHDIR", "HHDIRES": "HHDIR", "HHDIRFI": "HHDIR", "HHDIRFR": "HHDIR", "HHDIRGR": "HHDIR", "HHDIRHR": "HHDIR", "HHDIRHU": "HHDIR", "HHDIRIE": "HHDIR", "HHDIRIT": "HHDIR", "HHDIRLT": "HHDIR", "HHDIRLU": "HHDIR", "HHDIRLV": "HHDIR", "HHDIRNL": "HHDIR", "HHDIRNO": "HHDIR", "HHDIRPL": "HHDIR", "HHDIRPT": "HHDIR", "HHDIRSE": "HHDIR", "HHDIRSI": "HHDIR", "HHDIRSK": "HHDIR", "HHDIRTR": "HHDIR", "HHSAT": "HHS", "HHSBE": "HHS", "HHSCN": "HHS", "HHSCZ": "HHS", "HHSDE": "HHS", "HHSDK": "HHS", "HHSES": "HHS", "HHSEU": "HHS", "HHSFI": "HHS", "HHSFR": "HHS", "HHSGR": "HHS", "HHSHU": "HHS", "HHSIE": "HHS", "HHSIT": "HHS", "HHSNL": "HHS", "HHSNO": "HHS", "HHSPL": "HHS", "HHSPT": "HHS", "HHSRO": "HHS", "HHSSE": "HHS", "HHSSI": "HHS", "HHSUK": "HHS", "HOU5R": "HOU", "HOUAE": "HOU", "HOUAT": "HOU", "HOUAU": "HOU", "HOUBE": "HOU", "HOUBG": "HOU", "HOUBR": "HOU", "HOUCA": "HOU", "HOUCH": "HOU", "HOUCL": "HOU", "HOUCN": "HOU", "HOUCO": "HOU", "HOUCY": "HOU", "HOUCZ": "HOU", "HOUDE": "HOU", "HOUDK": "HOU", "HOUEA": "HOU", "HOUEE": "HOU", "HOUES": "HOU", "HOUEU": "HOU", "HOUFI": "HOU", "HOUFR": "HOU", "HOUGR": "HOU", "HOUHK": "HOU", "HOUHR": "HOU", "HOUHU": "HOU", "HOUID": "HOU", "HOUIE": "HOU", "HOUIL": "HOU", "HOUIN": "HOU", "HOUIS": "HOU", "HOUIT": "HOU", "HOUJP": "HOU", "HOUKR": "HOU", "HOULT": "HOU", "HOULU": "HOU", "HOULV": "HOU", "HOUMA": "HOU", "HOUMK": "HOU", "HOUMT": "HOU", "HOUMX": "HOU", "HOUMY": "HOU", "HOUNL": "HOU", "HOUNO": "HOU", "HOUNZ": "HOU", "HOUPE": "HOU", "HOUPH": "HOU", "HOUPL": "HOU", "HOUPT": "HOU", "HOURO": "HOU", "HOURU": "HOU", "HOUSE": "HOU", "HOUSG": "HOU", "HOUSI": "HOU", "HOUSK": "HOU", "HOUTH": "HOU", "HOUTR": "HOU", "HOUUK": "HOU", "HOUUS": "HOU", "HOUXW": "HOU", "HOUZA": "HOU", "IBD1AU": "IBD1", "IBD1BG": "IBD1", "IBD1CL": "IBD1", "IBD1CO": "IBD1", "IBD1CZ": "IBD1", "IBD1DK": "IBD1", "IBD1EA": "IBD1", "IBD1HR": "IBD1", "IBD1HU": "IBD1", "IBD1KR": "IBD1", "IBD1MX": "IBD1", "IBD1NZ": "IBD1", "IBD1PL": "IBD1", "IBD1PY": "IBD1", "IBD1RO": "IBD1", "IBD1RU": "IBD1", "IBD1SE": "IBD1", "IBD1SG": "IBD1", "IBD1TH": "IBD1", "IBD1TR": "IBD1", "IBD1UA": "IBD1", "IBD1UK": "IBD1", "IIPAAL": "IIPA", "IIPAAR": "IIPA", "IIPAAT": "IIPA", "IIPAAU": "IIPA", "IIPABA": "IIPA", "IIPABD": "IIPA", "IIPABE": "IIPA", "IIPABG": "IIPA", "IIPABR": "IIPA", "IIPACA": "IIPA", "IIPACH": "IIPA", "IIPACL": "IIPA", "IIPACN": "IIPA", "IIPACO": "IIPA", "IIPACY": "IIPA", "IIPACZ": "IIPA", "IIPADE": "IIPA", "IIPADK": "IIPA", "IIPAEE": "IIPA", "IIPAEG": "IIPA", "IIPAES": "IIPA", "IIPAFI": "IIPA", "IIPAFR": "IIPA", "IIPAGR": "IIPA", "IIPAHK": "IIPA", "IIPAHR": "IIPA", "IIPAHU": "IIPA", "IIPAID": "IIPA", "IIPAIE": "IIPA", "IIPAIL": "IIPA", "IIPAIN": "IIPA", "IIPAIS": "IIPA", "IIPAIT": "IIPA", "IIPAJP": "IIPA", "IIPAKH": "IIPA", "IIPAKR": "IIPA", "IIPALT": "IIPA", "IIPALU": "IIPA", "IIPALV": "IIPA", "IIPAMT": "IIPA", "IIPAMX": "IIPA", "IIPAMY": "IIPA", "IIPANL": "IIPA", "IIPANO": "IIPA", "IIPANP": "IIPA", "IIPAPH": "IIPA", "IIPAPK": "IIPA", "IIPAPL": "IIPA", "IIPAPT": "IIPA", "IIPARO": "IIPA", "IIPARU": "IIPA", "IIPASA": "IIPA", "IIPASE": "IIPA", "IIPASG": "IIPA", "IIPASI": "IIPA", "IIPASK": "IIPA", "IIPATH": "IIPA", "IIPATR": "IIPA", "IIPATW": "IIPA", "IIPAUK": "IIPA", "IIPAUS": "IIPA", "IIPAXK": "IIPA", "IIPAZA": "IIPA", "IIPLAL": "IIPL", "IIPLAR": "IIPL", "IIPLAT": "IIPL", "IIPLAU": "IIPL", "IIPLBA": "IIPL", "IIPLBD": "IIPL", "IIPLBE": "IIPL", "IIPLBG": "IIPL", "IIPLBR": "IIPL", "IIPLCA": "IIPL", "IIPLCH": "IIPL", "IIPLCL": "IIPL", "IIPLCN": "IIPL", "IIPLCO": "IIPL", "IIPLCY": "IIPL", "IIPLCZ": "IIPL", "IIPLDE": "IIPL", "IIPLDK": "IIPL", "IIPLEE": "IIPL", "IIPLEG": "IIPL", "IIPLES": "IIPL", "IIPLFI": "IIPL", "IIPLFR": "IIPL", "IIPLGR": "IIPL", "IIPLHK": "IIPL", "IIPLHR": "IIPL", "IIPLHU": "IIPL", "IIPLID": "IIPL", "IIPLIE": "IIPL", "IIPLIL": "IIPL", "IIPLIN": "IIPL", "IIPLIS": "IIPL", "IIPLIT": "IIPL", "IIPLJP": "IIPL", "IIPLKH": "IIPL", "IIPLKR": "IIPL", "IIPLLT": "IIPL", "IIPLLU": "IIPL", "IIPLLV": "IIPL", "IIPLMT": "IIPL", "IIPLMX": "IIPL", "IIPLMY": "IIPL", "IIPLNL": "IIPL", "IIPLNO": "IIPL", "IIPLNP": "IIPL", "IIPLPH": "IIPL", "IIPLPK": "IIPL", "IIPLPL": "IIPL", "IIPLPT": "IIPL", "IIPLRO": "IIPL", "IIPLRU": "IIPL", "IIPLSA": "IIPL", "IIPLSE": "IIPL", "IIPLSG": "IIPL", "IIPLSI": "IIPL", "IIPLSK": "IIPL", "IIPLTH": "IIPL", "IIPLTR": "IIPL", "IIPLTW": "IIPL", "IIPLUK": "IIPL", "IIPLUS": "IIPL", "IIPLXK": "IIPL", "IIPLZA": "IIPL", "IMPAE": "IMP", "IMPAL": "IMP", "IMPAR": "IMP", "IMPAT": "IMP", "IMPAU": "IMP", "IMPAZ": "IMP", "IMPBD": "IMP", "IMPBE": "IMP", "IMPBG": "IMP", "IMPBR": "IMP", "IMPBW": "IMP", "IMPBY": "IMP", "IMPCA": "IMP", "IMPCH": "IMP", "IMPCL": "IMP", "IMPCM": "IMP", "IMPCN": "IMP", "IMPCO": "IMP", "IMPCY": "IMP", "IMPCZ": "IMP", "IMPDE": "IMP", "IMPDK": "IMP", "IMPEA": "IMP", "IMPEE": "IMP", "IMPES": "IMP", "IMPEU": "IMP", "IMPFI": "IMP", "IMPFR": "IMP", "IMPGR": "IMP", "IMPHK": "IMP", "IMPHR": "IMP", "IMPHU": "IMP", "IMPID": "IMP", "IMPIE": "IMP", "IMPIL": "IMP", "IMPIN": "IMP", "IMPIR": "IMP", "IMPIT": "IMP", "IMPJO": "IMP", "IMPJP": "IMP", "IMPKR": "IMP", "IMPKZ": "IMP", "IMPLT": "IMP", "IMPLU": "IMP", "IMPLV": "IMP", "IMPMA": "IMP", "IMPME": "IMP", "IMPMK": "IMP", "IMPMN": "IMP", "IMPMONAR": "IMPMON", "IMPMONAU": "IMPMON", "IMPMONBD": "IMPMON", "IMPMONBE": "IMPMON", "IMPMONBG": "IMPMON", "IMPMONBR": "IMPMON", "IMPMONCA": "IMPMON", "IMPMONCL": "IMPMON", "IMPMONCN": "IMPMON", "IMPMONCO": "IMPMON", "IMPMONCR": "IMPMON", "IMPMONCZ": "IMPMON", "IMPMONDE": "IMPMON", "IMPMONDK": "IMPMON", "IMPMONEE": "IMPMON", "IMPMONEG": "IMPMON", "IMPMONES": "IMPMON", "IMPMONEU": "IMPMON", "IMPMONFI": "IMPMON", "IMPMONFR": "IMPMON", "IMPMONGR": "IMPMON", "IMPMONHK": "IMPMON", "IMPMONHR": "IMPMON", "IMPMONHU": "IMPMON", "IMPMONID": "IMPMON", "IMPMONIN": "IMPMON", "IMPMONIT": "IMPMON", "IMPMONJP": "IMPMON", "IMPMONKH": "IMPMON", "IMPMONKR": "IMPMON", "IMPMONKZ": "IMPMON", "IMPMONLT": "IMPMON", "IMPMONLU": "IMPMON", "IMPMONLV": "IMPMON", "IMPMONMK": "IMPMON", "IMPMONMT": "IMPMON", "IMPMONMX": "IMPMON", "IMPMONMY": "IMPMON", "IMPMONNL": "IMPMON", "IMPMONNP": "IMPMON", "IMPMONPH": "IMPMON", "IMPMONPK": "IMPMON", "IMPMONPL": "IMPMON", "IMPMONPT": "IMPMON", "IMPMONQA": "IMPMON", "IMPMONRO": "IMPMON", "IMPMONRS": "IMPMON", "IMPMONRU": "IMPMON", "IMPMONSA": "IMPMON", "IMPMONSE": "IMPMON", "IMPMONSG": "IMPMON", "IMPMONSI": "IMPMON", "IMPMONSK": "IMPMON", "IMPMONTH": "IMPMON", "IMPMONTR": "IMPMON", "IMPMONTW": "IMPMON", "IMPMONUA": "IMPMON", "IMPMONUK": "IMPMON", "IMPMONUS": "IMPMON", "IMPMONUY": "IMPMON", "IMPMONVN": "IMPMON", "IMPMONZA": "IMPMON", "IMPMT": "IMP", "IMPMX": "IMP", "IMPMY": "IMP", "IMPNG": "IMP", "IMPNL": "IMP", "IMPNO": "IMP", "IMPNZ": "IMP", "IMPPE": "IMP", "IMPPH": "IMP", "IMPPK": "IMP", "IMPPL": "IMP", "IMPPT": "IMP", "IMPQA": "IMP", "IMPRO": "IMP", "IMPRS": "IMP", "IMPRU": "IMP", "IMPRW": "IMP", "IMPSA": "IMP", "IMPSE": "IMP", "IMPSG": "IMP", "IMPSI": "IMP", "IMPSK": "IMP", "IMPSV": "IMP", "IMPTH": "IMP", "IMPTR": "IMP", "IMPTW": "IMP", "IMPUA": "IMP", "IMPUK": "IMP", "IMPUS": "IMP", "IMPZA": "IMP", "INVERAT": "INVER", "INVERBE": "INVER", "INVERCH": "INVER", "INVERCY": "INVER", "INVERCZ": "INVER", "INVERDE": "INVER", "INVERDK": "INVER", "INVEREE": "INVER", "INVERES": "INVER", "INVERFI": "INVER", "INVERFR": "INVER", "INVERGR": "INVER", "INVERHR": "INVER", "INVERHU": "INVER", "INVERIE": "INVER", "INVERIT": "INVER", "INVERLT": "INVER", "INVERLV": "INVER", "INVERNL": "INVER", "INVERNO": "INVER", "INVERPL": "INVER", "INVERPT": "INVER", "INVERRO": "INVER", "INVERSE": "INVER", "INVERSI": "INVER", "INVERSK": "INVER", "IPAR": "IP", "IPAT": "IP", "IPAU": "IP", "IPAZ": "IP", "IPBA": "IP", "IPBD": "IP", "IPBE": "IP", "IPBG": "IP", "IPBR": "IP", "IPBY": "IP", "IPCA": "IP", "IPCH": "IP", "IPCL": "IP", "IPCM": "IP", "IPCN": "IP", "IPCO": "IP", "IPCR": "IP", "IPCY": "IP", "IPCZ": "IP", "IPDE": "IP", "IPDK": "IP", "IPEE": "IP", "IPEG": "IP", "IPES": "IP", "IPEU": "IP", "IPFI": "IP", "IPFR": "IP", "IPGR": "IP", "IPHK": "IP", "IPHR": "IP", "IPHU": "IP", "IPID": "IP", "IPIE": "IP", "IPIL": "IP", "IPIN": "IP", "IPIT": "IP", "IPJO": "IP", "IPJP": "IP", "IPKR": "IP", "IPKZ": "IP", "IPLT": "IP", "IPLU": "IP", "IPLV": "IP", "IPMA": "IP", "IPME": "IP", "IPMK": "IP", "IPMT": "IP", "IPMX": "IP", "IPMY": "IP", "IPNL": "IP", "IPNO": "IP", "IPPA": "IP", "IPPH": "IP", "IPPK": "IP", "IPPL": "IP", "IPPT": "IP", "IPQA": "IP", "IPRO": "IP", "IPRS": "IP", "IPRU": "IP", "IPSA": "IP", "IPSE": "IP", "IPSG": "IP", "IPSI": "IP", "IPSK": "IP", "IPTH": "IP", "IPTR": "IP", "IPTW": "IP", "IPUA": "IP", "IPUK": "IP", "IPUS": "IP", "IPUY": "IP", "IPVN": "IP", "IPZA": "IP", "JHRUS": "JHR", "JLRUS": "JLR", "JQRUS": "JQR", "JVRAT": "JVR", "JVRBE": "JVR", "JVRBG": "JVR", "JVRCH": "JVR", "JVRCY": "JVR", "JVRCZ": "JVR", "JVRDE": "JVR", "JVREA": "JVR", "JVREE": "JVR", "JVRES": "JVR", "JVREU": "JVR", "JVRFI": "JVR", "JVRGR": "JVR", "JVRHR": "JVR", "JVRHU": "JVR", "JVRIE": "JVR", "JVRIT": "JVR", "JVRLT": "JVR", "JVRLU": "JVR", "JVRLV": "JVR", "JVRMK": "JVR", "JVRMT": "JVR", "JVRNL": "JVR", "JVRNO": "JVR", "JVRPL": "JVR", "JVRPT": "JVR", "JVRRO": "JVR", "JVRSE": "JVR", "JVRSI": "JVR", "JVRSK": "JVR", "JVRUK": "JVR", "JVRUS": "JVR", "KABE": "KA", "KABG": "KA", "KACN": "KA", "KACZ": "KA", "KADE": "KA", "KADK": "KA", "KAEE": "KA", "KAES": "KA", "KAFI": "KA", "KAFR": "KA", "KAGR": "KA", "KAHR": "KA", "KAHU": "KA", "KAIT": "KA", "KALT": "KA", "KALU": "KA", "KALV": "KA", "KAMT": "KA", "KANL": "KA", "KAPH": "KA", "KAPL": "KA", "KAPT": "KA", "KARO": "KA", "KASE": "KA", "KASI": "KA", "KASK": "KA", "LE00CN": "LE00", "LMICS": "LMICS", "M3AR": "M3", "M3AU": "M3", "M3BR": "M3", "M3BY": "M3", "M3CA": "M3", "M3CH": "M3", "M3CL": "M3", "M3CN": "M3", "M3CR": "M3", "M3EA": "M3", "M3HU": "M3", "M3ID": "M3", "M3IN": "M3", "M3JP": "M3", "M3KR": "M3", "M3KZ": "M3", "M3MA": "M3", "M3MX": "M3", "M3MY": "M3", "M3NO": "M3", "M3NP": "M3", "M3PH": "M3", "M3PL": "M3", "M3RU": "M3", "M3SA": "M3", "M3SG": "M3", "M3TH": "M3", "M3TN": "M3", "M3TR": "M3", "M3UA": "M3", "M3UK": "M3", "M3US": "M3", "M3UY": "M3", "M3YDAE": "M3YD", "M3YDAU": "M3YD", "M3YDCA": "M3YD", "M3YDCN": "M3YD", "M3YDCZ": "M3YD", "M3YDDK": "M3YD", "M3YDEA": "M3YD", "M3YDHK": "M3YD", "M3YDHR": "M3YD", "M3YDHU": "M3YD", "M3YDID": "M3YD", "M3YDIN": "M3YD", "M3YDJP": "M3YD", "M3YDKR": "M3YD", "M3YDMO": "M3YD", "M3YDMX": "M3YD", "M3YDMY": "M3YD", "M3YDNP": "M3YD", "M3YDNZ": "M3YD", "M3YDPA": "M3YD", "M3YDPH": "M3YD", "M3YDPL": "M3YD", "M3YDQA": "M3YD", "M3YDRO": "M3YD", "M3YDRU": "M3YD", "M3YDSA": "M3YD", "M3YDSE": "M3YD", "M3YDSG": "M3YD", "M3YDTH": "M3YD", "M3YDTR": "M3YD", "M3YDUA": "M3YD", "M3YDUK": "M3YD", "M3YDUS": "M3YD", "M3YDVN": "M3YD", "M3YDZA": "M3YD", "M3ZA": "M3", "MBAR": "MB", "MBAU": "MB", "MBBR": "MB", "MBCH": "MB", "MBCN": "MB", "MBEG": "MB", "MBIN": "MB", "MBJP": "MB", "MBKR": "MB", "MBMX": "MB", "MBMY": "MB", "MBNO": "MB", "MBRU": "MB", "MBSA": "MB", "MBTH": "MB", "MBTR": "MB", "MBUA": "MB", "MBUK": "MB", "MBUS": "MB", "NCTBE": "NCT", "NCTBG": "NCT", "NCTCN": "NCT", "NCTCZ": "NCT", "NCTDE": "NCT", "NCTDK": "NCT", "NCTEE": "NCT", "NCTES": "NCT", "NCTFI": "NCT", "NCTFR": "NCT", "NCTGR": "NCT", "NCTHR": "NCT", "NCTHU": "NCT", "NCTIT": "NCT", "NCTLT": "NCT", "NCTLU": "NCT", "NCTLV": "NCT", "NCTMT": "NCT", "NCTMX": "NCT", "NCTNL": "NCT", "NCTPH": "NCT", "NCTPL": "NCT", "NCTPT": "NCT", "NCTRO": "NCT", "NCTSE": "NCT", "NCTSI": "NCT", "NCTSK": "NCT", "NFCIAT": "NFCI", "NFCIBE": "NFCI", "NFCICZ": "NFCI", "NFCIDE": "NFCI", "NFCIDK": "NFCI", "NFCIEE": "NFCI", "NFCIES": "NFCI", "NFCIEU": "NFCI", "NFCIFI": "NFCI", "NFCIFR": "NFCI", "NFCIGR": "NFCI", "NFCIHU": "NFCI", "NFCIIE": "NFCI", "NFCIIT": "NFCI", "NFCINL": "NFCI", "NFCINO": "NFCI", "NFCIPL": "NFCI", "NFCIPT": "NFCI", "NFCIRO": "NFCI", "NFCISE": "NFCI", "NFCIUK": "NFCI", "NFCLOANBG": "NFCLOAN", "NFCLOANCY": "NFCLOAN", "NFCLOANEE": "NFCLOAN", "NFCLOANHR": "NFCLOAN", "NFCLOANLT": "NFCLOAN", "NFCLOANLV": "NFCLOAN", "NFCLOANMT": "NFCLOAN", "NFCLOANRO": "NFCLOAN", "NFCLOANSI": "NFCLOAN", "NFCLOANSK": "NFCLOAN", "NIIPAL": "NIIP", "NIIPAR": "NIIP", "NIIPAT": "NIIP", "NIIPAU": "NIIP", "NIIPBA": "NIIP", "NIIPBD": "NIIP", "NIIPBE": "NIIP", "NIIPBG": "NIIP", "NIIPBR": "NIIP", "NIIPCA": "NIIP", "NIIPCH": "NIIP", "NIIPCL": "NIIP", "NIIPCN": "NIIP", "NIIPCO": "NIIP", "NIIPCY": "NIIP", "NIIPCZ": "NIIP", "NIIPDE": "NIIP", "NIIPDK": "NIIP", "NIIPEE": "NIIP", "NIIPEG": "NIIP", "NIIPES": "NIIP", "NIIPFI": "NIIP", "NIIPFR": "NIIP", "NIIPGR": "NIIP", "NIIPHK": "NIIP", "NIIPHR": "NIIP", "NIIPHU": "NIIP", "NIIPID": "NIIP", "NIIPIE": "NIIP", "NIIPIL": "NIIP", "NIIPIN": "NIIP", "NIIPIS": "NIIP", "NIIPIT": "NIIP", "NIIPJP": "NIIP", "NIIPKH": "NIIP", "NIIPKR": "NIIP", "NIIPLT": "NIIP", "NIIPLU": "NIIP", "NIIPLV": "NIIP", "NIIPMK": "NIIP", "NIIPMT": "NIIP", "NIIPMX": "NIIP", "NIIPMY": "NIIP", "NIIPNL": "NIIP", "NIIPNO": "NIIP", "NIIPNP": "NIIP", "NIIPPH": "NIIP", "NIIPPK": "NIIP", "NIIPPL": "NIIP", "NIIPPT": "NIIP", "NIIPRO": "NIIP", "NIIPRS": "NIIP", "NIIPRU": "NIIP", "NIIPSA": "NIIP", "NIIPSE": "NIIP", "NIIPSG": "NIIP", "NIIPSI": "NIIP", "NIIPSK": "NIIP", "NIIPTH": "NIIP", "NIIPTR": "NIIP", "NIIPTW": "NIIP", "NIIPUA": "NIIP", "NIIPUK": "NIIP", "NIIPUS": "NIIP", "NIIPXK": "NIIP", "NIIPZA": "NIIP", "NPLAR": "NPL", "NPLAT": "NPL", "NPLAU": "NPL", "NPLBE": "NPL", "NPLBR": "NPL", "NPLCN": "NPL", "NPLDK": "NPL", "NPLES": "NPL", "NPLFI": "NPL", "NPLFR": "NPL", "NPLHK": "NPL", "NPLID": "NPL", "NPLIE": "NPL", "NPLIN": "NPL", "NPLIT": "NPL", "NPLJP": "NPL", "NPLLU": "NPL", "NPLMX": "NPL", "NPLNL": "NPL", "NPLNO": "NPL", "NPLPL": "NPL", "NPLRU": "NPL", "NPLSA": "NPL", "NPLSE": "NPL", "NPLSG": "NPL", "NPLTR": "NPL", "NPLUS": "NPL", "NPLZA": "NPL", "NYBE": "NY", "NYBG": "NY", "NYCN": "NY", "NYCZ": "NY", "NYDE": "NY", "NYDK": "NY", "NYEE": "NY", "NYES": "NY", "NYFI": "NY", "NYFR": "NY", "NYGR": "NY", "NYHR": "NY", "NYHU": "NY", "NYIT": "NY", "NYLT": "NY", "NYLU": "NY", "NYLV": "NY", "NYMT": "NY", "NYMX": "NY", "NYNL": "NY", "NYPH": "NY", "NYPL": "NY", "NYPT": "NY", "NYRO": "NY", "NYSE": "NY", "NYSI": "NY", "NYSK": "NY", "OILDEMAE": "OILDEM", "OILDEMAL": "OILDEM", "OILDEMAM": "OILDEM", "OILDEMAO": "OILDEM", "OILDEMAR": "OILDEM", "OILDEMAT": "OILDEM", "OILDEMAU": "OILDEM", "OILDEMAZ": "OILDEM", "OILDEMBB": "OILDEM", "OILDEMBD": "OILDEM", "OILDEMBE": "OILDEM", "OILDEMBG": "OILDEM", "OILDEMBH": "OILDEM", "OILDEMBM": "OILDEM", "OILDEMBN": "OILDEM", "OILDEMBO": "OILDEM", "OILDEMBR": "OILDEM", "OILDEMBY": "OILDEM", "OILDEMBZ": "OILDEM", "OILDEMCA": "OILDEM", "OILDEMCH": "OILDEM", "OILDEMCL": "OILDEM", "OILDEMCN": "OILDEM", "OILDEMCO": "OILDEM", "OILDEMCR": "OILDEM", "OILDEMCU": "OILDEM", "OILDEMCY": "OILDEM", "OILDEMCZ": "OILDEM", "OILDEMDE": "OILDEM", "OILDEMDK": "OILDEM", "OILDEMDO": "OILDEM", "OILDEMDZ": "OILDEM", "OILDEMEC": "OILDEM", "OILDEMEE": "OILDEM", "OILDEMEG": "OILDEM", "OILDEMES": "OILDEM", "OILDEMFI": "OILDEM", "OILDEMFR": "OILDEM", "OILDEMGA": "OILDEM", "OILDEMGD": "OILDEM", "OILDEMGE": "OILDEM", "OILDEMGM": "OILDEM", "OILDEMGQ": "OILDEM", "OILDEMGR": "OILDEM", "OILDEMGT": "OILDEM", "OILDEMGY": "OILDEM", "OILDEMHK": "OILDEM", "OILDEMHN": "OILDEM", "OILDEMHR": "OILDEM", "OILDEMHT": "OILDEM", "OILDEMHU": "OILDEM", "OILDEMID": "OILDEM", "OILDEMIE": "OILDEM", "OILDEMIN": "OILDEM", "OILDEMIQ": "OILDEM", "OILDEMIR": "OILDEM", "OILDEMIS": "OILDEM", "OILDEMIT": "OILDEM", "OILDEMJM": "OILDEM", "OILDEMJP": "OILDEM", "OILDEMKR": "OILDEM", "OILDEMKW": "OILDEM", "OILDEMKZ": "OILDEM", "OILDEMLT": "OILDEM", "OILDEMLU": "OILDEM", "OILDEMLV": "OILDEM", "OILDEMLY": "OILDEM", "OILDEMMA": "OILDEM", "OILDEMMD": "OILDEM", "OILDEMMK": "OILDEM", "OILDEMMM": "OILDEM", "OILDEMMT": "OILDEM", "OILDEMMU": "OILDEM", "OILDEMMX": "OILDEM", "OILDEMMY": "OILDEM", "OILDEMNE": "OILDEM", "OILDEMNG": "OILDEM", "OILDEMNI": "OILDEM", "OILDEMNL": "OILDEM", "OILDEMNO": "OILDEM", "OILDEMNP": "OILDEM", "OILDEMNZ": "OILDEM", "OILDEMOM": "OILDEM", "OILDEMPA": "OILDEM", "OILDEMPE": "OILDEM", "OILDEMPG": "OILDEM", "OILDEMPH": "OILDEM", "OILDEMPL": "OILDEM", "OILDEMPT": "OILDEM", "OILDEMPY": "OILDEM", "OILDEMQA": "OILDEM", "OILDEMRO": "OILDEM", "OILDEMRU": "OILDEM", "OILDEMSA": "OILDEM", "OILDEMSD": "OILDEM", "OILDEMSE": "OILDEM", "OILDEMSG": "OILDEM", "OILDEMSI": "OILDEM", "OILDEMSK": "OILDEM", "OILDEMSR": "OILDEM", "OILDEMSV": "OILDEM", "OILDEMSY": "OILDEM", "OILDEMSZ": "OILDEM", "OILDEMTH": "OILDEM", "OILDEMTJ": "OILDEM", "OILDEMTN": "OILDEM", "OILDEMTR": "OILDEM", "OILDEMTT": "OILDEM", "OILDEMTW": "OILDEM", "OILDEMUA": "OILDEM", "OILDEMUK": "OILDEM", "OILDEMUS": "OILDEM", "OILDEMUY": "OILDEM", "OILDEMVE": "OILDEM", "OILDEMVN": "OILDEM", "OILDEMYE": "OILDEM", "OILDEMZA": "OILDEM", "OILPRODAE": "OILPROD", "OILPRODAL": "OILPROD", "OILPRODAM": "OILPROD", "OILPRODAO": "OILPROD", "OILPRODAR": "OILPROD", "OILPRODAT": "OILPROD", "OILPRODAU": "OILPROD", "OILPRODAZ": "OILPROD", "OILPRODBB": "OILPROD", "OILPRODBD": "OILPROD", "OILPRODBE": "OILPROD", "OILPRODBG": "OILPROD", "OILPRODBH": "OILPROD", "OILPRODBM": "OILPROD", "OILPRODBN": "OILPROD", "OILPRODBO": "OILPROD", "OILPRODBR": "OILPROD", "OILPRODBY": "OILPROD", "OILPRODBZ": "OILPROD", "OILPRODCA": "OILPROD", "OILPRODCH": "OILPROD", "OILPRODCL": "OILPROD", "OILPRODCN": "OILPROD", "OILPRODCO": "OILPROD", "OILPRODCR": "OILPROD", "OILPRODCU": "OILPROD", "OILPRODCY": "OILPROD", "OILPRODCZ": "OILPROD", "OILPRODDE": "OILPROD", "OILPRODDK": "OILPROD", "OILPRODDO": "OILPROD", "OILPRODDZ": "OILPROD", "OILPRODEC": "OILPROD", "OILPRODEE": "OILPROD", "OILPRODEG": "OILPROD", "OILPRODES": "OILPROD", "OILPRODFI": "OILPROD", "OILPRODFR": "OILPROD", "OILPRODGA": "OILPROD", "OILPRODGD": "OILPROD", "OILPRODGE": "OILPROD", "OILPRODGM": "OILPROD", "OILPRODGQ": "OILPROD", "OILPRODGR": "OILPROD", "OILPRODGT": "OILPROD", "OILPRODGY": "OILPROD", "OILPRODHK": "OILPROD", "OILPRODHN": "OILPROD", "OILPRODHR": "OILPROD", "OILPRODHT": "OILPROD", "OILPRODHU": "OILPROD", "OILPRODID": "OILPROD", "OILPRODIE": "OILPROD", "OILPRODIN": "OILPROD", "OILPRODIQ": "OILPROD", "OILPRODIR": "OILPROD", "OILPRODIS": "OILPROD", "OILPRODIT": "OILPROD", "OILPRODJM": "OILPROD", "OILPRODJP": "OILPROD", "OILPRODKR": "OILPROD", "OILPRODKW": "OILPROD", "OILPRODKZ": "OILPROD", "OILPRODLT": "OILPROD", "OILPRODLU": "OILPROD", "OILPRODLV": "OILPROD", "OILPRODLY": "OILPROD", "OILPRODMA": "OILPROD", "OILPRODMD": "OILPROD", "OILPRODMK": "OILPROD", "OILPRODMM": "OILPROD", "OILPRODMT": "OILPROD", "OILPRODMU": "OILPROD", "OILPRODMX": "OILPROD", "OILPRODMY": "OILPROD", "OILPRODNE": "OILPROD", "OILPRODNG": "OILPROD", "OILPRODNI": "OILPROD", "OILPRODNL": "OILPROD", "OILPRODNO": "OILPROD", "OILPRODNP": "OILPROD", "OILPRODNZ": "OILPROD", "OILPRODOM": "OILPROD", "OILPRODPA": "OILPROD", "OILPRODPE": "OILPROD", "OILPRODPG": "OILPROD", "OILPRODPH": "OILPROD", "OILPRODPL": "OILPROD", "OILPRODPT": "OILPROD", "OILPRODPY": "OILPROD", "OILPRODQA": "OILPROD", "OILPRODRO": "OILPROD", "OILPRODRU": "OILPROD", "OILPRODSA": "OILPROD", "OILPRODSD": "OILPROD", "OILPRODSE": "OILPROD", "OILPRODSG": "OILPROD", "OILPRODSI": "OILPROD", "OILPRODSK": "OILPROD", "OILPRODSR": "OILPROD", "OILPRODSV": "OILPROD", "OILPRODSY": "OILPROD", "OILPRODSZ": "OILPROD", "OILPRODTH": "OILPROD", "OILPRODTJ": "OILPROD", "OILPRODTN": "OILPROD", "OILPRODTR": "OILPROD", "OILPRODTT": "OILPROD", "OILPRODTW": "OILPROD", "OILPRODUA": "OILPROD", "OILPRODUK": "OILPROD", "OILPRODUS": "OILPROD", "OILPRODUY": "OILPROD", "OILPRODVE": "OILPROD", "OILPRODVN": "OILPROD", "OILPRODYE": "OILPROD", "OILPRODZA": "OILPROD", "PALUM": "PALUM", "PAPPLE": "PAPPLE", "PARTCO": "PART", "PARTDO": "PART", "PARTLK": "PART", "PARTNZ": "PART", "PARTTR": "PART", "PBANSOP": "PBANSOP", "PBARL": "PBARL", "PBEEF": "PBEEF", "PCEUS": "PCE", "PCHANA": "PCHANA", "PCHROM": "PCHROM", "PCOALAU": "PCOALAU", "PCOALSA": "PCOALSA", "PCOBA": "PCOBA", "PCOCO": "PCOCO", "PCOFFOTM": "PCOFFOTM", "PCOFFROB": "PCOFFROB", "PCOIL": "PCOIL", "PCOPP": "PCOPP", "PCOTTIND": "PCOTTIND", "PDAP": "PDAP", "PFSHMEAL": "PFSHMEAL", "PGASO": "PGASO", "PGNUTS": "PGNUTS", "PGOLD": "PGOLD", "PHEATOIL": "PHEATOIL", "PHIDE": "PHIDE", "PIORECR": "PIORECR", "PLAMB": "PLAMB", "PLEAD": "PLEAD", "PLITH": "PLITH", "PLMMODY": "PLMMODY", "PLOGORE": "PLOGORE", "PLOGSK": "PLOGSK", "PMAIZMT": "PMAIZMT", "PMANGELE": "PMANGELE", "PMILK": "PMILK", "PNGASEU": "PNGASEU", "PNGASJP": "PNGASJP", "PNGASUS": "PNGASUS", "PNICK": "PNICK", "POATS": "POATS", "POILAPSP": "POILAPSP", "POILBRE": "POILBRE", "POILDUB": "POILDUB", "POILWTI": "POILWTI", "POLIRAR": "POLIR", "POLIRAU": "POLIR", "POLIRAZ": "POLIR", "POLIRBR": "POLIR", "POLIRCA": "POLIR", "POLIRCH": "POLIR", "POLIRCL": "POLIR", "POLIRCN": "POLIR", "POLIRCO": "POLIR", "POLIRCZ": "POLIR", "POLIRDK": "POLIR", "POLIREA": "POLIR", "POLIRHK": "POLIR", "POLIRHR": "POLIR", "POLIRHU": "POLIR", "POLIRID": "POLIR", "POLIRIL": "POLIR", "POLIRIN": "POLIR", "POLIRIS": "POLIR", "POLIRJP": "POLIR", "POLIRKR": "POLIR", "POLIRMK": "POLIR", "POLIRMO": "POLIR", "POLIRMX": "POLIR", "POLIRMY": "POLIR", "POLIRNO": "POLIR", "POLIRNP": "POLIR", "POLIRNZ": "POLIR", "POLIRPE": "POLIR", "POLIRPH": "POLIR", "POLIRPL": "POLIR", "POLIRPY": "POLIR", "POLIRQA": "POLIR", "POLIRRO": "POLIR", "POLIRRS": "POLIR", "POLIRRU": "POLIR", "POLIRSA": "POLIR", "POLIRSE": "POLIR", "POLIRSG": "POLIR", "POLIRTH": "POLIR", "POLIRTN": "POLIR", "POLIRTR": "POLIR", "POLIRTW": "POLIR", "POLIRUA": "POLIR", "POLIRUK": "POLIR", "POLIRUS": "POLIR", "POLIRUZ": "POLIR", "POLIRZA": "POLIR", "POLVOIL": "POLVOIL", "POPAD": "POP", "POPAE": "POP", "POPAL": "POP", "POPAM": "POP", "POPAR": "POP", "POPAT": "POP", "POPAU": "POP", "POPAZ": "POP", "POPBD": "POP", "POPBE": "POP", "POPBG": "POP", "POPBO": "POP", "POPBR": "POP", "POPBY": "POP", "POPCA": "POP", "POPCD": "POP", "POPCH": "POP", "POPCL": "POP", "POPCN": "POP", "POPCO": "POP", "POPCR": "POP", "POPCY": "POP", "POPCZ": "POP", "POPDE": "POP", "POPDK": "POP", "POPDO": "POP", "POPDZ": "POP", "POPEC": "POP", "POPEE": "POP", "POPEG": "POP", "POPES": "POP", "POPET": "POP", "POPEU": "POP", "POPFI": "POP", "POPFR": "POP", "POPGE": "POP", "POPGR": "POP", "POPGT": "POP", "POPHK": "POP", "POPHR": "POP", "POPHU": "POP", "POPID": "POP", "POPIE": "POP", "POPIL": "POP", "POPIN": "POP", "POPIQ": "POP", "POPIR": "POP", "POPIS": "POP", "POPIT": "POP", "POPJO": "POP", "POPJP": "POP", "POPKG": "POP", "POPKH": "POP", "POPKR": "POP", "POPKW": "POP", "POPKZ": "POP", "POPLB": "POP", "POPLI": "POP", "POPLK": "POP", "POPLT": "POP", "POPLU": "POP", "POPLV": "POP", "POPLY": "POP", "POPMA": "POP", "POPMD": "POP", "POPME": "POP", "POPMK": "POP", "POPMM": "POP", "POPMO": "POP", "POPMT": "POP", "POPMX": "POP", "POPMY": "POP", "POPNG": "POP", "POPNI": "POP", "POPNL": "POP", "POPNO": "POP", "POPNZ": "POP", "POPPH": "POP", "POPPL": "POP", "POPPT": "POP", "POPPY": "POP", "POPQA": "POP", "POPRO": "POP", "POPRS": "POP", "POPRU": "POP", "POPSA": "POP", "POPSD": "POP", "POPSE": "POP", "POPSG": "POP", "POPSI": "POP", "POPSK": "POP", "POPSM": "POP", "POPSN": "POP", "POPSV": "POP", "POPTH": "POP", "POPTJ": "POP", "POPTM": "POP", "POPTN": "POP", "POPTR": "POP", "POPTW": "POP", "POPUA": "POP", "POPUK": "POP", "POPUS": "POP", "POPUY": "POP", "POPUZ": "POP", "POPVE": "POP", "POPVN": "POP", "POPXK": "POP", "POPZA": "POP", "PORANG": "PORANG", "PPALLA": "PPALLA", "PPIAL": "PPI", "PPIAR": "PPI", "PPIAT": "PPI", "PPIAU": "PPI", "PPIBD": "PPI", "PPIBE": "PPI", "PPIBG": "PPI", "PPIBR": "PPI", "PPIBY": "PPI", "PPICA": "PPI", "PPICH": "PPI", "PPICL": "PPI", "PPICN": "PPI", "PPICO": "PPI", "PPICR": "PPI", "PPICY": "PPI", "PPICZ": "PPI", "PPIDE": "PPI", "PPIDK": "PPI", "PPIEE": "PPI", "PPIEG": "PPI", "PPIES": "PPI", "PPIEU": "PPI", "PPIFI": "PPI", "PPIFR": "PPI", "PPIGR": "PPI", "PPIHK": "PPI", "PPIHR": "PPI", "PPIHU": "PPI", "PPIID": "PPI", "PPIIE": "PPI", "PPIIL": "PPI", "PPIIN": "PPI", "PPIIT": "PPI", "PPIJO": "PPI", "PPIJP": "PPI", "PPIKR": "PPI", "PPIKZ": "PPI", "PPILT": "PPI", "PPILU": "PPI", "PPILV": "PPI", "PPIMA": "PPI", "PPIME": "PPI", "PPIMK": "PPI", "PPIMT": "PPI", "PPIMX": "PPI", "PPIMY": "PPI", "PPINL": "PPI", "PPINO": "PPI", "PPIPA": "PPI", "PPIPE": "PPI", "PPIPH": "PPI", "PPIPK": "PPI", "PPIPL": "PPI", "PPIPT": "PPI", "PPIQA": "PPI", "PPIRO": "PPI", "PPIRS": "PPI", "PPIRU": "PPI", "PPISA": "PPI", "PPISE": "PPI", "PPISG": "PPI", "PPISI": "PPI", "PPISK": "PPI", "PPISV": "PPI", "PPITH": "PPI", "PPITN": "PPI", "PPITR": "PPI", "PPITW": "PPI", "PPIUA": "PPI", "PPIUK": "PPI", "PPIUS": "PPI", "PPIUY": "PPI", "PPIVN": "PPI", "PPIZA": "PPI", "PPLAT": "PPLAT", "PPOIL": "PPOIL", "PPORK": "PPORK", "PPOTASH": "PPOTASH", "PPOULT": "PPOULT", "PPROPANE": "PPROPANE", "PRCAE": "PRC", "PRCAR": "PRC", "PRCAT": "PRC", "PRCAU": "PRC", "PRCAZ": "PRC", "PRCBD": "PRC", "PRCBE": "PRC", "PRCBG": "PRC", "PRCBR": "PRC", "PRCBW": "PRC", "PRCBY": "PRC", "PRCCA": "PRC", "PRCCH": "PRC", "PRCCL": "PRC", "PRCCN": "PRC", "PRCCO": "PRC", "PRCCY": "PRC", "PRCCZ": "PRC", "PRCDE": "PRC", "PRCDK": "PRC", "PRCEA": "PRC", "PRCEE": "PRC", "PRCES": "PRC", "PRCEU": "PRC", "PRCFI": "PRC", "PRCFR": "PRC", "PRCGR": "PRC", "PRCHK": "PRC", "PRCHR": "PRC", "PRCHU": "PRC", "PRCID": "PRC", "PRCIE": "PRC", "PRCIL": "PRC", "PRCIN": "PRC", "PRCIR": "PRC", "PRCIT": "PRC", "PRCJP": "PRC", "PRCKR": "PRC", "PRCKZ": "PRC", "PRCLT": "PRC", "PRCLU": "PRC", "PRCLV": "PRC", "PRCMA": "PRC", "PRCME": "PRC", "PRCMK": "PRC", "PRCMN": "PRC", "PRCMT": "PRC", "PRCMX": "PRC", "PRCMY": "PRC", "PRCNG": "PRC", "PRCNL": "PRC", "PRCNO": "PRC", "PRCNZ": "PRC", "PRCPE": "PRC", "PRCPH": "PRC", "PRCPK": "PRC", "PRCPL": "PRC", "PRCPT": "PRC", "PRCQA": "PRC", "PRCRO": "PRC", "PRCRS": "PRC", "PRCRU": "PRC", "PRCSA": "PRC", "PRCSE": "PRC", "PRCSG": "PRC", "PRCSI": "PRC", "PRCSK": "PRC", "PRCTH": "PRC", "PRCTR": "PRC", "PRCTW": "PRC", "PRCUA": "PRC", "PRCUK": "PRC", "PRCUS": "PRC", "PRCVN": "PRC", "PRCZA": "PRC", "PREODOM": "PREODOM", "PRICENPQ": "PRICENPQ", "PRIDEBTBG": "PRIDEBT", "PRIDEBTCY": "PRIDEBT", "PRIDEBTEE": "PRIDEBT", "PRIDEBTHR": "PRIDEBT", "PRIDEBTLT": "PRIDEBT", "PRIDEBTLV": "PRIDEBT", "PRIDEBTMT": "PRIDEBT", "PRIDEBTRO": "PRIDEBT", "PRIDEBTSI": "PRIDEBT", "PRIDEBTSK": "PRIDEBT", "PROIL": "PROIL", "PRUBB": "PRUBB", "PSALM": "PSALM", "PSAWMAL": "PSAWMAL", "PSAWORE": "PSAWORE", "PSHRI": "PSHRI", "PSILLUMP": "PSILLUMP", "PSILVER": "PSILVER", "PSMEA": "PSMEA", "PSOIL": "PSOIL", "PSORG": "PSORG", "PSOYB": "PSOYB", "PSUGAISA": "PSUGAISA", "PSUGAUSA": "PSUGAUSA", "PSUNO": "PSUNO", "PTEA": "PTEA", "PTEAINDIA": "PTEAINDIA", "PTEAMOM": "PTEAMOM", "PTEASL": "PTEASL", "PTIN": "PTIN", "PTOMATO": "PTOMATO", "PUCAE": "PUC", "PUCAL": "PUC", "PUCAR": "PUC", "PUCAT": "PUC", "PUCAU": "PUC", "PUCAZ": "PUC", "PUCBD": "PUC", "PUCBE": "PUC", "PUCBG": "PUC", "PUCBR": "PUC", "PUCBW": "PUC", "PUCBY": "PUC", "PUCCA": "PUC", "PUCCH": "PUC", "PUCCL": "PUC", "PUCCM": "PUC", "PUCCN": "PUC", "PUCCO": "PUC", "PUCCY": "PUC", "PUCCZ": "PUC", "PUCDE": "PUC", "PUCDK": "PUC", "PUCEA": "PUC", "PUCEE": "PUC", "PUCES": "PUC", "PUCEU": "PUC", "PUCFI": "PUC", "PUCFR": "PUC", "PUCGR": "PUC", "PUCHK": "PUC", "PUCHR": "PUC", "PUCHU": "PUC", "PUCID": "PUC", "PUCIE": "PUC", "PUCIL": "PUC", "PUCIN": "PUC", "PUCIR": "PUC", "PUCIT": "PUC", "PUCJP": "PUC", "PUCKR": "PUC", "PUCKZ": "PUC", "PUCLT": "PUC", "PUCLU": "PUC", "PUCLV": "PUC", "PUCMA": "PUC", "PUCME": "PUC", "PUCMK": "PUC", "PUCMT": "PUC", "PUCMX": "PUC", "PUCMY": "PUC", "PUCNG": "PUC", "PUCNL": "PUC", "PUCNO": "PUC", "PUCNZ": "PUC", "PUCPE": "PUC", "PUCPH": "PUC", "PUCPK": "PUC", "PUCPL": "PUC", "PUCPT": "PUC", "PUCQA": "PUC", "PUCRO": "PUC", "PUCRS": "PUC", "PUCRU": "PUC", "PUCSA": "PUC", "PUCSE": "PUC", "PUCSG": "PUC", "PUCSI": "PUC", "PUCSK": "PUC", "PUCTH": "PUC", "PUCTR": "PUC", "PUCTW": "PUC", "PUCUA": "PUC", "PUCUK": "PUC", "PUCUS": "PUC", "PUCVN": "PUC", "PUCZA": "PUC", "PURAN": "PURAN", "PUREA": "PUREA", "PVANPENT": "PVANPENT", "PWHEAMT": "PWHEAMT", "PWOOLC": "PWOOLC", "PWOOLF": "PWOOLF", "PZINC": "PZINC", "RCIAE": "RCI", "RCIAR": "RCI", "RCIAU": "RCI", "RCIAZ": "RCI", "RCIBW": "RCI", "RCIBY": "RCI", "RCICN": "RCI", "RCIHK": "RCI", "RCIIN": "RCI", "RCIIR": "RCI", "RCIKH": "RCI", "RCIKR": "RCI", "RCIMN": "RCI", "RCIMO": "RCI", "RCIMY": "RCI", "RCING": "RCI", "RCINO": "RCI", "RCINZ": "RCI", "RCIPA": "RCI", "RCIPH": "RCI", "RCIPK": "RCI", "RCISA": "RCI", "RCISG": "RCI", "RCITH": "RCI", "RCITW": "RCI", "RCIUA": "RCI", "RCIUK": "RCI", "RCIUS": "RCI", "RCIVN": "RCI", "RCIZA": "RCI", "RCONAE": "RCON", "RCONAL": "RCON", "RCONAT": "RCON", "RCONBA": "RCON", "RCONBD": "RCON", "RCONBE": "RCON", "RCONBG": "RCON", "RCONBY": "RCON", "RCONCA": "RCON", "RCONCH": "RCON", "RCONCL": "RCON", "RCONCY": "RCON", "RCONCZ": "RCON", "RCONDE": "RCON", "RCONDK": "RCON", "RCONEA": "RCON", "RCONEE": "RCON", "RCONES": "RCON", "RCONEU": "RCON", "RCONFI": "RCON", "RCONFR": "RCON", "RCONGR": "RCON", "RCONHR": "RCON", "RCONHU": "RCON", "RCONIE": "RCON", "RCONIT": "RCON", "RCONKR": "RCON", "RCONLT": "RCON", "RCONLU": "RCON", "RCONLV": "RCON", "RCONMK": "RCON", "RCONMT": "RCON", "RCONMX": "RCON", "RCONNL": "RCON", "RCONNO": "RCON", "RCONPL": "RCON", "RCONPT": "RCON", "RCONRO": "RCON", "RCONRS": "RCON", "RCONSE": "RCON", "RCONSI": "RCON", "RCONSK": "RCON", "RCONTH": "RCON", "RCONUA": "RCON", "RCONUK": "RCON", "REERAE": "REER", "REERAR": "REER", "REERAT": "REER", "REERAU": "REER", "REERAZ": "REER", "REERBE": "REER", "REERBG": "REER", "REERBR": "REER", "REERCA": "REER", "REERCH": "REER", "REERCL": "REER", "REERCN": "REER", "REERCO": "REER", "REERCY": "REER", "REERCZ": "REER", "REERDE": "REER", "REERDK": "REER", "REERDZ": "REER", "REEREA": "REEREA", "REEREE": "REER", "REERES": "REER", "REERFI": "REER", "REERFR": "REER", "REERGR": "REER", "REERHK": "REER", "REERHR": "REER", "REERHU": "REER", "REERID": "REER", "REERIE": "REER", "REERIL": "REER", "REERIN": "REER", "REERIS": "REER", "REERIT": "REER", "REERJP": "REER", "REERKR": "REER", "REERLT": "REER", "REERLU": "REER", "REERLV": "REER", "REERMT": "REER", "REERMX": "REER", "REERMY": "REER", "REERNL": "REER", "REERNO": "REER", "REERNZ": "REER", "REERPE": "REER", "REERPH": "REER", "REERPL": "REER", "REERPT": "REER", "REERPY": "REER", "REERRO": "REER", "REERRS": "REER", "REERRU": "REER", "REERSA": "REER", "REERSE": "REER", "REERSG": "REER", "REERSI": "REER", "REERSK": "REER", "REERTH": "REER", "REERTR": "REER", "REERTW": "REER", "REERUK": "REER", "REERUS": "REER", "REERZA": "REER", "RETAAL": "RETA", "RETAAR": "RETA", "RETAAT": "RETA", "RETAAU": "RETA", "RETAAZ": "RETA", "RETABA": "RETA", "RETABE": "RETA", "RETABG": "RETA", "RETABR": "RETA", "RETACA": "RETA", "RETACH": "RETA", "RETACN": "RETA", "RETACO": "RETA", "RETACY": "RETA", "RETACZ": "RETA", "RETADE": "RETA", "RETADK": "RETA", "RETAEE": "RETA", "RETAES": "RETA", "RETAEU": "RETA", "RETAFI": "RETA", "RETAFR": "RETA", "RETAGR": "RETA", "RETAHK": "RETA", "RETAHR": "RETA", "RETAHU": "RETA", "RETAID": "RETA", "RETAIE": "RETA", "RETAIL": "RETA", "RETAIR": "RETA", "RETAIT": "RETA", "RETAJP": "RETA", "RETAKR": "RETA", "RETALT": "RETA", "RETALU": "RETA", "RETALV": "RETA", "RETAME": "RETA", "RETAMK": "RETA", "RETAMT": "RETA", "RETAMX": "RETA", "RETAMY": "RETA", "RETANL": "RETA", "RETANO": "RETA", "RETANZ": "RETA", "RETAPL": "RETA", "RETAPT": "RETA", "RETARO": "RETA", "RETARS": "RETA", "RETARU": "RETA", "RETASE": "RETA", "RETASG": "RETA", "RETASI": "RETA", "RETASK": "RETA", "RETATH": "RETA", "RETATR": "RETA", "RETATW": "RETA", "RETAUK": "RETA", "RETAUS": "RETA", "RETAVN": "RETA", "RETAZA": "RETA", "REXPAE": "REXP", "REXPAL": "REXP", "REXPAR": "REXP", "REXPAT": "REXP", "REXPAU": "REXP", "REXPAZ": "REXP", "REXPBA": "REXP", "REXPBD": "REXP", "REXPBE": "REXP", "REXPBG": "REXP", "REXPBR": "REXP", "REXPBW": "REXP", "REXPBY": "REXP", "REXPCA": "REXP", "REXPCH": "REXP", "REXPCL": "REXP", "REXPCN": "REXP", "REXPCO": "REXP", "REXPCY": "REXP", "REXPCZ": "REXP", "REXPDE": "REXP", "REXPDK": "REXP", "REXPEA": "REXP", "REXPEE": "REXP", "REXPES": "REXP", "REXPEU": "REXP", "REXPFI": "REXP", "REXPFR": "REXP", "REXPGR": "REXP", "REXPHK": "REXP", "REXPHN": "REXP", "REXPHR": "REXP", "REXPHU": "REXP", "REXPID": "REXP", "REXPIE": "REXP", "REXPIL": "REXP", "REXPIN": "REXP", "REXPIR": "REXP", "REXPIT": "REXP", "REXPJP": "REXP", "REXPKH": "REXP", "REXPKR": "REXP", "REXPLT": "REXP", "REXPLU": "REXP", "REXPLV": "REXP", "REXPMA": "REXP", "REXPMK": "REXP", "REXPMN": "REXP", "REXPMO": "REXP", "REXPMT": "REXP", "REXPMX": "REXP", "REXPMY": "REXP", "REXPNG": "REXP", "REXPNL": "REXP", "REXPNO": "REXP", "REXPNZ": "REXP", "REXPPA": "REXP", "REXPPE": "REXP", "REXPPH": "REXP", "REXPPK": "REXP", "REXPPL": "REXP", "REXPPT": "REXP", "REXPRO": "REXP", "REXPRS": "REXP", "REXPRU": "REXP", "REXPRW": "REXP", "REXPSA": "REXP", "REXPSE": "REXP", "REXPSG": "REXP", "REXPSI": "REXP", "REXPSK": "REXP", "REXPTH": "REXP", "REXPTR": "REXP", "REXPTW": "REXP", "REXPUA": "REXP", "REXPUK": "REXP", "REXPUS": "REXP", "REXPUZ": "REXP", "REXPZA": "REXP", "RGCFAT": "RGCF", "RGCFBA": "RGCF", "RGCFBE": "RGCF", "RGCFBG": "RGCF", "RGCFBY": "RGCF", "RGCFCH": "RGCF", "RGCFCO": "RGCF", "RGCFCY": "RGCF", "RGCFCZ": "RGCF", "RGCFDE": "RGCF", "RGCFDK": "RGCF", "RGCFEA": "RGCF", "RGCFEE": "RGCF", "RGCFES": "RGCF", "RGCFEU": "RGCF", "RGCFFI": "RGCF", "RGCFFR": "RGCF", "RGCFGR": "RGCF", "RGCFHR": "RGCF", "RGCFHU": "RGCF", "RGCFIE": "RGCF", "RGCFIT": "RGCF", "RGCFKR": "RGCF", "RGCFLT": "RGCF", "RGCFLU": "RGCF", "RGCFLV": "RGCF", "RGCFMK": "RGCF", "RGCFMT": "RGCF", "RGCFNL": "RGCF", "RGCFNO": "RGCF", "RGCFNZ": "RGCF", "RGCFPE": "RGCF", "RGCFPK": "RGCF", "RGCFPL": "RGCF", "RGCFPT": "RGCF", "RGCFRO": "RGCF", "RGCFRS": "RGCF", "RGCFRU": "RGCF", "RGCFRW": "RGCF", "RGCFSE": "RGCF", "RGCFSI": "RGCF", "RGCFSK": "RGCF", "RGCFUA": "RGCF", "RGCFUK": "RGCF", "RGDPAE": "RGDP", "RGDPAL": "RGDP", "RGDPAR": "RGDP", "RGDPAT": "RGDP", "RGDPAU": "RGDP", "RGDPAZ": "RGDP", "RGDPBA": "RGDP", "RGDPBD": "RGDP", "RGDPBE": "RGDP", "RGDPBG": "RGDP", "RGDPBR": "RGDP", "RGDPBW": "RGDP", "RGDPBY": "RGDP", "RGDPCA": "RGDP", "RGDPCH": "RGDP", "RGDPCL": "RGDP", "RGDPCM": "RGDP", "RGDPCN": "RGDP", "RGDPCO": "RGDP", "RGDPCR": "RGDP", "RGDPCY": "RGDP", "RGDPCZ": "RGDP", "RGDPDE": "RGDP", "RGDPDK": "RGDP", "RGDPEA": "RGDP", "RGDPEE": "RGDP", "RGDPEG": "RGDP", "RGDPES": "RGDP", "RGDPEU": "RGDP", "RGDPFI": "RGDP", "RGDPFR": "RGDP", "RGDPGR": "RGDP", "RGDPHK": "RGDP", "RGDPHN": "RGDP", "RGDPHR": "RGDP", "RGDPHU": "RGDP", "RGDPID": "RGDP", "RGDPIE": "RGDP", "RGDPIL": "RGDP", "RGDPIN": "RGDP", "RGDPIR": "RGDP", "RGDPIT": "RGDP", "RGDPJO": "RGDP", "RGDPJP": "RGDP", "RGDPKH": "RGDP", "RGDPKR": "RGDP", "RGDPKZ": "RGDP", "RGDPLA": "RGDP", "RGDPLT": "RGDP", "RGDPLU": "RGDP", "RGDPLV": "RGDP", "RGDPMA": "RGDP", "RGDPMK": "RGDP", "RGDPMN": "RGDP", "RGDPMO": "RGDP", "RGDPMT": "RGDP", "RGDPMX": "RGDP", "RGDPMY": "RGDP", "RGDPNG": "RGDP", "RGDPNL": "RGDP", "RGDPNO": "RGDP", "RGDPNZ": "RGDP", "RGDPPA": "RGDP", "RGDPPCAL": "RGDPPC", "RGDPPCAT": "RGDPPC", "RGDPPCAU": "RGDPPC", "RGDPPCBE": "RGDPPC", "RGDPPCBG": "RGDPPC", "RGDPPCBR": "RGDPPC", "RGDPPCCH": "RGDPPC", "RGDPPCCY": "RGDPPC", "RGDPPCCZ": "RGDPPC", "RGDPPCDE": "RGDPPC", "RGDPPCDK": "RGDPPC", "RGDPPCEA": "RGDPPC", "RGDPPCEE": "RGDPPC", "RGDPPCES": "RGDPPC", "RGDPPCEU": "RGDPPC", "RGDPPCFI": "RGDPPC", "RGDPPCFR": "RGDPPC", "RGDPPCGR": "RGDPPC", "RGDPPCHR": "RGDPPC", "RGDPPCHU": "RGDPPC", "RGDPPCIE": "RGDPPC", "RGDPPCIS": "RGDPPC", "RGDPPCIT": "RGDPPC", "RGDPPCLT": "RGDPPC", "RGDPPCLU": "RGDPPC", "RGDPPCLV": "RGDPPC", "RGDPPCME": "RGDPPC", "RGDPPCMK": "RGDPPC", "RGDPPCMT": "RGDPPC", "RGDPPCNL": "RGDPPC", "RGDPPCNO": "RGDPPC", "RGDPPCNZ": "RGDPPC", "RGDPPCPL": "RGDPPC", "RGDPPCPT": "RGDPPC", "RGDPPCRO": "RGDPPC", "RGDPPCRS": "RGDPPC", "RGDPPCSE": "RGDPPC", "RGDPPCSI": "RGDPPC", "RGDPPCSK": "RGDPPC", "RGDPPCTR": "RGDPPC", "RGDPPCUS": "RGDPPC", "RGDPPE": "RGDP", "RGDPPH": "RGDP", "RGDPPK": "RGDP", "RGDPPL": "RGDP", "RGDPPT": "RGDP", "RGDPQA": "RGDP", "RGDPRO": "RGDP", "RGDPRS": "RGDP", "RGDPRU": "RGDP", "RGDPRW": "RGDP", "RGDPSA": "RGDP", "RGDPSE": "RGDP", "RGDPSG": "RGDP", "RGDPSI": "RGDP", "RGDPSK": "RGDP", "RGDPTH": "RGDP", "RGDPTN": "RGDP", "RGDPTR": "RGDP", "RGDPTW": "RGDP", "RGDPUA": "RGDP", "RGDPUK": "RGDP", "RGDPUS": "RGDP", "RGDPUY": "RGDP", "RGDPUZ": "RGDP", "RGDPVN": "RGDP", "RGDPZA": "RGDP", "RGFCFAE": "RGFCF", "RGFCFAL": "RGFCF", "RGFCFAR": "RGFCF", "RGFCFAT": "RGFCF", "RGFCFAU": "RGFCF", "RGFCFAZ": "RGFCF", "RGFCFBA": "RGFCF", "RGFCFBD": "RGFCF", "RGFCFBE": "RGFCF", "RGFCFBG": "RGFCF", "RGFCFBR": "RGFCF", "RGFCFBW": "RGFCF", "RGFCFBY": "RGFCF", "RGFCFCA": "RGFCF", "RGFCFCH": "RGFCF", "RGFCFCL": "RGFCF", "RGFCFCN": "RGFCF", "RGFCFCO": "RGFCF", "RGFCFCR": "RGFCF", "RGFCFCY": "RGFCF", "RGFCFCZ": "RGFCF", "RGFCFDE": "RGFCF", "RGFCFDK": "RGFCF", "RGFCFEA": "RGFCF", "RGFCFEE": "RGFCF", "RGFCFES": "RGFCF", "RGFCFEU": "RGFCF", "RGFCFFI": "RGFCF", "RGFCFFR": "RGFCF", "RGFCFGR": "RGFCF", "RGFCFHK": "RGFCF", "RGFCFHN": "RGFCF", "RGFCFHR": "RGFCF", "RGFCFHU": "RGFCF", "RGFCFID": "RGFCF", "RGFCFIE": "RGFCF", "RGFCFIL": "RGFCF", "RGFCFIN": "RGFCF", "RGFCFIR": "RGFCF", "RGFCFIT": "RGFCF", "RGFCFJP": "RGFCF", "RGFCFKH": "RGFCF", "RGFCFKR": "RGFCF", "RGFCFLT": "RGFCF", "RGFCFLU": "RGFCF", "RGFCFLV": "RGFCF", "RGFCFMA": "RGFCF", "RGFCFMN": "RGFCF", "RGFCFMO": "RGFCF", "RGFCFMT": "RGFCF", "RGFCFMX": "RGFCF", "RGFCFMY": "RGFCF", "RGFCFNG": "RGFCF", "RGFCFNL": "RGFCF", "RGFCFNO": "RGFCF", "RGFCFNZ": "RGFCF", "RGFCFPH": "RGFCF", "RGFCFPK": "RGFCF", "RGFCFPL": "RGFCF", "RGFCFPT": "RGFCF", "RGFCFRO": "RGFCF", "RGFCFRS": "RGFCF", "RGFCFRU": "RGFCF", "RGFCFSA": "RGFCF", "RGFCFSE": "RGFCF", "RGFCFSG": "RGFCF", "RGFCFSI": "RGFCF", "RGFCFSK": "RGFCF", "RGFCFTH": "RGFCF", "RGFCFTR": "RGFCF", "RGFCFTW": "RGFCF", "RGFCFUA": "RGFCF", "RGFCFUK": "RGFCF", "RGFCFUS": "RGFCF", "RGFCFVN": "RGFCF", "RGFCFZA": "RGFCF", "RIMPAE": "RIMP", "RIMPAL": "RIMP", "RIMPAR": "RIMP", "RIMPAT": "RIMP", "RIMPAU": "RIMP", "RIMPAZ": "RIMP", "RIMPBA": "RIMP", "RIMPBD": "RIMP", "RIMPBE": "RIMP", "RIMPBG": "RIMP", "RIMPBR": "RIMP", "RIMPBW": "RIMP", "RIMPBY": "RIMP", "RIMPCA": "RIMP", "RIMPCH": "RIMP", "RIMPCL": "RIMP", "RIMPCN": "RIMP", "RIMPCO": "RIMP", "RIMPCY": "RIMP", "RIMPCZ": "RIMP", "RIMPDE": "RIMP", "RIMPDK": "RIMP", "RIMPEA": "RIMP", "RIMPEE": "RIMP", "RIMPES": "RIMP", "RIMPEU": "RIMP", "RIMPFI": "RIMP", "RIMPFR": "RIMP", "RIMPGR": "RIMP", "RIMPHK": "RIMP", "RIMPHN": "RIMP", "RIMPHR": "RIMP", "RIMPHU": "RIMP", "RIMPID": "RIMP", "RIMPIE": "RIMP", "RIMPIL": "RIMP", "RIMPIN": "RIMP", "RIMPIR": "RIMP", "RIMPIT": "RIMP", "RIMPJP": "RIMP", "RIMPKH": "RIMP", "RIMPKR": "RIMP", "RIMPLT": "RIMP", "RIMPLU": "RIMP", "RIMPLV": "RIMP", "RIMPMA": "RIMP", "RIMPMK": "RIMP", "RIMPMN": "RIMP", "RIMPMO": "RIMP", "RIMPMT": "RIMP", "RIMPMX": "RIMP", "RIMPMY": "RIMP", "RIMPNG": "RIMP", "RIMPNL": "RIMP", "RIMPNO": "RIMP", "RIMPNZ": "RIMP", "RIMPPA": "RIMP", "RIMPPE": "RIMP", "RIMPPH": "RIMP", "RIMPPK": "RIMP", "RIMPPL": "RIMP", "RIMPPT": "RIMP", "RIMPRO": "RIMP", "RIMPRS": "RIMP", "RIMPRU": "RIMP", "RIMPRW": "RIMP", "RIMPSA": "RIMP", "RIMPSE": "RIMP", "RIMPSG": "RIMP", "RIMPSI": "RIMP", "RIMPSK": "RIMP", "RIMPTH": "RIMP", "RIMPTR": "RIMP", "RIMPTW": "RIMP", "RIMPUA": "RIMP", "RIMPUK": "RIMP", "RIMPUS": "RIMP", "RIMPUZ": "RIMP", "RIMPZA": "RIMP", "RPRCAE": "RPRC", "RPRCAR": "RPRC", "RPRCAT": "RPRC", "RPRCAU": "RPRC", "RPRCAZ": "RPRC", "RPRCBA": "RPRC", "RPRCBD": "RPRC", "RPRCBE": "RPRC", "RPRCBG": "RPRC", "RPRCBR": "RPRC", "RPRCBY": "RPRC", "RPRCCA": "RPRC", "RPRCCH": "RPRC", "RPRCCL": "RPRC", "RPRCCN": "RPRC", "RPRCCO": "RPRC", "RPRCCR": "RPRC", "RPRCCY": "RPRC", "RPRCCZ": "RPRC", "RPRCDE": "RPRC", "RPRCDK": "RPRC", "RPRCEA": "RPRC", "RPRCEE": "RPRC", "RPRCES": "RPRC", "RPRCEU": "RPRC", "RPRCFI": "RPRC", "RPRCFR": "RPRC", "RPRCGR": "RPRC", "RPRCHK": "RPRC", "RPRCHN": "RPRC", "RPRCHR": "RPRC", "RPRCHU": "RPRC", "RPRCID": "RPRC", "RPRCIE": "RPRC", "RPRCIL": "RPRC", "RPRCIN": "RPRC", "RPRCIR": "RPRC", "RPRCIT": "RPRC", "RPRCJP": "RPRC", "RPRCKH": "RPRC", "RPRCKR": "RPRC", "RPRCLT": "RPRC", "RPRCLU": "RPRC", "RPRCLV": "RPRC", "RPRCMA": "RPRC", "RPRCMK": "RPRC", "RPRCMO": "RPRC", "RPRCMT": "RPRC", "RPRCMX": "RPRC", "RPRCMY": "RPRC", "RPRCNG": "RPRC", "RPRCNL": "RPRC", "RPRCNO": "RPRC", "RPRCNZ": "RPRC", "RPRCPE": "RPRC", "RPRCPH": "RPRC", "RPRCPK": "RPRC", "RPRCPL": "RPRC", "RPRCPT": "RPRC", "RPRCRO": "RPRC", "RPRCRS": "RPRC", "RPRCRU": "RPRC", "RPRCRW": "RPRC", "RPRCSA": "RPRC", "RPRCSE": "RPRC", "RPRCSG": "RPRC", "RPRCSI": "RPRC", "RPRCSK": "RPRC", "RPRCTH": "RPRC", "RPRCTR": "RPRC", "RPRCTW": "RPRC", "RPRCUA": "RPRC", "RPRCUK": "RPRC", "RPRCUS": "RPRC", "RPRCUZ": "RPRC", "RPRCVN": "RPRC", "RPRCZA": "RPRC", "RPUCAE": "RPUC", "RPUCAL": "RPUC", "RPUCAR": "RPUC", "RPUCAT": "RPUC", "RPUCAU": "RPUC", "RPUCAZ": "RPUC", "RPUCBA": "RPUC", "RPUCBD": "RPUC", "RPUCBE": "RPUC", "RPUCBG": "RPUC", "RPUCBR": "RPUC", "RPUCBW": "RPUC", "RPUCBY": "RPUC", "RPUCCA": "RPUC", "RPUCCH": "RPUC", "RPUCCL": "RPUC", "RPUCCN": "RPUC", "RPUCCO": "RPUC", "RPUCCR": "RPUC", "RPUCCY": "RPUC", "RPUCCZ": "RPUC", "RPUCDE": "RPUC", "RPUCDK": "RPUC", "RPUCEA": "RPUC", "RPUCEE": "RPUC", "RPUCES": "RPUC", "RPUCEU": "RPUC", "RPUCFI": "RPUC", "RPUCFR": "RPUC", "RPUCGR": "RPUC", "RPUCHK": "RPUC", "RPUCHN": "RPUC", "RPUCHR": "RPUC", "RPUCHU": "RPUC", "RPUCID": "RPUC", "RPUCIE": "RPUC", "RPUCIL": "RPUC", "RPUCIN": "RPUC", "RPUCIR": "RPUC", "RPUCIT": "RPUC", "RPUCJP": "RPUC", "RPUCKH": "RPUC", "RPUCKR": "RPUC", "RPUCLT": "RPUC", "RPUCLU": "RPUC", "RPUCLV": "RPUC", "RPUCMA": "RPUC", "RPUCMK": "RPUC", "RPUCMN": "RPUC", "RPUCMO": "RPUC", "RPUCMT": "RPUC", "RPUCMX": "RPUC", "RPUCMY": "RPUC", "RPUCNG": "RPUC", "RPUCNL": "RPUC", "RPUCNO": "RPUC", "RPUCNZ": "RPUC", "RPUCPA": "RPUC", "RPUCPE": "RPUC", "RPUCPH": "RPUC", "RPUCPK": "RPUC", "RPUCPL": "RPUC", "RPUCPT": "RPUC", "RPUCRO": "RPUC", "RPUCRS": "RPUC", "RPUCRU": "RPUC", "RPUCRW": "RPUC", "RPUCSA": "RPUC", "RPUCSE": "RPUC", "RPUCSG": "RPUC", "RPUCSI": "RPUC", "RPUCSK": "RPUC", "RPUCTH": "RPUC", "RPUCTR": "RPUC", "RPUCTW": "RPUC", "RPUCUA": "RPUC", "RPUCUK": "RPUC", "RPUCUS": "RPUC", "RPUCUZ": "RPUC", "RPUCVN": "RPUC", "RPUCZA": "RPUC", "SEIAE": "SEI", "SEIAT": "SEI", "SEIAU": "SEI", "SEIBD": "SEI", "SEIBE": "SEI", "SEIBR": "SEI", "SEICA": "SEI", "SEICH": "SEI", "SEICN": "SEI", "SEIDE": "SEI", "SEIDK": "SEI", "SEIES": "SEI", "SEIFI": "SEI", "SEIFR": "SEI", "SEIID": "SEI", "SEIIE": "SEI", "SEIIN": "SEI", "SEIIT": "SEI", "SEIJP": "SEI", "SEIKR": "SEI", "SEILA": "SEI", "SEILU": "SEI", "SEIMX": "SEI", "SEINL": "SEI", "SEINO": "SEI", "SEIOM": "SEI", "SEIPA": "SEI", "SEIPK": "SEI", "SEIPL": "SEI", "SEIRS": "SEI", "SEIRU": "SEI", "SEISE": "SEI", "SEITR": "SEI", "SEIUS": "SEI", "SEIUZ": "SEI", "SEIZA": "SEI", "SENTAL": "SENT", "SENTAT": "SENT", "SENTAU": "SENT", "SENTBE": "SENT", "SENTBG": "SENT", "SENTBR": "SENT", "SENTCY": "SENT", "SENTCZ": "SENT", "SENTDE": "SENT", "SENTDK": "SENT", "SENTEE": "SENT", "SENTES": "SENT", "SENTEU": "SENT", "SENTFI": "SENT", "SENTFR": "SENT", "SENTGR": "SENT", "SENTHR": "SENT", "SENTHU": "SENT", "SENTIE": "SENT", "SENTIT": "SENT", "SENTJP": "SENT", "SENTKR": "SENT", "SENTLT": "SENT", "SENTLU": "SENT", "SENTLV": "SENT", "SENTME": "SENT", "SENTMK": "SENT", "SENTMT": "SENT", "SENTNL": "SENT", "SENTPL": "SENT", "SENTPT": "SENT", "SENTRO": "SENT", "SENTRS": "SENT", "SENTSE": "SENT", "SENTSI": "SENT", "SENTSK": "SENT", "SENTTR": "SENT", "SENTUK": "SENT", "SENTUS": "SENT", "TBBE": "TB", "TBBG": "TB", "TBBR": "TB", "TBCA": "TB", "TBCZ": "TB", "TBDE": "TB", "TBDK": "TB", "TBEE": "TB", "TBEG": "TB", "TBFI": "TB", "TBFR": "TB", "TBGR": "TB", "TBHR": "TB", "TBHU": "TB", "TBIN": "TB", "TBIT": "TB", "TBLT": "TB", "TBLU": "TB", "TBLV": "TB", "TBMK": "TB", "TBMT": "TB", "TBMX": "TB", "TBNL": "TB", "TBPL": "TB", "TBPT": "TB", "TBRO": "TB", "TBRS": "TB", "TBSE": "TB", "TBSI": "TB", "TBSK": "TB", "TBTH": "TB", "TBUS": "TB", "TBVN": "TB", "URATEAE": "URATE", "URATEAR": "URATE", "URATEAT": "URATE", "URATEAU": "URATE", "URATEAZ": "URATE", "URATEBD": "URATE", "URATEBE": "URATE", "URATEBG": "URATE", "URATEBR": "URATE", "URATEBY": "URATE", "URATECA": "URATE", "URATECH": "URATE", "URATECL": "URATE", "URATECN": "URATE", "URATECO": "URATE", "URATECR": "URATE", "URATECY": "URATE", "URATECZ": "URATE", "URATEDE": "URATE", "URATEDK": "URATE", "URATEDO": "URATE", "URATEEA": "URATE", "URATEEE": "URATE", "URATEEG": "URATE", "URATEES": "URATE", "URATEEU": "URATE", "URATEFI": "URATE", "URATEFR": "URATE", "URATEGR": "URATE", "URATEHK": "URATE", "URATEHR": "URATE", "URATEHU": "URATE", "URATEID": "URATE", "URATEIE": "URATE", "URATEIL": "URATE", "URATEIS": "URATE", "URATEIT": "URATE", "URATEJO": "URATE", "URATEJP": "URATE", "URATEKH": "URATE", "URATEKR": "URATE", "URATEKZ": "URATE", "URATELK": "URATE", "URATELT": "URATE", "URATELU": "URATE", "URATELV": "URATE", "URATEMA": "URATE", "URATEMO": "URATE", "URATEMT": "URATE", "URATEMX": "URATE", "URATEMY": "URATE", "URATENL": "URATE", "URATENO": "URATE", "URATENZ": "URATE", "URATEPE": "URATE", "URATEPH": "URATE", "URATEPK": "URATE", "URATEPL": "URATE", "URATEPT": "URATE", "URATEQA": "URATE", "URATERO": "URATE", "URATERS": "URATE", "URATERU": "URATE", "URATESA": "URATE", "URATESE": "URATE", "URATESG": "URATE", "URATESI": "URATE", "URATESK": "URATE", "URATETH": "URATE", "URATETN": "URATE", "URATETR": "URATE", "URATETW": "URATE", "URATEUA": "URATE", "URATEUK": "URATE", "URATEUS": "URATE", "URATEUY": "URATE", "URATEVN": "URATE", "URATEZA": "URATE", "UTILAL": "UTIL", "UTILAR": "UTIL", "UTILAT": "UTIL", "UTILBE": "UTIL", "UTILBG": "UTIL", "UTILCA": "UTIL", "UTILCY": "UTIL", "UTILCZ": "UTIL", "UTILDE": "UTIL", "UTILDK": "UTIL", "UTILEE": "UTIL", "UTILES": "UTIL", "UTILEU": "UTIL", "UTILFI": "UTIL", "UTILFR": "UTIL", "UTILGR": "UTIL", "UTILHR": "UTIL", "UTILHU": "UTIL", "UTILIE": "UTIL", "UTILIT": "UTIL", "UTILLT": "UTIL", "UTILLU": "UTIL", "UTILLV": "UTIL", "UTILME": "UTIL", "UTILMK": "UTIL", "UTILMT": "UTIL", "UTILNL": "UTIL", "UTILPL": "UTIL", "UTILPT": "UTIL", "UTILRO": "UTIL", "UTILRS": "UTIL", "UTILSE": "UTIL", "UTILSI": "UTIL", "UTILSK": "UTIL", "UTILTR": "UTIL", "UTILUK": "UTIL", "UTILUS": "UTIL", "WAGEMANCL": "WAGEMAN", "WAGEMANMX": "WAGEMAN", "WAGEMANUS": "WAGEMAN", "WAGEUY": "WAGE", "WAGEVN": "WAGE", "Y10YDAR": "Y10YD", "Y10YDAT": "Y10YD", "Y10YDAU": "Y10YD", "Y10YDBD": "Y10YD", "Y10YDBE": "Y10YD", "Y10YDBG": "Y10YD", "Y10YDCA": "Y10YD", "Y10YDCN": "Y10YD", "Y10YDCY": "Y10YD", "Y10YDCZ": "Y10YD", "Y10YDDE": "Y10YD", "Y10YDDK": "Y10YD", "Y10YDEA": "Y10YD", "Y10YDEE": "Y10YD", "Y10YDES": "Y10YD", "Y10YDEU": "Y10YD", "Y10YDFI": "Y10YD", "Y10YDFR": "Y10YD", "Y10YDGR": "Y10YD", "Y10YDHK": "Y10YD", "Y10YDHR": "Y10YD", "Y10YDHU": "Y10YD", "Y10YDID": "Y10YD", "Y10YDIE": "Y10YD", "Y10YDIN": "Y10YD", "Y10YDIT": "Y10YD", "Y10YDJP": "Y10YD", "Y10YDKR": "Y10YD", "Y10YDLT": "Y10YD", "Y10YDLU": "Y10YD", "Y10YDLV": "Y10YD", "Y10YDMT": "Y10YD", "Y10YDMX": "Y10YD", "Y10YDMY": "Y10YD", "Y10YDNL": "Y10YD", "Y10YDNZ": "Y10YD", "Y10YDPH": "Y10YD", "Y10YDPL": "Y10YD", "Y10YDPT": "Y10YD", "Y10YDQA": "Y10YD", "Y10YDRO": "Y10YD", "Y10YDRU": "Y10YD", "Y10YDSA": "Y10YD", "Y10YDSE": "Y10YD", "Y10YDSG": "Y10YD", "Y10YDSI": "Y10YD", "Y10YDSK": "Y10YD", "Y10YDTH": "Y10YD", "Y10YDTR": "Y10YD", "Y10YDTW": "Y10YD", "Y10YDUA": "Y10YD", "Y10YDUK": "Y10YD", "Y10YDUS": "Y10YD", "Y10YDVN": "Y10YD", "Y10YDZA": "Y10YD"}